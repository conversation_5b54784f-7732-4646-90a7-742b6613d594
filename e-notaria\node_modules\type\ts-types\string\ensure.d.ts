import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureString(value: any, options?: EnsureBaseOptions): string;
declare function ensureString(value: any, options?: EnsureBaseOptions & EnsureIsOptional): string | null;
declare function ensureString(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<string>): string;

export default ensureString;

# Reporte de Arquitectura de Código - Dominio de Negocio
## Sistema SIGN+ v2 - Sistema de Gestión Notarial

### Información General
- **Sistema**: SIGN+ v2 (Sistema de Gestión Notarial)
- **Tecnología**: PHP 8.1, MySQL, JavaScript
- **Arquitectura**: Monolítica con patrón MVC
- **Framework**: Custom PHP con librerías especializadas

---

## 1. DOMINIO DE NEGOCIO PRINCIPAL

### 1.1 Contexto del Negocio
El sistema SIGN+ v2 es una plataforma integral para la gestión de servicios notariales que automatiza y digitaliza los procesos de una notaría, incluyendo:

- **Gestión documental notarial**
- **Firma digital y certificación electrónica**
- **Procesos administrativos y financieros**
- **Control de repertorios y registros**
- **Servicios de atención al cliente**

### 1.2 Entidades de Dominio Principales

#### **Usuario** (`entidades/usuario.php`)
- **Responsabilidad**: Gestión de autenticación, autorización y permisos
- **Atributos**: username, password, nombreSocial, estado
- **Funcionalidades**:
  - Autenticación JWT
  - Sistema RBAC (Role-Based Access Control)
  - Gestión de sesiones y tokens cifrados

#### **Orden de Trabajo** (`entidades/ordendeTrabajo.php`)
- **Responsabilidad**: Gestión del flujo de trabajo notarial
- **Atributos**: 
  - Datos principales: numero_ot, tipo_documento, fecha, materia
  - Cliente: rut_cliente, nombres_cliente, apellidos
  - Proceso: gestora, numero_wf, jefa_registro, ayudante
  - Repertorio: repertorio, fecha_repertorio, notario
  - UAF: direccion_flujo_UAF
- **Funcionalidades**:
  - Seguimiento de avances
  - Gestión de comparecientes
  - Control de repertorio

#### **Menu** (`entidades/menu.php`)
- **Responsabilidad**: Generación dinámica de menús basada en roles
- **Funcionalidades**:
  - Aplicación de restricciones RBAC
  - Generación de shortcuts de inicio
  - Configuración modular de accesos

---

## 2. MÓDULOS FUNCIONALES DEL SISTEMA

### 2.1 Módulos Principales (según `layouts/raf.json`)

#### **Módulo de Caja (BHE)**
- **Ubicación**: `app/caja_v2/`
- **Funcionalidad**: Emisión de Boletas de Honorarios Electrónicas
- **Roles**: CAJERO, FUNCIONARIO, JEFE_REGISTRO, REPERTORISTA, NOTARIO, ADMINISTRADOR

#### **Órdenes de Trabajo (OT)**
- **Ubicación**: `app/ot/`
- **Funcionalidad**: Creación, Modificación y Seguimiento de Órdenes de Trabajo
- **Tecnología**: Vue.js frontend + PHP backend
- **Roles**: CAJERO, FUNCIONARIO, JEFE_REGISTRO, REPERTORISTA, NOTARIO

#### **Documentos Privados**
- **Ubicación**: `app/documento_privado/`
- **Sub-módulos**:
  - **Documento Privado Local**: Solicitudes iniciadas en oficina
  - **Documento Privado Online**: Solicitudes web de notarial.cl
  - **Documento Privado Externo**: Integración con sistemas externos
  - **Notas de Cobro**: Generación express de notas de cobro

#### **Escrituras Públicas**
- **Ubicación**: `app/escrituras_publicas/`
- **Sub-módulos**:
  - **Copias de Escrituras**: Emisión basada en número de repertorio
  - **Extractos**: Extractos de escrituras públicas
  - **Repertorio**: Gestión de repertorio de escrituras
  - **Borradores y Certificados**

#### **Vehículos**
- **Ubicación**: `app/vehiculos/`
- **Sub-módulos**:
  - **STEV**: Servicio de Transferencia Electrónica de Vehículos
  - **Repertorio de Vehículos**: Gestión de repertorio vehicular

#### **Letras**
- **Ubicación**: `app/letras/`
- **Funcionalidades**:
  - Ingreso y gestión de letras
  - Generación de citaciones
  - Informes de protestos
  - Boletín comercial

#### **Firma Digital**
- **Ubicación**: `app/firma_digital/`
- **Funcionalidades**:
  - Gestión de certificados digitales
  - Proceso de firma electrónica
  - Bloqueo de documentos
  - Verificación de documentos firmados

---

## 3. ARQUITECTURA DE CAPAS

### 3.1 Capa de Presentación
- **Frontend**: HTML5, CSS3, JavaScript, Vue.js (módulos específicos)
- **Templates**: Sistema de layouts modulares (`layouts/`)
- **Assets**: Recursos estáticos (`assets/`)

### 3.2 Capa de Lógica de Negocio
- **Controladores**: Archivos index.php en cada módulo
- **Servicios**: Clases especializadas en `calls/lib_*.php`
- **APIs**: Endpoints REST en `api/` de cada módulo

### 3.3 Capa de Datos
- **Entidades**: Clases de dominio en `entidades/`
- **Acceso a Datos**: Clase `ingreso` para conexiones MySQL
- **Base de Datos**: MySQL con múltiples esquemas especializados

---

## 4. PATRONES ARQUITECTÓNICOS

### 4.1 Patrón MVC
- **Modelo**: Entidades de dominio y clases de acceso a datos
- **Vista**: Templates HTML con PHP embebido
- **Controlador**: Scripts de procesamiento y APIs

### 4.2 Patrón Repository
- Implementado a través de clases especializadas (`lib_*.php`)
- Separación de lógica de acceso a datos

### 4.3 Patrón Factory
- Utilizado en la generación de menús dinámicos
- Creación de objetos basada en configuración JSON

---

## 5. SISTEMA DE SEGURIDAD Y PERMISOS

### 5.1 RBAC (Role-Based Access Control)
- **Librería**: PhpRbac (`library/PhpRbac/`)
- **Roles Definidos**:
  - ADMINISTRADOR
  - NOTARIO
  - REPERTORISTA
  - JEFE_REGISTRO
  - FUNCIONARIO
  - CAJERO

### 5.2 Autenticación
- **JWT Tokens**: Implementación custom (`library/jwt/`)
- **Cifrado**: AES-128-CTR para tokens
- **Sesiones**: Gestión de sesiones PHP con validación JWT

### 5.3 Autorización
- Control granular por módulo y funcionalidad
- Configuración declarativa en `layouts/raf.json`
- Validación en tiempo de ejecución

---

## 6. INTEGRACIONES EXTERNAS

### 6.1 Servicios Notariales
- **Notarial.cl**: Integración para documentos privados externos
- **STEV**: Servicio de Transferencia Electrónica de Vehículos
- **Webservices**: Comunicación SOAP/REST con entidades externas

### 6.2 Servicios de Firma Digital
- **Certificados PEM**: Gestión de certificados digitales
- **Firma Electrónica**: Proceso de firma con validación criptográfica
- **Timestamping**: Sellado de tiempo para documentos

### 6.3 Servicios Financieros
- **Webpay**: Integración para pagos electrónicos
- **Bancos**: Conectividad con sistemas bancarios
- **Boletas Electrónicas**: Emisión de documentos tributarios

---

## 7. GESTIÓN DE DOCUMENTOS

### 7.1 Tipos de Documentos
- **Escrituras Públicas**: Documentos notariales principales
- **Documentos Privados**: Contratos y acuerdos privados
- **Certificados**: Documentos de certificación
- **Copias**: Reproducciones autenticadas
- **Extractos**: Resúmenes de documentos principales

### 7.2 Flujo de Procesamiento
1. **Ingreso**: Recepción de solicitud
2. **Validación**: Verificación de datos y documentos
3. **Procesamiento**: Generación del documento
4. **Firma**: Proceso de firma digital
5. **Entrega**: Distribución al cliente

---

## 8. TECNOLOGÍAS Y LIBRERÍAS

### 8.1 Backend
- **PHP 8.1**: Lenguaje principal
- **MySQL**: Base de datos relacional
- **Composer**: Gestión de dependencias
- **FPDF/TCPDF**: Generación de PDFs

### 8.2 Frontend
- **Vue.js**: Framework reactivo para módulos específicos
- **jQuery**: Manipulación DOM y AJAX
- **Bootstrap**: Framework CSS
- **TinyAjax**: Librería AJAX custom

### 8.3 Librerías Especializadas
- **PhpRbac**: Control de acceso basado en roles
- **JWT**: Autenticación por tokens
- **Sentry**: Monitoreo de errores
- **HTML2PDF**: Conversión HTML a PDF

---

## 9. CONCLUSIONES Y RECOMENDACIONES

### 9.1 Fortalezas Arquitectónicas
- **Modularidad**: Sistema bien dividido en módulos funcionales
- **Seguridad**: Implementación robusta de RBAC y JWT
- **Escalabilidad**: Arquitectura permite agregar nuevos módulos
- **Especialización**: Cada módulo maneja un dominio específico

### 9.2 Áreas de Mejora
- **Modernización**: Migración a frameworks modernos (Laravel, Symfony)
- **API First**: Implementación de APIs RESTful consistentes
- **Testing**: Implementación de pruebas automatizadas
- **Documentación**: Mejora en documentación técnica

### 9.3 Recomendaciones Técnicas
- Implementar patrón de inyección de dependencias
- Adoptar PSR standards para PHP
- Implementar cache distribuido (Redis)
- Migrar a contenedores (Docker)
- Implementar CI/CD pipeline

---

**Fecha del Reporte**: 12 de Julio, 2025  
**Versión del Sistema**: SIGN+ v2  
**Analista**: Augment Agent

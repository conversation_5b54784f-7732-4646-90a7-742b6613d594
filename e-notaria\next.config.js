/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuración experimental para Next.js 15
  experimental: {
    // Habilitar Server Actions
    serverActions: {
      allowedOrigins: ['localhost:3000', '***************:3000'],
      bodySizeLimit: '10mb'
    }
  },

  // Configuración para desarrollo en red local
  allowedDevOrigins: ['***************:3000'],

  // Configuración de TypeScript
  typescript: {
    ignoreBuildErrors: false
  },

  // Configuración de ESLint
  eslint: {
    ignoreDuringBuilds: false
  },

  // Headers de seguridad básicos
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ]
  },

  // Configuración de rewrites para API legacy
  async rewrites() {
    return [
      {
        source: '/api/legacy/:path*',
        destination: 'http://localhost/SIGNv2-Main/app/:path*'
      }
    ]
  },

  // Configuración de compresión
  compress: true,

  // Configuración de powered by header
  poweredByHeader: false
}

module.exports = nextConfig

{"name": "ratchet/rfc6455", "type": "library", "description": "RFC6455 WebSocket protocol handler", "keywords": ["WebSockets", "websocket", "RFC6455"], "homepage": "http://socketo.me", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "role": "Developer"}], "support": {"issues": "https://github.com/ratchetphp/RFC6455/issues", "chat": "https://gitter.im/reactphp/reactphp"}, "autoload": {"psr-4": {"Ratchet\\RFC6455\\": "src"}}, "require": {"php": ">=5.4.2", "guzzlehttp/psr7": "^2 || ^1.7"}, "require-dev": {"phpunit/phpunit": "^5.7", "react/socket": "^1.3"}, "scripts": {"abtest-client": "ABTEST=client && sh tests/ab/run_ab_tests.sh", "abtest-server": "ABTEST=server && sh tests/ab/run_ab_tests.sh", "phpunit": "phpunit --colors=always", "test": ["@abtest-client", "@abtest-server", "@phpunit"]}}
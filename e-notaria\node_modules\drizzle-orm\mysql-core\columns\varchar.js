import { entityKind } from "../../entity.js";
import { getColumnNameAndConfig } from "../../utils.js";
import { MySqlColumn, MySqlColumnBuilder } from "./common.js";
class MySqlVarCharBuilder extends MySqlColumnBuilder {
  static [entityKind] = "MySqlVarCharBuilder";
  /** @internal */
  constructor(name, config) {
    super(name, "string", "MySqlVarChar");
    this.config.length = config.length;
    this.config.enum = config.enum;
  }
  /** @internal */
  build(table) {
    return new MySqlVarChar(
      table,
      this.config
    );
  }
}
class MySqlVarChar extends MySqlColumn {
  static [entityKind] = "MySqlVarChar";
  length = this.config.length;
  enumValues = this.config.enum;
  getSQLType() {
    return this.length === void 0 ? `varchar` : `varchar(${this.length})`;
  }
}
function varchar(a, b) {
  const { name, config } = getColumnNameAndConfig(a, b);
  return new MySqlVarCharBuilder(name, config);
}
export {
  MySqlVarChar,
  MySqlVarCharBuilder,
  varchar
};
//# sourceMappingURL=varchar.js.map
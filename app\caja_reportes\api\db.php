<?php
require_once("../../../calls/lib.php");
class DB {

    public static function connect()
    {   
        $conect           = new ingreso;
        $database_link    = $conect->Conectarse();
        return $database_link;   
    }

    public static function query($qry,$accion)
    {
        $database_link = DB::connect();
        mysqli_set_charset($database_link, "utf8mb4");
        $returnArray = array();
        if($accion == 'I')
        {
            $result = mysqli_query($database_link, $qry) or die("Error: ". mysqli_error($database_link)." ".$qry);
            if($result)
            {
                $returnArray['estado']      = true;
                $returnArray['id_insert']   = mysqli_insert_id($database_link);
            }else{
                $returnArray['estado']      = false;
                $returnArray['id_insert']   = '';
            }
            return $returnArray;
        }else if($accion == 'S'){
            $result = mysqli_query($database_link, $qry) or die("Error: ". mysqli_error($database_link)." ".$qry);
            $i=0;
            while ($row = mysqli_fetch_array($result))
                if ($row)
            $returnArray[$i++]=$row;
            mysqli_free_result($result);
            return $returnArray;
        }else{
            $result = mysqli_query($database_link, $qry) or die("Error: ". mysqli_error($database_link)." ".$qry);
            if($result)
            {
                $returnArray['estado']      = true;
            }else{
                $returnArray['estado']      = false;
            }
            return $returnArray;
        }
        mysqli_close($database_link);
    }

    public static function fechaHora($tipo)
    {
        $serverTimeZone = date_default_timezone_get();
        $userTimeZone 	= "America/Santiago";
        $date = '';
        $dateTime 		= new DateTime ($date, new DateTimeZone($serverTimeZone));
        $dateTime->setTimezone(new DateTimeZone($userTimeZone));
        $hoy 			= $dateTime->format("Y-m-d H:i:s");
        $fecha 			= $dateTime->format("Y-m-d");
        $hora 			= $dateTime->format("H:i:s");
        if($tipo == 'fechahora')
        {
            return $hoy;
        }else if($tipo == 'fecha'){
            return $fecha;
        }else if($tipo == 'hora'){
            return $hora;
        }

        
    }

    public static function insertmysql($parametro)
    {
        $database_link  = DB::connect();
        $parametro = $database_link->real_escape_string($parametro ?? '');
        return utf8_decode(addslashes($parametro));
    }

    public static function selectmysql($parametro)
    {
        $database_link = DB::connect();
        return stripcslashes(utf8_encode(trim($parametro ?? '')));
    }

    public static function traeTitular()
    {
        $datos              = new datos;
        return strtoupper($datos->conservador());
    }

    public static function traeIdFojas()
    {
        $datos              = new datos;
        return strtoupper($datos->web_fojas());
    }

    public static function traeUsuario($id)
    {
        $nombre        = array();
        $sql_cliente   = "SELECT * FROM usuarios_not WHERE id = '".$id."' AND estado = '1'";
        $row_usuario   = DB::query($sql_cliente,'S');
        if(!empty($row_usuario[0]['id']))
        {
            $nombre['nombre']   = strtoupper($row_usuario[0]['nombreSocial']);
            $nombre['username'] = strtoupper($row_usuario[0]['nombre']);
        }
        return $nombre;
    }

    public static function sanitizar($input)
    {
        $database_link  = DB::connect();
        //$parametro = $database_link->real_escape_string($parametro ?? '');

        if (is_array($input)) {
            foreach($input as $var=>$val) {
                $output[$var] = sanitize($val);
            }
        }
        else {
            $input  = DB::cleanInput($input);
            $output = $database_link->real_escape_string($input);
        }
        return $output;
    }

    private static function cleanInput($input) 
    {
        $search = array(
          '@<script[^>]*?>.*?</script>@si',   // Elimina javascript
          '@<[\/\!]*?[^<>]*?>@si',            // Elimina las etiquetas HTML
          '@<style[^>]*?>.*?</style>@siU',    // Elimina las etiquetas de estilo
          '@<![\s\S]*?--[ \t\n\r]*>@'         // Elimina los comentarios multi-línea
        );
       
          $output = preg_replace($search, '', $input);
          return $output;
    }

}




?>
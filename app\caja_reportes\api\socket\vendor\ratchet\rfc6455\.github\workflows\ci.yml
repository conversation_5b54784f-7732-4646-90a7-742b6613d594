name: CI

on:
  push:
  pull_request:

jobs:
  PHPUnit:
    name: PHPUnit (PHP ${{ matrix.php }})(${{ matrix.env }})
    runs-on: ubuntu-20.04
    strategy:
      matrix:
        env:
          - client
          - server
        php:
          - 7.4
          - 7.3
          - 7.2
          - 7.1
          - 7.0
          - 5.6
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          coverage: xdebug
      - run: docker pull crossbario/autobahn-testsuite
      - run: composer install

      - run: sh tests/ab/run_ab_tests.sh
        env:
            ABTEST: ${{ matrix.env }}
            SKIP_DEFLATE: _skip_deflate
        if: ${{ matrix.php <= 5.6 }}

      - run: sh tests/ab/run_ab_tests.sh
        env:
            ABTEST: ${{ matrix.env }}
        if: ${{ matrix.php >= 7.0 }}
      - run: vendor/bin/phpunit --verbose

(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function Zo(e,t){const n=Object.create(null),r=e.split(",");for(let s=0;s<r.length;s++)n[r[s]]=!0;return t?s=>!!n[s.toLowerCase()]:s=>!!n[s]}const Fe={},Er=[],zt=()=>{},Ov=()=>!1,Iv=/^on[^a-z]/,Yi=e=>Iv.test(e),Yo=e=>e.startsWith("onUpdate:"),ze=Object.assign,Go=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Av=Object.prototype.hasOwnProperty,_e=(e,t)=>Av.call(e,t),ie=Array.isArray,Tr=e=>Gi(e)==="[object Map]",yd=e=>Gi(e)==="[object Set]",me=e=>typeof e=="function",$e=e=>typeof e=="string",Ko=e=>typeof e=="symbol",Pe=e=>e!==null&&typeof e=="object",pd=e=>Pe(e)&&me(e.then)&&me(e.catch),bd=Object.prototype.toString,Gi=e=>bd.call(e),Vv=e=>Gi(e).slice(8,-1),wd=e=>Gi(e)==="[object Object]",Jo=e=>$e(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,hi=Zo(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ki=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pv=/-(\w)/g,It=Ki(e=>e.replace(Pv,(t,n)=>n?n.toUpperCase():"")),Mv=/\B([A-Z])/g,Nr=Ki(e=>e.replace(Mv,"-$1").toLowerCase()),vn=Ki(e=>e.charAt(0).toUpperCase()+e.slice(1)),Aa=Ki(e=>e?`on${vn(e)}`:""),vs=(e,t)=>!Object.is(e,t),Va=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Ti=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Fv=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Dv=e=>{const t=$e(e)?Number(e):NaN;return isNaN(t)?e:t};let nu;const io=()=>nu||(nu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Xo(e){if(ie(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=$e(r)?$v(r):Xo(r);if(s)for(const i in s)t[i]=s[i]}return t}else{if($e(e))return e;if(Pe(e))return e}}const Nv=/;(?![^(]*\))/g,Lv=/:([^]+)/,Rv=/\/\*[^]*?\*\//g;function $v(e){const t={};return e.replace(Rv,"").split(Nv).forEach(n=>{if(n){const r=n.split(Lv);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Qo(e){let t="";if($e(e))t=e;else if(ie(e))for(let n=0;n<e.length;n++){const r=Qo(e[n]);r&&(t+=r+" ")}else if(Pe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Bv="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Hv=Zo(Bv);function Sd(e){return!!e||e===""}const Se=e=>$e(e)?e:e==null?"":ie(e)||Pe(e)&&(e.toString===bd||!me(e.toString))?JSON.stringify(e,Cd,2):String(e),Cd=(e,t)=>t&&t.__v_isRef?Cd(e,t.value):Tr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s])=>(n[`${r} =>`]=s,n),{})}:yd(t)?{[`Set(${t.size})`]:[...t.values()]}:Pe(t)&&!ie(t)&&!wd(t)?String(t):t;let vt;class xd{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=vt,!t&&vt&&(this.index=(vt.scopes||(vt.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=vt;try{return vt=this,t()}finally{vt=n}}}on(){vt=this}off(){vt=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function Vs(e){return new xd(e)}function Uv(e,t=vt){t&&t.active&&t.effects.push(e)}function _d(){return vt}function lt(e){vt&&vt.cleanups.push(e)}const el=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Ed=e=>(e.w&Nn)>0,Td=e=>(e.n&Nn)>0,zv=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Nn},jv=e=>{const{deps:t}=e;if(t.length){let n=0;for(let r=0;r<t.length;r++){const s=t[r];Ed(s)&&!Td(s)?s.delete(e):t[n++]=s,s.w&=~Nn,s.n&=~Nn}t.length=n}},ki=new WeakMap;let rs=0,Nn=1;const ao=30;let Bt;const sr=Symbol(""),oo=Symbol("");class tl{constructor(t,n=null,r){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,Uv(this,r)}run(){if(!this.active)return this.fn();let t=Bt,n=Mn;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=Bt,Bt=this,Mn=!0,Nn=1<<++rs,rs<=ao?zv(this):ru(this),this.fn()}finally{rs<=ao&&jv(this),Nn=1<<--rs,Bt=this.parent,Mn=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Bt===this?this.deferStop=!0:this.active&&(ru(this),this.onStop&&this.onStop(),this.active=!1)}}function ru(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Mn=!0;const kd=[];function Lr(){kd.push(Mn),Mn=!1}function Rr(){const e=kd.pop();Mn=e===void 0?!0:e}function mt(e,t,n){if(Mn&&Bt){let r=ki.get(e);r||ki.set(e,r=new Map);let s=r.get(n);s||r.set(n,s=el()),Od(s)}}function Od(e,t){let n=!1;rs<=ao?Td(e)||(e.n|=Nn,n=!Ed(e)):n=!e.has(Bt),n&&(e.add(Bt),Bt.deps.push(e))}function cn(e,t,n,r,s,i){const a=ki.get(e);if(!a)return;let o=[];if(t==="clear")o=[...a.values()];else if(n==="length"&&ie(e)){const l=Number(r);a.forEach((u,c)=>{(c==="length"||c>=l)&&o.push(u)})}else switch(n!==void 0&&o.push(a.get(n)),t){case"add":ie(e)?Jo(n)&&o.push(a.get("length")):(o.push(a.get(sr)),Tr(e)&&o.push(a.get(oo)));break;case"delete":ie(e)||(o.push(a.get(sr)),Tr(e)&&o.push(a.get(oo)));break;case"set":Tr(e)&&o.push(a.get(sr));break}if(o.length===1)o[0]&&lo(o[0]);else{const l=[];for(const u of o)u&&l.push(...u);lo(el(l))}}function lo(e,t){const n=ie(e)?e:[...e];for(const r of n)r.computed&&su(r);for(const r of n)r.computed||su(r)}function su(e,t){(e!==Bt||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}function Wv(e,t){var n;return(n=ki.get(e))==null?void 0:n.get(t)}const qv=Zo("__proto__,__v_isRef,__isVue"),Id=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ko)),Zv=nl(),Yv=nl(!1,!0),Gv=nl(!0),iu=Kv();function Kv(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=ce(this);for(let i=0,a=this.length;i<a;i++)mt(r,"get",i+"");const s=r[t](...n);return s===-1||s===!1?r[t](...n.map(ce)):s}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Lr();const r=ce(this)[t].apply(this,n);return Rr(),r}}),e}function Jv(e){const t=ce(this);return mt(t,"has",e),t.hasOwnProperty(e)}function nl(e=!1,t=!1){return function(r,s,i){if(s==="__v_isReactive")return!e;if(s==="__v_isReadonly")return e;if(s==="__v_isShallow")return t;if(s==="__v_raw"&&i===(e?t?mg:Fd:t?Md:Pd).get(r))return r;const a=ie(r);if(!e){if(a&&_e(iu,s))return Reflect.get(iu,s,i);if(s==="hasOwnProperty")return Jv}const o=Reflect.get(r,s,i);return(Ko(s)?Id.has(s):qv(s))||(e||mt(r,"get",s),t)?o:Oe(o)?a&&Jo(s)?o:o.value:Pe(o)?e?Ps(o):nt(o):o}}const Xv=Ad(),Qv=Ad(!0);function Ad(e=!1){return function(n,r,s,i){let a=n[r];if(Ar(a)&&Oe(a)&&!Oe(s))return!1;if(!e&&(!Oi(s)&&!Ar(s)&&(a=ce(a),s=ce(s)),!ie(n)&&Oe(a)&&!Oe(s)))return a.value=s,!0;const o=ie(n)&&Jo(r)?Number(r)<n.length:_e(n,r),l=Reflect.set(n,r,s,i);return n===ce(i)&&(o?vs(s,a)&&cn(n,"set",r,s):cn(n,"add",r,s)),l}}function eg(e,t){const n=_e(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&cn(e,"delete",t,void 0),r}function tg(e,t){const n=Reflect.has(e,t);return(!Ko(t)||!Id.has(t))&&mt(e,"has",t),n}function ng(e){return mt(e,"iterate",ie(e)?"length":sr),Reflect.ownKeys(e)}const Vd={get:Zv,set:Xv,deleteProperty:eg,has:tg,ownKeys:ng},rg={get:Gv,set(e,t){return!0},deleteProperty(e,t){return!0}},sg=ze({},Vd,{get:Yv,set:Qv}),rl=e=>e,Ji=e=>Reflect.getPrototypeOf(e);function Js(e,t,n=!1,r=!1){e=e.__v_raw;const s=ce(e),i=ce(t);n||(t!==i&&mt(s,"get",t),mt(s,"get",i));const{has:a}=Ji(s),o=r?rl:n?al:gs;if(a.call(s,t))return o(e.get(t));if(a.call(s,i))return o(e.get(i));e!==s&&e.get(t)}function Xs(e,t=!1){const n=this.__v_raw,r=ce(n),s=ce(e);return t||(e!==s&&mt(r,"has",e),mt(r,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function Qs(e,t=!1){return e=e.__v_raw,!t&&mt(ce(e),"iterate",sr),Reflect.get(e,"size",e)}function au(e){e=ce(e);const t=ce(this);return Ji(t).has.call(t,e)||(t.add(e),cn(t,"add",e,e)),this}function ou(e,t){t=ce(t);const n=ce(this),{has:r,get:s}=Ji(n);let i=r.call(n,e);i||(e=ce(e),i=r.call(n,e));const a=s.call(n,e);return n.set(e,t),i?vs(t,a)&&cn(n,"set",e,t):cn(n,"add",e,t),this}function lu(e){const t=ce(this),{has:n,get:r}=Ji(t);let s=n.call(t,e);s||(e=ce(e),s=n.call(t,e)),r&&r.call(t,e);const i=t.delete(e);return s&&cn(t,"delete",e,void 0),i}function uu(){const e=ce(this),t=e.size!==0,n=e.clear();return t&&cn(e,"clear",void 0,void 0),n}function ei(e,t){return function(r,s){const i=this,a=i.__v_raw,o=ce(a),l=t?rl:e?al:gs;return!e&&mt(o,"iterate",sr),a.forEach((u,c)=>r.call(s,l(u),l(c),i))}}function ti(e,t,n){return function(...r){const s=this.__v_raw,i=ce(s),a=Tr(i),o=e==="entries"||e===Symbol.iterator&&a,l=e==="keys"&&a,u=s[e](...r),c=n?rl:t?al:gs;return!t&&mt(i,"iterate",l?oo:sr),{next(){const{value:d,done:f}=u.next();return f?{value:d,done:f}:{value:o?[c(d[0]),c(d[1])]:c(d),done:f}},[Symbol.iterator](){return this}}}}function Cn(e){return function(...t){return e==="delete"?!1:this}}function ig(){const e={get(i){return Js(this,i)},get size(){return Qs(this)},has:Xs,add:au,set:ou,delete:lu,clear:uu,forEach:ei(!1,!1)},t={get(i){return Js(this,i,!1,!0)},get size(){return Qs(this)},has:Xs,add:au,set:ou,delete:lu,clear:uu,forEach:ei(!1,!0)},n={get(i){return Js(this,i,!0)},get size(){return Qs(this,!0)},has(i){return Xs.call(this,i,!0)},add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear"),forEach:ei(!0,!1)},r={get(i){return Js(this,i,!0,!0)},get size(){return Qs(this,!0)},has(i){return Xs.call(this,i,!0)},add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear"),forEach:ei(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=ti(i,!1,!1),n[i]=ti(i,!0,!1),t[i]=ti(i,!1,!0),r[i]=ti(i,!0,!0)}),[e,n,t,r]}const[ag,og,lg,ug]=ig();function sl(e,t){const n=t?e?ug:lg:e?og:ag;return(r,s,i)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(_e(n,s)&&s in r?n:r,s,i)}const cg={get:sl(!1,!1)},dg={get:sl(!1,!0)},fg={get:sl(!0,!1)},Pd=new WeakMap,Md=new WeakMap,Fd=new WeakMap,mg=new WeakMap;function hg(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function vg(e){return e.__v_skip||!Object.isExtensible(e)?0:hg(Vv(e))}function nt(e){return Ar(e)?e:il(e,!1,Vd,cg,Pd)}function gg(e){return il(e,!1,sg,dg,Md)}function Ps(e){return il(e,!0,rg,fg,Fd)}function il(e,t,n,r,s){if(!Pe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=s.get(e);if(i)return i;const a=vg(e);if(a===0)return e;const o=new Proxy(e,a===2?r:n);return s.set(e,o),o}function ln(e){return Ar(e)?ln(e.__v_raw):!!(e&&e.__v_isReactive)}function Ar(e){return!!(e&&e.__v_isReadonly)}function Oi(e){return!!(e&&e.__v_isShallow)}function Dd(e){return ln(e)||Ar(e)}function ce(e){const t=e&&e.__v_raw;return t?ce(t):e}function Xi(e){return Ti(e,"__v_skip",!0),e}const gs=e=>Pe(e)?nt(e):e,al=e=>Pe(e)?Ps(e):e;function Nd(e){Mn&&Bt&&(e=ce(e),Od(e.dep||(e.dep=el())))}function Ld(e,t){e=ce(e);const n=e.dep;n&&lo(n)}function Oe(e){return!!(e&&e.__v_isRef===!0)}function K(e){return Rd(e,!1)}function be(e){return Rd(e,!0)}function Rd(e,t){return Oe(e)?e:new yg(e,t)}class yg{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:ce(t),this._value=n?t:gs(t)}get value(){return Nd(this),this._value}set value(t){const n=this.__v_isShallow||Oi(t)||Ar(t);t=n?t:ce(t),vs(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:gs(t),Ld(this))}}function tt(e){return Oe(e)?e.value:e}const pg={get:(e,t,n)=>tt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Oe(s)&&!Oe(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function $d(e){return ln(e)?e:new Proxy(e,pg)}function Qi(e){const t=ie(e)?new Array(e.length):{};for(const n in e)t[n]=Bd(e,n);return t}class bg{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Wv(ce(this._object),this._key)}}class wg{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function de(e,t,n){return Oe(e)?e:me(e)?new wg(e):Pe(e)&&arguments.length>1?Bd(e,t,n):K(e)}function Bd(e,t,n){const r=e[t];return Oe(r)?r:new bg(e,t,n)}class Sg{constructor(t,n,r,s){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new tl(t,()=>{this._dirty||(this._dirty=!0,Ld(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=r}get value(){const t=ce(this);return Nd(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function Cg(e,t,n=!1){let r,s;const i=me(e);return i?(r=e,s=zt):(r=e.get,s=e.set),new Sg(r,s,i||!s,n)}function Fn(e,t,n,r){let s;try{s=r?e(...r):e()}catch(i){ea(i,t,n)}return s}function kt(e,t,n,r){if(me(e)){const i=Fn(e,t,n,r);return i&&pd(i)&&i.catch(a=>{ea(a,t,n)}),i}const s=[];for(let i=0;i<e.length;i++)s.push(kt(e[i],t,n,r));return s}function ea(e,t,n,r=!0){const s=t?t.vnode:null;if(t){let i=t.parent;const a=t.proxy,o=n;for(;i;){const u=i.ec;if(u){for(let c=0;c<u.length;c++)if(u[c](e,a,o)===!1)return}i=i.parent}const l=t.appContext.config.errorHandler;if(l){Fn(l,null,10,[e,a,o]);return}}xg(e,n,s,r)}function xg(e,t,n,r=!0){console.error(e)}let ys=!1,uo=!1;const rt=[];let Kt=0;const kr=[];let sn=null,Jn=0;const Hd=Promise.resolve();let ol=null;function ut(e){const t=ol||Hd;return e?t.then(this?e.bind(this):e):t}function _g(e){let t=Kt+1,n=rt.length;for(;t<n;){const r=t+n>>>1;ps(rt[r])<e?t=r+1:n=r}return t}function ll(e){(!rt.length||!rt.includes(e,ys&&e.allowRecurse?Kt+1:Kt))&&(e.id==null?rt.push(e):rt.splice(_g(e.id),0,e),Ud())}function Ud(){!ys&&!uo&&(uo=!0,ol=Hd.then(jd))}function Eg(e){const t=rt.indexOf(e);t>Kt&&rt.splice(t,1)}function Tg(e){ie(e)?kr.push(...e):(!sn||!sn.includes(e,e.allowRecurse?Jn+1:Jn))&&kr.push(e),Ud()}function cu(e,t=ys?Kt+1:0){for(;t<rt.length;t++){const n=rt[t];n&&n.pre&&(rt.splice(t,1),t--,n())}}function zd(e){if(kr.length){const t=[...new Set(kr)];if(kr.length=0,sn){sn.push(...t);return}for(sn=t,sn.sort((n,r)=>ps(n)-ps(r)),Jn=0;Jn<sn.length;Jn++)sn[Jn]();sn=null,Jn=0}}const ps=e=>e.id==null?1/0:e.id,kg=(e,t)=>{const n=ps(e)-ps(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function jd(e){uo=!1,ys=!0,rt.sort(kg);const t=zt;try{for(Kt=0;Kt<rt.length;Kt++){const n=rt[Kt];n&&n.active!==!1&&Fn(n,null,14)}}finally{Kt=0,rt.length=0,zd(),ys=!1,ol=null,(rt.length||kr.length)&&jd()}}function Og(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Fe;let s=n;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in r){const c=`${a==="modelValue"?"model":a}Modifiers`,{number:d,trim:f}=r[c]||Fe;f&&(s=n.map(m=>$e(m)?m.trim():m)),d&&(s=n.map(Fv))}let o,l=r[o=Aa(t)]||r[o=Aa(It(t))];!l&&i&&(l=r[o=Aa(Nr(t))]),l&&kt(l,e,6,s);const u=r[o+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[o])return;e.emitted[o]=!0,kt(u,e,6,s)}}function Wd(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const i=e.emits;let a={},o=!1;if(!me(e)){const l=u=>{const c=Wd(u,t,!0);c&&(o=!0,ze(a,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!o?(Pe(e)&&r.set(e,null),null):(ie(i)?i.forEach(l=>a[l]=null):ze(a,i),Pe(e)&&r.set(e,a),a)}function ta(e,t){return!e||!Yi(t)?!1:(t=t.slice(2).replace(/Once$/,""),_e(e,t[0].toLowerCase()+t.slice(1))||_e(e,Nr(t))||_e(e,t))}let dt=null,qd=null;function Ii(e){const t=dt;return dt=e,qd=e&&e.type.__scopeId||null,t}function re(e,t=dt,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&xu(-1);const i=Ii(t);let a;try{a=e(...s)}finally{Ii(i),r._d&&xu(1)}return a};return r._n=!0,r._c=!0,r._d=!0,r}function Pa(e){const{type:t,vnode:n,proxy:r,withProxy:s,props:i,propsOptions:[a],slots:o,attrs:l,emit:u,render:c,renderCache:d,data:f,setupState:m,ctx:h,inheritAttrs:g}=e;let y,E;const _=Ii(e);try{if(n.shapeFlag&4){const O=s||r;y=Gt(c.call(O,O,d,i,m,f,h)),E=l}else{const O=t;y=Gt(O.length>1?O(i,{attrs:l,slots:o,emit:u}):O(i,null)),E=t.props?l:Ig(l)}}catch(O){us.length=0,ea(O,e,1),y=v(jt)}let w=y;if(E&&g!==!1){const O=Object.keys(E),{shapeFlag:I}=w;O.length&&I&7&&(a&&O.some(Yo)&&(E=Ag(E,a)),w=fn(w,E))}return n.dirs&&(w=fn(w),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&(w.transition=n.transition),y=w,Ii(_),y}const Ig=e=>{let t;for(const n in e)(n==="class"||n==="style"||Yi(n))&&((t||(t={}))[n]=e[n]);return t},Ag=(e,t)=>{const n={};for(const r in e)(!Yo(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Vg(e,t,n){const{props:r,children:s,component:i}=e,{props:a,children:o,patchFlag:l}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?du(r,a,u):!!a;if(l&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const f=c[d];if(a[f]!==r[f]&&!ta(u,f))return!0}}}else return(s||o)&&(!o||!o.$stable)?!0:r===a?!1:r?a?du(r,a,u):!0:!!a;return!1}function du(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const i=r[s];if(t[i]!==e[i]&&!ta(n,i))return!0}return!1}function Pg({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const Mg=e=>e.__isSuspense;function Fg(e,t){t&&t.pendingBranch?ie(e)?t.effects.push(...e):t.effects.push(e):Tg(e)}function gn(e,t){return ul(e,null,t)}const ni={};function he(e,t,n){return ul(e,t,n)}function ul(e,t,{immediate:n,deep:r,flush:s,onTrack:i,onTrigger:a}=Fe){var o;const l=_d()===((o=Ke)==null?void 0:o.scope)?Ke:null;let u,c=!1,d=!1;if(Oe(e)?(u=()=>e.value,c=Oi(e)):ln(e)?(u=()=>e,r=!0):ie(e)?(d=!0,c=e.some(O=>ln(O)||Oi(O)),u=()=>e.map(O=>{if(Oe(O))return O.value;if(ln(O))return er(O);if(me(O))return Fn(O,l,2)})):me(e)?t?u=()=>Fn(e,l,2):u=()=>{if(!(l&&l.isUnmounted))return f&&f(),kt(e,l,3,[m])}:u=zt,t&&r){const O=u;u=()=>er(O())}let f,m=O=>{f=_.onStop=()=>{Fn(O,l,4)}},h;if(xs)if(m=zt,t?n&&kt(t,l,3,[u(),d?[]:void 0,m]):u(),s==="sync"){const O=Iy();h=O.__watcherHandles||(O.__watcherHandles=[])}else return zt;let g=d?new Array(e.length).fill(ni):ni;const y=()=>{if(_.active)if(t){const O=_.run();(r||c||(d?O.some((I,k)=>vs(I,g[k])):vs(O,g)))&&(f&&f(),kt(t,l,3,[O,g===ni?void 0:d&&g[0]===ni?[]:g,m]),g=O)}else _.run()};y.allowRecurse=!!t;let E;s==="sync"?E=y:s==="post"?E=()=>ct(y,l&&l.suspense):(y.pre=!0,l&&(y.id=l.uid),E=()=>ll(y));const _=new tl(u,E);t?n?y():g=_.run():s==="post"?ct(_.run.bind(_),l&&l.suspense):_.run();const w=()=>{_.stop(),l&&l.scope&&Go(l.scope.effects,_)};return h&&h.push(w),w}function Dg(e,t,n){const r=this.proxy,s=$e(e)?e.includes(".")?Zd(r,e):()=>r[e]:e.bind(r,r);let i;me(t)?i=t:(i=t.handler,n=t);const a=Ke;Vr(this);const o=ul(s,i.bind(r),n);return a?Vr(a):ir(),o}function Zd(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}function er(e,t){if(!Pe(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),Oe(e))er(e.value,t);else if(ie(e))for(let n=0;n<e.length;n++)er(e[n],t);else if(yd(e)||Tr(e))e.forEach(n=>{er(n,t)});else if(wd(e))for(const n in e)er(e[n],t);return e}function ft(e,t){const n=dt;if(n===null)return e;const r=ia(n)||n.proxy,s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[a,o,l,u=Fe]=t[i];a&&(me(a)&&(a={mounted:a,updated:a}),a.deep&&er(o),s.push({dir:a,instance:r,value:o,oldValue:void 0,arg:l,modifiers:u}))}return e}function Wn(e,t,n,r){const s=e.dirs,i=t&&t.dirs;for(let a=0;a<s.length;a++){const o=s[a];i&&(o.oldValue=i[a].value);let l=o.dir[r];l&&(Lr(),kt(l,n,8,[e.el,o,e,t]),Rr())}}function Yd(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return pn(()=>{e.isMounted=!0}),bn(()=>{e.isUnmounting=!0}),e}const Ct=[Function,Array],Gd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ct,onEnter:Ct,onAfterEnter:Ct,onEnterCancelled:Ct,onBeforeLeave:Ct,onLeave:Ct,onAfterLeave:Ct,onLeaveCancelled:Ct,onBeforeAppear:Ct,onAppear:Ct,onAfterAppear:Ct,onAppearCancelled:Ct},Ng={name:"BaseTransition",props:Gd,setup(e,{slots:t}){const n=gl(),r=Yd();let s;return()=>{const i=t.default&&cl(t.default(),!0);if(!i||!i.length)return;let a=i[0];if(i.length>1){for(const g of i)if(g.type!==jt){a=g;break}}const o=ce(e),{mode:l}=o;if(r.isLeaving)return Ma(a);const u=fu(a);if(!u)return Ma(a);const c=bs(u,o,r,n);ws(u,c);const d=n.subTree,f=d&&fu(d);let m=!1;const{getTransitionKey:h}=u.type;if(h){const g=h();s===void 0?s=g:g!==s&&(s=g,m=!0)}if(f&&f.type!==jt&&(!Xn(u,f)||m)){const g=bs(f,o,r,n);if(ws(f,g),l==="out-in")return r.isLeaving=!0,g.afterLeave=()=>{r.isLeaving=!1,n.update.active!==!1&&n.update()},Ma(a);l==="in-out"&&u.type!==jt&&(g.delayLeave=(y,E,_)=>{const w=Kd(r,f);w[String(f.key)]=f,y._leaveCb=()=>{E(),y._leaveCb=void 0,delete c.delayedLeave},c.delayedLeave=_})}return a}}},Lg=Ng;function Kd(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function bs(e,t,n,r){const{appear:s,mode:i,persisted:a=!1,onBeforeEnter:o,onEnter:l,onAfterEnter:u,onEnterCancelled:c,onBeforeLeave:d,onLeave:f,onAfterLeave:m,onLeaveCancelled:h,onBeforeAppear:g,onAppear:y,onAfterAppear:E,onAppearCancelled:_}=t,w=String(e.key),O=Kd(n,e),I=(p,T)=>{p&&kt(p,r,9,T)},k=(p,T)=>{const V=T[1];I(p,T),ie(p)?p.every($=>$.length<=1)&&V():p.length<=1&&V()},C={mode:i,persisted:a,beforeEnter(p){let T=o;if(!n.isMounted)if(s)T=g||o;else return;p._leaveCb&&p._leaveCb(!0);const V=O[w];V&&Xn(e,V)&&V.el._leaveCb&&V.el._leaveCb(),I(T,[p])},enter(p){let T=l,V=u,$=c;if(!n.isMounted)if(s)T=y||l,V=E||u,$=_||c;else return;let P=!1;const R=p._enterCb=F=>{P||(P=!0,F?I($,[p]):I(V,[p]),C.delayedLeave&&C.delayedLeave(),p._enterCb=void 0)};T?k(T,[p,R]):R()},leave(p,T){const V=String(e.key);if(p._enterCb&&p._enterCb(!0),n.isUnmounting)return T();I(d,[p]);let $=!1;const P=p._leaveCb=R=>{$||($=!0,T(),R?I(h,[p]):I(m,[p]),p._leaveCb=void 0,O[V]===e&&delete O[V])};O[V]=e,f?k(f,[p,P]):P()},clone(p){return bs(p,t,n,r)}};return C}function Ma(e){if(na(e))return e=fn(e),e.children=null,e}function fu(e){return na(e)?e.children?e.children[0]:void 0:e}function ws(e,t){e.shapeFlag&6&&e.component?ws(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function cl(e,t=!1,n){let r=[],s=0;for(let i=0;i<e.length;i++){let a=e[i];const o=n==null?a.key:String(n)+String(a.key!=null?a.key:i);a.type===ke?(a.patchFlag&128&&s++,r=r.concat(cl(a.children,t,o))):(t||a.type!==jt)&&r.push(o!=null?fn(a,{key:o}):a)}if(s>1)for(let i=0;i<r.length;i++)r[i].patchFlag=-2;return r}function Rg(e,t){return me(e)?(()=>ze({name:e.name},t,{setup:e}))():e}const vi=e=>!!e.type.__asyncLoader,na=e=>e.type.__isKeepAlive;function $g(e,t){Jd(e,"a",t)}function Bg(e,t){Jd(e,"da",t)}function Jd(e,t,n=Ke){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(ra(t,r,n),n){let s=n.parent;for(;s&&s.parent;)na(s.parent.vnode)&&Hg(r,t,n,s),s=s.parent}}function Hg(e,t,n,r){const s=ra(t,e,r,!0);Qd(()=>{Go(r[t],s)},n)}function ra(e,t,n=Ke,r=!1){if(n){const s=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...a)=>{if(n.isUnmounted)return;Lr(),Vr(n);const o=kt(t,n,e,a);return ir(),Rr(),o});return r?s.unshift(i):s.push(i),i}}const yn=e=>(t,n=Ke)=>(!xs||e==="sp")&&ra(e,(...r)=>t(...r),n),dl=yn("bm"),pn=yn("m"),Ug=yn("bu"),Xd=yn("u"),bn=yn("bum"),Qd=yn("um"),zg=yn("sp"),jg=yn("rtg"),Wg=yn("rtc");function qg(e,t=Ke){ra("ec",e,t)}const ef="components",Zg="directives",tf=Symbol.for("v-ndc");function Yg(e){return $e(e)?nf(ef,e,!1)||e:e||tf}function dn(e){return nf(Zg,e)}function nf(e,t,n=!0,r=!1){const s=dt||Ke;if(s){const i=s.type;if(e===ef){const o=Ty(i,!1);if(o&&(o===t||o===It(t)||o===vn(It(t))))return i}const a=mu(s[e]||i[e],t)||mu(s.appContext[e],t);return!a&&r?i:a}}function mu(e,t){return e&&(e[t]||e[It(t)]||e[vn(It(t))])}function rf(e,t,n,r){let s;const i=n&&n[r];if(ie(e)||$e(e)){s=new Array(e.length);for(let a=0,o=e.length;a<o;a++)s[a]=t(e[a],a,void 0,i&&i[a])}else if(typeof e=="number"){s=new Array(e);for(let a=0;a<e;a++)s[a]=t(a+1,a,void 0,i&&i[a])}else if(Pe(e))if(e[Symbol.iterator])s=Array.from(e,(a,o)=>t(a,o,void 0,i&&i[o]));else{const a=Object.keys(e);s=new Array(a.length);for(let o=0,l=a.length;o<l;o++){const u=a[o];s[o]=t(e[u],u,o,i&&i[o])}}else s=[];return n&&(n[r]=s),s}const co=e=>e?vf(e)?ia(e)||e.proxy:co(e.parent):null,os=ze(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>co(e.parent),$root:e=>co(e.root),$emit:e=>e.emit,$options:e=>fl(e),$forceUpdate:e=>e.f||(e.f=()=>ll(e.update)),$nextTick:e=>e.n||(e.n=ut.bind(e.proxy)),$watch:e=>Dg.bind(e)}),Fa=(e,t)=>e!==Fe&&!e.__isScriptSetup&&_e(e,t),Gg={get({_:e},t){const{ctx:n,setupState:r,data:s,props:i,accessCache:a,type:o,appContext:l}=e;let u;if(t[0]!=="$"){const m=a[t];if(m!==void 0)switch(m){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return i[t]}else{if(Fa(r,t))return a[t]=1,r[t];if(s!==Fe&&_e(s,t))return a[t]=2,s[t];if((u=e.propsOptions[0])&&_e(u,t))return a[t]=3,i[t];if(n!==Fe&&_e(n,t))return a[t]=4,n[t];fo&&(a[t]=0)}}const c=os[t];let d,f;if(c)return t==="$attrs"&&mt(e,"get",t),c(e);if((d=o.__cssModules)&&(d=d[t]))return d;if(n!==Fe&&_e(n,t))return a[t]=4,n[t];if(f=l.config.globalProperties,_e(f,t))return f[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:i}=e;return Fa(s,t)?(s[t]=n,!0):r!==Fe&&_e(r,t)?(r[t]=n,!0):_e(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:i}},a){let o;return!!n[a]||e!==Fe&&_e(e,a)||Fa(t,a)||(o=i[0])&&_e(o,a)||_e(r,a)||_e(os,a)||_e(s.config.globalProperties,a)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:_e(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function hu(e){return ie(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let fo=!0;function Kg(e){const t=fl(e),n=e.proxy,r=e.ctx;fo=!1,t.beforeCreate&&vu(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:a,watch:o,provide:l,inject:u,created:c,beforeMount:d,mounted:f,beforeUpdate:m,updated:h,activated:g,deactivated:y,beforeDestroy:E,beforeUnmount:_,destroyed:w,unmounted:O,render:I,renderTracked:k,renderTriggered:C,errorCaptured:p,serverPrefetch:T,expose:V,inheritAttrs:$,components:P,directives:R,filters:F}=t;if(u&&Jg(u,r,null),a)for(const ee in a){const U=a[ee];me(U)&&(r[ee]=U.bind(n))}if(s){const ee=s.call(n,n);Pe(ee)&&(e.data=nt(ee))}if(fo=!0,i)for(const ee in i){const U=i[ee],W=me(U)?U.bind(n,n):me(U.get)?U.get.bind(n,n):zt,fe=!me(U)&&me(U.set)?U.set.bind(n):zt,oe=S({get:W,set:fe});Object.defineProperty(r,ee,{enumerable:!0,configurable:!0,get:()=>oe.value,set:Ce=>oe.value=Ce})}if(o)for(const ee in o)sf(o[ee],r,n,ee);if(l){const ee=me(l)?l.call(n):l;Reflect.ownKeys(ee).forEach(U=>{wt(U,ee[U])})}c&&vu(c,e,"c");function Z(ee,U){ie(U)?U.forEach(W=>ee(W.bind(n))):U&&ee(U.bind(n))}if(Z(dl,d),Z(pn,f),Z(Ug,m),Z(Xd,h),Z($g,g),Z(Bg,y),Z(qg,p),Z(Wg,k),Z(jg,C),Z(bn,_),Z(Qd,O),Z(zg,T),ie(V))if(V.length){const ee=e.exposed||(e.exposed={});V.forEach(U=>{Object.defineProperty(ee,U,{get:()=>n[U],set:W=>n[U]=W})})}else e.exposed||(e.exposed={});I&&e.render===zt&&(e.render=I),$!=null&&(e.inheritAttrs=$),P&&(e.components=P),R&&(e.directives=R)}function Jg(e,t,n=zt){ie(e)&&(e=mo(e));for(const r in e){const s=e[r];let i;Pe(s)?"default"in s?i=Re(s.from||r,s.default,!0):i=Re(s.from||r):i=Re(s),Oe(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):t[r]=i}}function vu(e,t,n){kt(ie(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function sf(e,t,n,r){const s=r.includes(".")?Zd(n,r):()=>n[r];if($e(e)){const i=t[e];me(i)&&he(s,i)}else if(me(e))he(s,e.bind(n));else if(Pe(e))if(ie(e))e.forEach(i=>sf(i,t,n,r));else{const i=me(e.handler)?e.handler.bind(n):t[e.handler];me(i)&&he(s,i,e)}}function fl(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,o=i.get(t);let l;return o?l=o:!s.length&&!n&&!r?l=t:(l={},s.length&&s.forEach(u=>Ai(l,u,a,!0)),Ai(l,t,a)),Pe(t)&&i.set(t,l),l}function Ai(e,t,n,r=!1){const{mixins:s,extends:i}=t;i&&Ai(e,i,n,!0),s&&s.forEach(a=>Ai(e,a,n,!0));for(const a in t)if(!(r&&a==="expose")){const o=Xg[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const Xg={data:gu,props:yu,emits:yu,methods:ss,computed:ss,beforeCreate:at,created:at,beforeMount:at,mounted:at,beforeUpdate:at,updated:at,beforeDestroy:at,beforeUnmount:at,destroyed:at,unmounted:at,activated:at,deactivated:at,errorCaptured:at,serverPrefetch:at,components:ss,directives:ss,watch:ey,provide:gu,inject:Qg};function gu(e,t){return t?e?function(){return ze(me(e)?e.call(this,this):e,me(t)?t.call(this,this):t)}:t:e}function Qg(e,t){return ss(mo(e),mo(t))}function mo(e){if(ie(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function at(e,t){return e?[...new Set([].concat(e,t))]:t}function ss(e,t){return e?ze(Object.create(null),e,t):t}function yu(e,t){return e?ie(e)&&ie(t)?[...new Set([...e,...t])]:ze(Object.create(null),hu(e),hu(t??{})):t}function ey(e,t){if(!e)return t;if(!t)return e;const n=ze(Object.create(null),e);for(const r in t)n[r]=at(e[r],t[r]);return n}function af(){return{app:null,config:{isNativeTag:Ov,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ty=0;function ny(e,t){return function(r,s=null){me(r)||(r=ze({},r)),s!=null&&!Pe(s)&&(s=null);const i=af(),a=new Set;let o=!1;const l=i.app={_uid:ty++,_component:r,_props:s,_container:null,_context:i,_instance:null,version:Ay,get config(){return i.config},set config(u){},use(u,...c){return a.has(u)||(u&&me(u.install)?(a.add(u),u.install(l,...c)):me(u)&&(a.add(u),u(l,...c))),l},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),l},component(u,c){return c?(i.components[u]=c,l):i.components[u]},directive(u,c){return c?(i.directives[u]=c,l):i.directives[u]},mount(u,c,d){if(!o){const f=v(r,s);return f.appContext=i,c&&t?t(f,u):e(f,u,d),o=!0,l._container=u,u.__vue_app__=l,ia(f.component)||f.component.proxy}},unmount(){o&&(e(null,l._container),delete l._container.__vue_app__)},provide(u,c){return i.provides[u]=c,l},runWithContext(u){Ss=l;try{return u()}finally{Ss=null}}};return l}}let Ss=null;function wt(e,t){if(Ke){let n=Ke.provides;const r=Ke.parent&&Ke.parent.provides;r===n&&(n=Ke.provides=Object.create(r)),n[e]=t}}function Re(e,t,n=!1){const r=Ke||dt;if(r||Ss){const s=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:Ss._context.provides;if(s&&e in s)return s[e];if(arguments.length>1)return n&&me(t)?t.call(r&&r.proxy):t}}function ry(){return!!(Ke||dt||Ss)}function sy(e,t,n,r=!1){const s={},i={};Ti(i,sa,1),e.propsDefaults=Object.create(null),of(e,t,s,i);for(const a in e.propsOptions[0])a in s||(s[a]=void 0);n?e.props=r?s:gg(s):e.type.props?e.props=s:e.props=i,e.attrs=i}function iy(e,t,n,r){const{props:s,attrs:i,vnode:{patchFlag:a}}=e,o=ce(s),[l]=e.propsOptions;let u=!1;if((r||a>0)&&!(a&16)){if(a&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let f=c[d];if(ta(e.emitsOptions,f))continue;const m=t[f];if(l)if(_e(i,f))m!==i[f]&&(i[f]=m,u=!0);else{const h=It(f);s[h]=ho(l,o,h,m,e,!1)}else m!==i[f]&&(i[f]=m,u=!0)}}}else{of(e,t,s,i)&&(u=!0);let c;for(const d in o)(!t||!_e(t,d)&&((c=Nr(d))===d||!_e(t,c)))&&(l?n&&(n[d]!==void 0||n[c]!==void 0)&&(s[d]=ho(l,o,d,void 0,e,!0)):delete s[d]);if(i!==o)for(const d in i)(!t||!_e(t,d))&&(delete i[d],u=!0)}u&&cn(e,"set","$attrs")}function of(e,t,n,r){const[s,i]=e.propsOptions;let a=!1,o;if(t)for(let l in t){if(hi(l))continue;const u=t[l];let c;s&&_e(s,c=It(l))?!i||!i.includes(c)?n[c]=u:(o||(o={}))[c]=u:ta(e.emitsOptions,l)||(!(l in r)||u!==r[l])&&(r[l]=u,a=!0)}if(i){const l=ce(n),u=o||Fe;for(let c=0;c<i.length;c++){const d=i[c];n[d]=ho(s,l,d,u[d],e,!_e(u,d))}}return a}function ho(e,t,n,r,s,i){const a=e[n];if(a!=null){const o=_e(a,"default");if(o&&r===void 0){const l=a.default;if(a.type!==Function&&!a.skipFactory&&me(l)){const{propsDefaults:u}=s;n in u?r=u[n]:(Vr(s),r=u[n]=l.call(null,t),ir())}else r=l}a[0]&&(i&&!o?r=!1:a[1]&&(r===""||r===Nr(n))&&(r=!0))}return r}function lf(e,t,n=!1){const r=t.propsCache,s=r.get(e);if(s)return s;const i=e.props,a={},o=[];let l=!1;if(!me(e)){const c=d=>{l=!0;const[f,m]=lf(d,t,!0);ze(a,f),m&&o.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!i&&!l)return Pe(e)&&r.set(e,Er),Er;if(ie(i))for(let c=0;c<i.length;c++){const d=It(i[c]);pu(d)&&(a[d]=Fe)}else if(i)for(const c in i){const d=It(c);if(pu(d)){const f=i[c],m=a[d]=ie(f)||me(f)?{type:f}:ze({},f);if(m){const h=Su(Boolean,m.type),g=Su(String,m.type);m[0]=h>-1,m[1]=g<0||h<g,(h>-1||_e(m,"default"))&&o.push(d)}}}const u=[a,o];return Pe(e)&&r.set(e,u),u}function pu(e){return e[0]!=="$"}function bu(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function wu(e,t){return bu(e)===bu(t)}function Su(e,t){return ie(t)?t.findIndex(n=>wu(n,e)):me(t)&&wu(t,e)?0:-1}const uf=e=>e[0]==="_"||e==="$stable",ml=e=>ie(e)?e.map(Gt):[Gt(e)],ay=(e,t,n)=>{if(t._n)return t;const r=re((...s)=>ml(t(...s)),n);return r._c=!1,r},cf=(e,t,n)=>{const r=e._ctx;for(const s in e){if(uf(s))continue;const i=e[s];if(me(i))t[s]=ay(s,i,r);else if(i!=null){const a=ml(i);t[s]=()=>a}}},df=(e,t)=>{const n=ml(t);e.slots.default=()=>n},oy=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=ce(t),Ti(t,"_",n)):cf(t,e.slots={})}else e.slots={},t&&df(e,t);Ti(e.slots,sa,1)},ly=(e,t,n)=>{const{vnode:r,slots:s}=e;let i=!0,a=Fe;if(r.shapeFlag&32){const o=t._;o?n&&o===1?i=!1:(ze(s,t),!n&&o===1&&delete s._):(i=!t.$stable,cf(t,s)),a=t}else t&&(df(e,t),a={default:1});if(i)for(const o in s)!uf(o)&&!(o in a)&&delete s[o]};function vo(e,t,n,r,s=!1){if(ie(e)){e.forEach((f,m)=>vo(f,t&&(ie(t)?t[m]:t),n,r,s));return}if(vi(r)&&!s)return;const i=r.shapeFlag&4?ia(r.component)||r.component.proxy:r.el,a=s?null:i,{i:o,r:l}=e,u=t&&t.r,c=o.refs===Fe?o.refs={}:o.refs,d=o.setupState;if(u!=null&&u!==l&&($e(u)?(c[u]=null,_e(d,u)&&(d[u]=null)):Oe(u)&&(u.value=null)),me(l))Fn(l,o,12,[a,c]);else{const f=$e(l),m=Oe(l);if(f||m){const h=()=>{if(e.f){const g=f?_e(d,l)?d[l]:c[l]:l.value;s?ie(g)&&Go(g,i):ie(g)?g.includes(i)||g.push(i):f?(c[l]=[i],_e(d,l)&&(d[l]=c[l])):(l.value=[i],e.k&&(c[e.k]=l.value))}else f?(c[l]=a,_e(d,l)&&(d[l]=a)):m&&(l.value=a,e.k&&(c[e.k]=a))};a?(h.id=-1,ct(h,n)):h()}}}const ct=Fg;function uy(e){return cy(e)}function cy(e,t){const n=io();n.__VUE__=!0;const{insert:r,remove:s,patchProp:i,createElement:a,createText:o,createComment:l,setText:u,setElementText:c,parentNode:d,nextSibling:f,setScopeId:m=zt,insertStaticContent:h}=e,g=(b,x,A,N=null,D=null,H=null,Y=!1,B=null,j=!!x.dynamicChildren)=>{if(b===x)return;b&&!Xn(b,x)&&(N=Dt(b),Ce(b,D,H,!0),b=null),x.patchFlag===-2&&(j=!1,x.dynamicChildren=null);const{type:L,ref:te,shapeFlag:X}=x;switch(L){case Ms:y(b,x,A,N);break;case jt:E(b,x,A,N);break;case Da:b==null&&_(x,A,N,Y);break;case ke:P(b,x,A,N,D,H,Y,B,j);break;default:X&1?I(b,x,A,N,D,H,Y,B,j):X&6?R(b,x,A,N,D,H,Y,B,j):(X&64||X&128)&&L.process(b,x,A,N,D,H,Y,B,j,Sn)}te!=null&&D&&vo(te,b&&b.ref,H,x||b,!x)},y=(b,x,A,N)=>{if(b==null)r(x.el=o(x.children),A,N);else{const D=x.el=b.el;x.children!==b.children&&u(D,x.children)}},E=(b,x,A,N)=>{b==null?r(x.el=l(x.children||""),A,N):x.el=b.el},_=(b,x,A,N)=>{[b.el,b.anchor]=h(b.children,x,A,N,b.el,b.anchor)},w=({el:b,anchor:x},A,N)=>{let D;for(;b&&b!==x;)D=f(b),r(b,A,N),b=D;r(x,A,N)},O=({el:b,anchor:x})=>{let A;for(;b&&b!==x;)A=f(b),s(b),b=A;s(x)},I=(b,x,A,N,D,H,Y,B,j)=>{Y=Y||x.type==="svg",b==null?k(x,A,N,D,H,Y,B,j):T(b,x,D,H,Y,B,j)},k=(b,x,A,N,D,H,Y,B)=>{let j,L;const{type:te,props:X,shapeFlag:ne,transition:le,dirs:pe}=b;if(j=b.el=a(b.type,H,X&&X.is,X),ne&8?c(j,b.children):ne&16&&p(b.children,j,null,N,D,H&&te!=="foreignObject",Y,B),pe&&Wn(b,null,N,"created"),C(j,b,b.scopeId,Y,N),X){for(const Ie in X)Ie!=="value"&&!hi(Ie)&&i(j,Ie,null,X[Ie],H,b.children,N,D,ge);"value"in X&&i(j,"value",null,X.value),(L=X.onVnodeBeforeMount)&&Yt(L,N,b)}pe&&Wn(b,null,N,"beforeMount");const Ve=(!D||D&&!D.pendingBranch)&&le&&!le.persisted;Ve&&le.beforeEnter(j),r(j,x,A),((L=X&&X.onVnodeMounted)||Ve||pe)&&ct(()=>{L&&Yt(L,N,b),Ve&&le.enter(j),pe&&Wn(b,null,N,"mounted")},D)},C=(b,x,A,N,D)=>{if(A&&m(b,A),N)for(let H=0;H<N.length;H++)m(b,N[H]);if(D){let H=D.subTree;if(x===H){const Y=D.vnode;C(b,Y,Y.scopeId,Y.slotScopeIds,D.parent)}}},p=(b,x,A,N,D,H,Y,B,j=0)=>{for(let L=j;L<b.length;L++){const te=b[L]=B?In(b[L]):Gt(b[L]);g(null,te,x,A,N,D,H,Y,B)}},T=(b,x,A,N,D,H,Y)=>{const B=x.el=b.el;let{patchFlag:j,dynamicChildren:L,dirs:te}=x;j|=b.patchFlag&16;const X=b.props||Fe,ne=x.props||Fe;let le;A&&qn(A,!1),(le=ne.onVnodeBeforeUpdate)&&Yt(le,A,x,b),te&&Wn(x,b,A,"beforeUpdate"),A&&qn(A,!0);const pe=D&&x.type!=="foreignObject";if(L?V(b.dynamicChildren,L,B,A,N,pe,H):Y||U(b,x,B,null,A,N,pe,H,!1),j>0){if(j&16)$(B,x,X,ne,A,N,D);else if(j&2&&X.class!==ne.class&&i(B,"class",null,ne.class,D),j&4&&i(B,"style",X.style,ne.style,D),j&8){const Ve=x.dynamicProps;for(let Ie=0;Ie<Ve.length;Ie++){const We=Ve[Ie],Nt=X[We],vr=ne[We];(vr!==Nt||We==="value")&&i(B,We,Nt,vr,D,b.children,A,N,ge)}}j&1&&b.children!==x.children&&c(B,x.children)}else!Y&&L==null&&$(B,x,X,ne,A,N,D);((le=ne.onVnodeUpdated)||te)&&ct(()=>{le&&Yt(le,A,x,b),te&&Wn(x,b,A,"updated")},N)},V=(b,x,A,N,D,H,Y)=>{for(let B=0;B<x.length;B++){const j=b[B],L=x[B],te=j.el&&(j.type===ke||!Xn(j,L)||j.shapeFlag&70)?d(j.el):A;g(j,L,te,null,N,D,H,Y,!0)}},$=(b,x,A,N,D,H,Y)=>{if(A!==N){if(A!==Fe)for(const B in A)!hi(B)&&!(B in N)&&i(b,B,A[B],null,Y,x.children,D,H,ge);for(const B in N){if(hi(B))continue;const j=N[B],L=A[B];j!==L&&B!=="value"&&i(b,B,L,j,Y,x.children,D,H,ge)}"value"in N&&i(b,"value",A.value,N.value)}},P=(b,x,A,N,D,H,Y,B,j)=>{const L=x.el=b?b.el:o(""),te=x.anchor=b?b.anchor:o("");let{patchFlag:X,dynamicChildren:ne,slotScopeIds:le}=x;le&&(B=B?B.concat(le):le),b==null?(r(L,A,N),r(te,A,N),p(x.children,A,te,D,H,Y,B,j)):X>0&&X&64&&ne&&b.dynamicChildren?(V(b.dynamicChildren,ne,A,D,H,Y,B),(x.key!=null||D&&x===D.subTree)&&hl(b,x,!0)):U(b,x,A,te,D,H,Y,B,j)},R=(b,x,A,N,D,H,Y,B,j)=>{x.slotScopeIds=B,b==null?x.shapeFlag&512?D.ctx.activate(x,A,N,Y,j):F(x,A,N,D,H,Y,j):G(b,x,j)},F=(b,x,A,N,D,H,Y)=>{const B=b.component=Sy(b,N,D);if(na(b)&&(B.ctx.renderer=Sn),Cy(B),B.asyncDep){if(D&&D.registerDep(B,Z),!b.el){const j=B.subTree=v(jt);E(null,j,x,A)}return}Z(B,b,x,A,D,H,Y)},G=(b,x,A)=>{const N=x.component=b.component;if(Vg(b,x,A))if(N.asyncDep&&!N.asyncResolved){ee(N,x,A);return}else N.next=x,Eg(N.update),N.update();else x.el=b.el,N.vnode=x},Z=(b,x,A,N,D,H,Y)=>{const B=()=>{if(b.isMounted){let{next:te,bu:X,u:ne,parent:le,vnode:pe}=b,Ve=te,Ie;qn(b,!1),te?(te.el=pe.el,ee(b,te,Y)):te=pe,X&&Va(X),(Ie=te.props&&te.props.onVnodeBeforeUpdate)&&Yt(Ie,le,te,pe),qn(b,!0);const We=Pa(b),Nt=b.subTree;b.subTree=We,g(Nt,We,d(Nt.el),Dt(Nt),b,D,H),te.el=We.el,Ve===null&&Pg(b,We.el),ne&&ct(ne,D),(Ie=te.props&&te.props.onVnodeUpdated)&&ct(()=>Yt(Ie,le,te,pe),D)}else{let te;const{el:X,props:ne}=x,{bm:le,m:pe,parent:Ve}=b,Ie=vi(x);if(qn(b,!1),le&&Va(le),!Ie&&(te=ne&&ne.onVnodeBeforeMount)&&Yt(te,Ve,x),qn(b,!0),X&&Ia){const We=()=>{b.subTree=Pa(b),Ia(X,b.subTree,b,D,null)};Ie?x.type.__asyncLoader().then(()=>!b.isUnmounted&&We()):We()}else{const We=b.subTree=Pa(b);g(null,We,A,N,b,D,H),x.el=We.el}if(pe&&ct(pe,D),!Ie&&(te=ne&&ne.onVnodeMounted)){const We=x;ct(()=>Yt(te,Ve,We),D)}(x.shapeFlag&256||Ve&&vi(Ve.vnode)&&Ve.vnode.shapeFlag&256)&&b.a&&ct(b.a,D),b.isMounted=!0,x=A=N=null}},j=b.effect=new tl(B,()=>ll(L),b.scope),L=b.update=()=>j.run();L.id=b.uid,qn(b,!0),L()},ee=(b,x,A)=>{x.component=b;const N=b.vnode.props;b.vnode=x,b.next=null,iy(b,x.props,N,A),ly(b,x.children,A),Lr(),cu(),Rr()},U=(b,x,A,N,D,H,Y,B,j=!1)=>{const L=b&&b.children,te=b?b.shapeFlag:0,X=x.children,{patchFlag:ne,shapeFlag:le}=x;if(ne>0){if(ne&128){fe(L,X,A,N,D,H,Y,B,j);return}else if(ne&256){W(L,X,A,N,D,H,Y,B,j);return}}le&8?(te&16&&ge(L,D,H),X!==L&&c(A,X)):te&16?le&16?fe(L,X,A,N,D,H,Y,B,j):ge(L,D,H,!0):(te&8&&c(A,""),le&16&&p(X,A,N,D,H,Y,B,j))},W=(b,x,A,N,D,H,Y,B,j)=>{b=b||Er,x=x||Er;const L=b.length,te=x.length,X=Math.min(L,te);let ne;for(ne=0;ne<X;ne++){const le=x[ne]=j?In(x[ne]):Gt(x[ne]);g(b[ne],le,A,null,D,H,Y,B,j)}L>te?ge(b,D,H,!0,!1,X):p(x,A,N,D,H,Y,B,j,X)},fe=(b,x,A,N,D,H,Y,B,j)=>{let L=0;const te=x.length;let X=b.length-1,ne=te-1;for(;L<=X&&L<=ne;){const le=b[L],pe=x[L]=j?In(x[L]):Gt(x[L]);if(Xn(le,pe))g(le,pe,A,null,D,H,Y,B,j);else break;L++}for(;L<=X&&L<=ne;){const le=b[X],pe=x[ne]=j?In(x[ne]):Gt(x[ne]);if(Xn(le,pe))g(le,pe,A,null,D,H,Y,B,j);else break;X--,ne--}if(L>X){if(L<=ne){const le=ne+1,pe=le<te?x[le].el:N;for(;L<=ne;)g(null,x[L]=j?In(x[L]):Gt(x[L]),A,pe,D,H,Y,B,j),L++}}else if(L>ne)for(;L<=X;)Ce(b[L],D,H,!0),L++;else{const le=L,pe=L,Ve=new Map;for(L=pe;L<=ne;L++){const ht=x[L]=j?In(x[L]):Gt(x[L]);ht.key!=null&&Ve.set(ht.key,L)}let Ie,We=0;const Nt=ne-pe+1;let vr=!1,Ql=0;const Yr=new Array(Nt);for(L=0;L<Nt;L++)Yr[L]=0;for(L=le;L<=X;L++){const ht=b[L];if(We>=Nt){Ce(ht,D,H,!0);continue}let Zt;if(ht.key!=null)Zt=Ve.get(ht.key);else for(Ie=pe;Ie<=ne;Ie++)if(Yr[Ie-pe]===0&&Xn(ht,x[Ie])){Zt=Ie;break}Zt===void 0?Ce(ht,D,H,!0):(Yr[Zt-pe]=L+1,Zt>=Ql?Ql=Zt:vr=!0,g(ht,x[Zt],A,null,D,H,Y,B,j),We++)}const eu=vr?dy(Yr):Er;for(Ie=eu.length-1,L=Nt-1;L>=0;L--){const ht=pe+L,Zt=x[ht],tu=ht+1<te?x[ht+1].el:N;Yr[L]===0?g(null,Zt,A,tu,D,H,Y,B,j):vr&&(Ie<0||L!==eu[Ie]?oe(Zt,A,tu,2):Ie--)}}},oe=(b,x,A,N,D=null)=>{const{el:H,type:Y,transition:B,children:j,shapeFlag:L}=b;if(L&6){oe(b.component.subTree,x,A,N);return}if(L&128){b.suspense.move(x,A,N);return}if(L&64){Y.move(b,x,A,Sn);return}if(Y===ke){r(H,x,A);for(let X=0;X<j.length;X++)oe(j[X],x,A,N);r(b.anchor,x,A);return}if(Y===Da){w(b,x,A);return}if(N!==2&&L&1&&B)if(N===0)B.beforeEnter(H),r(H,x,A),ct(()=>B.enter(H),D);else{const{leave:X,delayLeave:ne,afterLeave:le}=B,pe=()=>r(H,x,A),Ve=()=>{X(H,()=>{pe(),le&&le()})};ne?ne(H,pe,Ve):Ve()}else r(H,x,A)},Ce=(b,x,A,N=!1,D=!1)=>{const{type:H,props:Y,ref:B,children:j,dynamicChildren:L,shapeFlag:te,patchFlag:X,dirs:ne}=b;if(B!=null&&vo(B,null,A,b,!0),te&256){x.ctx.deactivate(b);return}const le=te&1&&ne,pe=!vi(b);let Ve;if(pe&&(Ve=Y&&Y.onVnodeBeforeUnmount)&&Yt(Ve,x,b),te&6)Ft(b.component,A,N);else{if(te&128){b.suspense.unmount(A,N);return}le&&Wn(b,null,x,"beforeUnmount"),te&64?b.type.remove(b,x,A,D,Sn,N):L&&(H!==ke||X>0&&X&64)?ge(L,x,A,!1,!0):(H===ke&&X&384||!D&&te&16)&&ge(j,x,A),N&&De(b)}(pe&&(Ve=Y&&Y.onVnodeUnmounted)||le)&&ct(()=>{Ve&&Yt(Ve,x,b),le&&Wn(b,null,x,"unmounted")},A)},De=b=>{const{type:x,el:A,anchor:N,transition:D}=b;if(x===ke){He(A,N);return}if(x===Da){O(b);return}const H=()=>{s(A),D&&!D.persisted&&D.afterLeave&&D.afterLeave()};if(b.shapeFlag&1&&D&&!D.persisted){const{leave:Y,delayLeave:B}=D,j=()=>Y(A,H);B?B(b.el,H,j):j()}else H()},He=(b,x)=>{let A;for(;b!==x;)A=f(b),s(b),b=A;s(x)},Ft=(b,x,A)=>{const{bum:N,scope:D,update:H,subTree:Y,um:B}=b;N&&Va(N),D.stop(),H&&(H.active=!1,Ce(Y,b,x,A)),B&&ct(B,x),ct(()=>{b.isUnmounted=!0},x),x&&x.pendingBranch&&!x.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===x.pendingId&&(x.deps--,x.deps===0&&x.resolve())},ge=(b,x,A,N=!1,D=!1,H=0)=>{for(let Y=H;Y<b.length;Y++)Ce(b[Y],x,A,N,D)},Dt=b=>b.shapeFlag&6?Dt(b.component.subTree):b.shapeFlag&128?b.suspense.next():f(b.anchor||b.el),Ks=(b,x,A)=>{b==null?x._vnode&&Ce(x._vnode,null,null,!0):g(x._vnode||null,b,x,null,null,null,A),cu(),zd(),x._vnode=b},Sn={p:g,um:Ce,m:oe,r:De,mt:F,mc:p,pc:U,pbc:V,n:Dt,o:e};let Oa,Ia;return t&&([Oa,Ia]=t(Sn)),{render:Ks,hydrate:Oa,createApp:ny(Ks,Oa)}}function qn({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function hl(e,t,n=!1){const r=e.children,s=t.children;if(ie(r)&&ie(s))for(let i=0;i<r.length;i++){const a=r[i];let o=s[i];o.shapeFlag&1&&!o.dynamicChildren&&((o.patchFlag<=0||o.patchFlag===32)&&(o=s[i]=In(s[i]),o.el=a.el),n||hl(a,o)),o.type===Ms&&(o.el=a.el)}}function dy(e){const t=e.slice(),n=[0];let r,s,i,a,o;const l=e.length;for(r=0;r<l;r++){const u=e[r];if(u!==0){if(s=n[n.length-1],e[s]<u){t[r]=s,n.push(r);continue}for(i=0,a=n.length-1;i<a;)o=i+a>>1,e[n[o]]<u?i=o+1:a=o;u<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,a=n[i-1];i-- >0;)n[i]=a,a=t[a];return n}const fy=e=>e.__isTeleport,ls=e=>e&&(e.disabled||e.disabled===""),Cu=e=>typeof SVGElement<"u"&&e instanceof SVGElement,go=(e,t)=>{const n=e&&e.to;return $e(n)?t?t(n):null:n},my={__isTeleport:!0,process(e,t,n,r,s,i,a,o,l,u){const{mc:c,pc:d,pbc:f,o:{insert:m,querySelector:h,createText:g,createComment:y}}=u,E=ls(t.props);let{shapeFlag:_,children:w,dynamicChildren:O}=t;if(e==null){const I=t.el=g(""),k=t.anchor=g("");m(I,n,r),m(k,n,r);const C=t.target=go(t.props,h),p=t.targetAnchor=g("");C&&(m(p,C),a=a||Cu(C));const T=(V,$)=>{_&16&&c(w,V,$,s,i,a,o,l)};E?T(n,k):C&&T(C,p)}else{t.el=e.el;const I=t.anchor=e.anchor,k=t.target=e.target,C=t.targetAnchor=e.targetAnchor,p=ls(e.props),T=p?n:k,V=p?I:C;if(a=a||Cu(k),O?(f(e.dynamicChildren,O,T,s,i,a,o),hl(e,t,!0)):l||d(e,t,T,V,s,i,a,o,!1),E)p||ri(t,n,I,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const $=t.target=go(t.props,h);$&&ri(t,$,null,u,0)}else p&&ri(t,k,C,u,1)}ff(t)},remove(e,t,n,r,{um:s,o:{remove:i}},a){const{shapeFlag:o,children:l,anchor:u,targetAnchor:c,target:d,props:f}=e;if(d&&i(c),(a||!ls(f))&&(i(u),o&16))for(let m=0;m<l.length;m++){const h=l[m];s(h,t,n,!0,!!h.dynamicChildren)}},move:ri,hydrate:hy};function ri(e,t,n,{o:{insert:r},m:s},i=2){i===0&&r(e.targetAnchor,t,n);const{el:a,anchor:o,shapeFlag:l,children:u,props:c}=e,d=i===2;if(d&&r(a,t,n),(!d||ls(c))&&l&16)for(let f=0;f<u.length;f++)s(u[f],t,n,2);d&&r(o,t,n)}function hy(e,t,n,r,s,i,{o:{nextSibling:a,parentNode:o,querySelector:l}},u){const c=t.target=go(t.props,l);if(c){const d=c._lpa||c.firstChild;if(t.shapeFlag&16)if(ls(t.props))t.anchor=u(a(e),t,o(e),n,r,s,i),t.targetAnchor=d;else{t.anchor=a(e);let f=d;for(;f;)if(f=a(f),f&&f.nodeType===8&&f.data==="teleport anchor"){t.targetAnchor=f,c._lpa=t.targetAnchor&&a(t.targetAnchor);break}u(d,t,c,n,r,s,i)}ff(t)}return t.anchor&&a(t.anchor)}const vy=my;function ff(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;for(;n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}const ke=Symbol.for("v-fgt"),Ms=Symbol.for("v-txt"),jt=Symbol.for("v-cmt"),Da=Symbol.for("v-stc"),us=[];let Ht=null;function Ye(e=!1){us.push(Ht=e?null:[])}function gy(){us.pop(),Ht=us[us.length-1]||null}let Cs=1;function xu(e){Cs+=e}function mf(e){return e.dynamicChildren=Cs>0?Ht||Er:null,gy(),Cs>0&&Ht&&Ht.push(e),e}function tr(e,t,n,r,s,i){return mf(J(e,t,n,r,s,i,!0))}function gt(e,t,n,r,s){return mf(v(e,t,n,r,s,!0))}function yo(e){return e?e.__v_isVNode===!0:!1}function Xn(e,t){return e.type===t.type&&e.key===t.key}const sa="__vInternal",hf=({key:e})=>e??null,gi=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?$e(e)||Oe(e)||me(e)?{i:dt,r:e,k:t,f:!!n}:e:null);function J(e,t=null,n=null,r=0,s=null,i=e===ke?0:1,a=!1,o=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&hf(t),ref:t&&gi(t),scopeId:qd,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:dt};return o?(vl(l,n),i&128&&e.normalize(l)):n&&(l.shapeFlag|=$e(n)?8:16),Cs>0&&!a&&Ht&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&Ht.push(l),l}const v=yy;function yy(e,t=null,n=null,r=0,s=null,i=!1){if((!e||e===tf)&&(e=jt),yo(e)){const o=fn(e,t,!0);return n&&vl(o,n),Cs>0&&!i&&Ht&&(o.shapeFlag&6?Ht[Ht.indexOf(e)]=o:Ht.push(o)),o.patchFlag|=-2,o}if(ky(e)&&(e=e.__vccOpts),t){t=py(t);let{class:o,style:l}=t;o&&!$e(o)&&(t.class=Qo(o)),Pe(l)&&(Dd(l)&&!ie(l)&&(l=ze({},l)),t.style=Xo(l))}const a=$e(e)?1:Mg(e)?128:fy(e)?64:Pe(e)?4:me(e)?2:0;return J(e,t,n,r,s,a,i,!0)}function py(e){return e?Dd(e)||sa in e?ze({},e):e:null}function fn(e,t,n=!1){const{props:r,ref:s,patchFlag:i,children:a}=e,o=t?ue(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:o,key:o&&hf(o),ref:t&&t.ref?n&&s?ie(s)?s.concat(gi(t)):[s,gi(t)]:gi(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ke?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&fn(e.ssContent),ssFallback:e.ssFallback&&fn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Ze(e=" ",t=0){return v(Ms,null,e,t)}function nr(e="",t=!1){return t?(Ye(),gt(jt,null,e)):v(jt,null,e)}function Gt(e){return e==null||typeof e=="boolean"?v(jt):ie(e)?v(ke,null,e.slice()):typeof e=="object"?In(e):v(Ms,null,String(e))}function In(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:fn(e)}function vl(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(ie(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),vl(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!(sa in t)?t._ctx=dt:s===3&&dt&&(dt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else me(t)?(t={default:t,_ctx:dt},n=32):(t=String(t),r&64?(n=16,t=[Ze(t)]):n=8);e.children=t,e.shapeFlag|=n}function ue(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Qo([t.class,r.class]));else if(s==="style")t.style=Xo([t.style,r.style]);else if(Yi(s)){const i=t[s],a=r[s];a&&i!==a&&!(ie(i)&&i.includes(a))&&(t[s]=i?[].concat(i,a):a)}else s!==""&&(t[s]=r[s])}return t}function Yt(e,t,n,r=null){kt(e,t,7,[n,r])}const by=af();let wy=0;function Sy(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||by,i={uid:wy++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new xd(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:lf(r,s),emitsOptions:Wd(r,s),emit:null,emitted:null,propsDefaults:Fe,inheritAttrs:r.inheritAttrs,ctx:Fe,data:Fe,props:Fe,attrs:Fe,slots:Fe,refs:Fe,setupState:Fe,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Og.bind(null,i),e.ce&&e.ce(i),i}let Ke=null;const gl=()=>Ke||dt;let yl,gr,_u="__VUE_INSTANCE_SETTERS__";(gr=io()[_u])||(gr=io()[_u]=[]),gr.push(e=>Ke=e),yl=e=>{gr.length>1?gr.forEach(t=>t(e)):gr[0](e)};const Vr=e=>{yl(e),e.scope.on()},ir=()=>{Ke&&Ke.scope.off(),yl(null)};function vf(e){return e.vnode.shapeFlag&4}let xs=!1;function Cy(e,t=!1){xs=t;const{props:n,children:r}=e.vnode,s=vf(e);sy(e,n,s,t),oy(e,r);const i=s?xy(e,t):void 0;return xs=!1,i}function xy(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Xi(new Proxy(e.ctx,Gg));const{setup:r}=n;if(r){const s=e.setupContext=r.length>1?Ey(e):null;Vr(e),Lr();const i=Fn(r,e,0,[e.props,s]);if(Rr(),ir(),pd(i)){if(i.then(ir,ir),t)return i.then(a=>{Eu(e,a,t)}).catch(a=>{ea(a,e,0)});e.asyncDep=i}else Eu(e,i,t)}else gf(e,t)}function Eu(e,t,n){me(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Pe(t)&&(e.setupState=$d(t)),gf(e,n)}let Tu;function gf(e,t,n){const r=e.type;if(!e.render){if(!t&&Tu&&!r.render){const s=r.template||fl(e).template;if(s){const{isCustomElement:i,compilerOptions:a}=e.appContext.config,{delimiters:o,compilerOptions:l}=r,u=ze(ze({isCustomElement:i,delimiters:o},a),l);r.render=Tu(s,u)}}e.render=r.render||zt}Vr(e),Lr(),Kg(e),Rr(),ir()}function _y(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return mt(e,"get","$attrs"),t[n]}}))}function Ey(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return _y(e)},slots:e.slots,emit:e.emit,expose:t}}function ia(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy($d(Xi(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in os)return os[n](e)},has(t,n){return n in t||n in os}}))}function Ty(e,t=!0){return me(e)?e.displayName||e.name:e.name||t&&e.__name}function ky(e){return me(e)&&"__vccOpts"in e}const S=(e,t)=>Cg(e,t,xs);function $n(e,t,n){const r=arguments.length;return r===2?Pe(t)&&!ie(t)?yo(t)?v(e,null,[t]):v(e,t):v(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&yo(n)&&(n=[n]),v(e,t,n))}const Oy=Symbol.for("v-scx"),Iy=()=>Re(Oy),Ay="3.3.4",Vy="http://www.w3.org/2000/svg",Qn=typeof document<"u"?document:null,ku=Qn&&Qn.createElement("template"),Py={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t?Qn.createElementNS(Vy,e):Qn.createElement(e,n?{is:n}:void 0);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>Qn.createTextNode(e),createComment:e=>Qn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Qn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,i){const a=n?n.previousSibling:t.lastChild;if(s&&(s===i||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===i||!(s=s.nextSibling)););else{ku.innerHTML=r?`<svg>${e}</svg>`:e;const o=ku.content;if(r){const l=o.firstChild;for(;l.firstChild;)o.appendChild(l.firstChild);o.removeChild(l)}t.insertBefore(o,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function My(e,t,n){const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function Fy(e,t,n){const r=e.style,s=$e(n);if(n&&!s){if(t&&!$e(t))for(const i in t)n[i]==null&&po(r,i,"");for(const i in n)po(r,i,n[i])}else{const i=r.display;s?t!==n&&(r.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=i)}}const Ou=/\s*!important$/;function po(e,t,n){if(ie(n))n.forEach(r=>po(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Dy(e,t);Ou.test(n)?e.setProperty(Nr(r),n.replace(Ou,""),"important"):e[r]=n}}const Iu=["Webkit","Moz","ms"],Na={};function Dy(e,t){const n=Na[t];if(n)return n;let r=It(t);if(r!=="filter"&&r in e)return Na[t]=r;r=vn(r);for(let s=0;s<Iu.length;s++){const i=Iu[s]+r;if(i in e)return Na[t]=i}return t}const Au="http://www.w3.org/1999/xlink";function Ny(e,t,n,r,s){if(r&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(Au,t.slice(6,t.length)):e.setAttributeNS(Au,t,n);else{const i=Hv(t);n==null||i&&!Sd(n)?e.removeAttribute(t):e.setAttribute(t,i?"":n)}}function Ly(e,t,n,r,s,i,a){if(t==="innerHTML"||t==="textContent"){r&&a(r,s,i),e[t]=n??"";return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){e._value=n;const u=o==="OPTION"?e.getAttribute("value"):e.value,c=n??"";u!==c&&(e.value=c),n==null&&e.removeAttribute(t);return}let l=!1;if(n===""||n==null){const u=typeof e[t];u==="boolean"?n=Sd(n):n==null&&u==="string"?(n="",l=!0):u==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(t)}function Ry(e,t,n,r){e.addEventListener(t,n,r)}function $y(e,t,n,r){e.removeEventListener(t,n,r)}function By(e,t,n,r,s=null){const i=e._vei||(e._vei={}),a=i[t];if(r&&a)a.value=r;else{const[o,l]=Hy(t);if(r){const u=i[t]=jy(r,s);Ry(e,o,u,l)}else a&&($y(e,o,a,l),i[t]=void 0)}}const Vu=/(?:Once|Passive|Capture)$/;function Hy(e){let t;if(Vu.test(e)){t={};let r;for(;r=e.match(Vu);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Nr(e.slice(2)),t]}let La=0;const Uy=Promise.resolve(),zy=()=>La||(Uy.then(()=>La=0),La=Date.now());function jy(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;kt(Wy(r,n.value),t,5,[r])};return n.value=e,n.attached=zy(),n}function Wy(e,t){if(ie(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Pu=/^on[a-z]/,qy=(e,t,n,r,s=!1,i,a,o,l)=>{t==="class"?My(e,r,s):t==="style"?Fy(e,n,r):Yi(t)?Yo(t)||By(e,t,n,r,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Zy(e,t,r,s))?Ly(e,t,r,i,a,o,l):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Ny(e,t,r,s))};function Zy(e,t,n,r){return r?!!(t==="innerHTML"||t==="textContent"||t in e&&Pu.test(t)&&me(n)):t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA"||Pu.test(t)&&$e(n)?!1:t in e}const xn="transition",Gr="animation",mn=(e,{slots:t})=>$n(Lg,pf(e),t);mn.displayName="Transition";const yf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Yy=mn.props=ze({},Gd,yf),Zn=(e,t=[])=>{ie(e)?e.forEach(n=>n(...t)):e&&e(...t)},Mu=e=>e?ie(e)?e.some(t=>t.length>1):e.length>1:!1;function pf(e){const t={};for(const P in e)P in yf||(t[P]=e[P]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:o=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=a,appearToClass:c=o,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,h=Gy(s),g=h&&h[0],y=h&&h[1],{onBeforeEnter:E,onEnter:_,onEnterCancelled:w,onLeave:O,onLeaveCancelled:I,onBeforeAppear:k=E,onAppear:C=_,onAppearCancelled:p=w}=t,T=(P,R,F)=>{kn(P,R?c:o),kn(P,R?u:a),F&&F()},V=(P,R)=>{P._isLeaving=!1,kn(P,d),kn(P,m),kn(P,f),R&&R()},$=P=>(R,F)=>{const G=P?C:_,Z=()=>T(R,P,F);Zn(G,[R,Z]),Fu(()=>{kn(R,P?l:i),rn(R,P?c:o),Mu(G)||Du(R,r,g,Z)})};return ze(t,{onBeforeEnter(P){Zn(E,[P]),rn(P,i),rn(P,a)},onBeforeAppear(P){Zn(k,[P]),rn(P,l),rn(P,u)},onEnter:$(!1),onAppear:$(!0),onLeave(P,R){P._isLeaving=!0;const F=()=>V(P,R);rn(P,d),wf(),rn(P,f),Fu(()=>{P._isLeaving&&(kn(P,d),rn(P,m),Mu(O)||Du(P,r,y,F))}),Zn(O,[P,F])},onEnterCancelled(P){T(P,!1),Zn(w,[P])},onAppearCancelled(P){T(P,!0),Zn(p,[P])},onLeaveCancelled(P){V(P),Zn(I,[P])}})}function Gy(e){if(e==null)return null;if(Pe(e))return[Ra(e.enter),Ra(e.leave)];{const t=Ra(e);return[t,t]}}function Ra(e){return Dv(e)}function rn(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e._vtc||(e._vtc=new Set)).add(t)}function kn(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Fu(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Ky=0;function Du(e,t,n,r){const s=e._endId=++Ky,i=()=>{s===e._endId&&r()};if(n)return setTimeout(i,n);const{type:a,timeout:o,propCount:l}=bf(e,t);if(!a)return r();const u=a+"end";let c=0;const d=()=>{e.removeEventListener(u,f),i()},f=m=>{m.target===e&&++c>=l&&d()};setTimeout(()=>{c<l&&d()},o+1),e.addEventListener(u,f)}function bf(e,t){const n=window.getComputedStyle(e),r=h=>(n[h]||"").split(", "),s=r(`${xn}Delay`),i=r(`${xn}Duration`),a=Nu(s,i),o=r(`${Gr}Delay`),l=r(`${Gr}Duration`),u=Nu(o,l);let c=null,d=0,f=0;t===xn?a>0&&(c=xn,d=a,f=i.length):t===Gr?u>0&&(c=Gr,d=u,f=l.length):(d=Math.max(a,u),c=d>0?a>u?xn:Gr:null,f=c?c===xn?i.length:l.length:0);const m=c===xn&&/\b(transform|all)(,|$)/.test(r(`${xn}Property`).toString());return{type:c,timeout:d,propCount:f,hasTransform:m}}function Nu(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Lu(n)+Lu(e[r])))}function Lu(e){return Number(e.slice(0,-1).replace(",","."))*1e3}function wf(){return document.body.offsetHeight}const Sf=new WeakMap,Cf=new WeakMap,xf={name:"TransitionGroup",props:ze({},Yy,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=gl(),r=Yd();let s,i;return Xd(()=>{if(!s.length)return;const a=e.moveClass||`${e.name||"v"}-move`;if(!np(s[0].el,n.vnode.el,a))return;s.forEach(Qy),s.forEach(ep);const o=s.filter(tp);wf(),o.forEach(l=>{const u=l.el,c=u.style;rn(u,a),c.transform=c.webkitTransform=c.transitionDuration="";const d=u._moveCb=f=>{f&&f.target!==u||(!f||/transform$/.test(f.propertyName))&&(u.removeEventListener("transitionend",d),u._moveCb=null,kn(u,a))};u.addEventListener("transitionend",d)})}),()=>{const a=ce(e),o=pf(a);let l=a.tag||ke;s=i,i=t.default?cl(t.default()):[];for(let u=0;u<i.length;u++){const c=i[u];c.key!=null&&ws(c,bs(c,o,r,n))}if(s)for(let u=0;u<s.length;u++){const c=s[u];ws(c,bs(c,o,r,n)),Sf.set(c,c.el.getBoundingClientRect())}return v(l,null,i)}}},Jy=e=>delete e.mode;xf.props;const Xy=xf;function Qy(e){const t=e.el;t._moveCb&&t._moveCb(),t._enterCb&&t._enterCb()}function ep(e){Cf.set(e,e.el.getBoundingClientRect())}function tp(e){const t=Sf.get(e),n=Cf.get(e),r=t.left-n.left,s=t.top-n.top;if(r||s){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${r}px,${s}px)`,i.transitionDuration="0s",e}}function np(e,t,n){const r=e.cloneNode();e._vtc&&e._vtc.forEach(a=>{a.split(/\s+/).forEach(o=>o&&r.classList.remove(o))}),n.split(/\s+/).forEach(a=>a&&r.classList.add(a)),r.style.display="none";const s=t.nodeType===1?t:t.parentNode;s.appendChild(r);const{hasTransform:i}=bf(r);return s.removeChild(r),i}const $r={beforeMount(e,{value:t},{transition:n}){e._vod=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Kr(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Kr(e,!0),r.enter(e)):r.leave(e,()=>{Kr(e,!1)}):Kr(e,t))},beforeUnmount(e,{value:t}){Kr(e,t)}};function Kr(e,t){e.style.display=t?e._vod:"none"}const rp=ze({patchProp:qy},Py);let Ru;function sp(){return Ru||(Ru=uy(rp))}const ip=(...e)=>{const t=sp().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=ap(r);if(!s)return;const i=t._component;!me(i)&&!i.render&&!i.template&&(i.template=s.innerHTML),s.innerHTML="";const a=n(s,!1,s instanceof SVGElement);return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),a},t};function ap(e){return $e(e)?document.querySelector(e):e}var op=!1;/*!
  * pinia v2.1.4
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */let _f;const aa=e=>_f=e,Ef=Symbol();function bo(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var cs;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(cs||(cs={}));function lp(){const e=Vs(!0),t=e.run(()=>K({}));let n=[],r=[];const s=Xi({install(i){aa(s),s._a=i,i.provide(Ef,s),i.config.globalProperties.$pinia=s,r.forEach(a=>n.push(a)),r=[]},use(i){return!this._a&&!op?r.push(i):n.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const Tf=()=>{};function $u(e,t,n,r=Tf){e.push(t);const s=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),r())};return!n&&_d()&&lt(s),s}function yr(e,...t){e.slice().forEach(n=>{n(...t)})}const up=e=>e();function wo(e,t){e instanceof Map&&t instanceof Map&&t.forEach((n,r)=>e.set(r,n)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];bo(s)&&bo(r)&&e.hasOwnProperty(n)&&!Oe(r)&&!ln(r)?e[n]=wo(s,r):e[n]=r}return e}const cp=Symbol();function dp(e){return!bo(e)||!e.hasOwnProperty(cp)}const{assign:On}=Object;function fp(e){return!!(Oe(e)&&e.effect)}function mp(e,t,n,r){const{state:s,actions:i,getters:a}=t,o=n.state.value[e];let l;function u(){o||(n.state.value[e]=s?s():{});const c=Qi(n.state.value[e]);return On(c,i,Object.keys(a||{}).reduce((d,f)=>(d[f]=Xi(S(()=>{aa(n);const m=n._s.get(e);return a[f].call(m,m)})),d),{}))}return l=kf(e,u,t,n,r,!0),l}function kf(e,t,n={},r,s,i){let a;const o=On({actions:{}},n),l={deep:!0};let u,c,d=[],f=[],m;const h=r.state.value[e];!i&&!h&&(r.state.value[e]={}),K({});let g;function y(p){let T;u=c=!1,typeof p=="function"?(p(r.state.value[e]),T={type:cs.patchFunction,storeId:e,events:m}):(wo(r.state.value[e],p),T={type:cs.patchObject,payload:p,storeId:e,events:m});const V=g=Symbol();ut().then(()=>{g===V&&(u=!0)}),c=!0,yr(d,T,r.state.value[e])}const E=i?function(){const{state:T}=n,V=T?T():{};this.$patch($=>{On($,V)})}:Tf;function _(){a.stop(),d=[],f=[],r._s.delete(e)}function w(p,T){return function(){aa(r);const V=Array.from(arguments),$=[],P=[];function R(Z){$.push(Z)}function F(Z){P.push(Z)}yr(f,{args:V,name:p,store:I,after:R,onError:F});let G;try{G=T.apply(this&&this.$id===e?this:I,V)}catch(Z){throw yr(P,Z),Z}return G instanceof Promise?G.then(Z=>(yr($,Z),Z)).catch(Z=>(yr(P,Z),Promise.reject(Z))):(yr($,G),G)}}const O={_p:r,$id:e,$onAction:$u.bind(null,f),$patch:y,$reset:E,$subscribe(p,T={}){const V=$u(d,p,T.detached,()=>$()),$=a.run(()=>he(()=>r.state.value[e],P=>{(T.flush==="sync"?c:u)&&p({storeId:e,type:cs.direct,events:m},P)},On({},l,T)));return V},$dispose:_},I=nt(O);r._s.set(e,I);const k=r._a&&r._a.runWithContext||up,C=r._e.run(()=>(a=Vs(),k(()=>a.run(t))));for(const p in C){const T=C[p];if(Oe(T)&&!fp(T)||ln(T))i||(h&&dp(T)&&(Oe(T)?T.value=h[p]:wo(T,h[p])),r.state.value[e][p]=T);else if(typeof T=="function"){const V=w(p,T);C[p]=V,o.actions[p]=T}}return On(I,C),On(ce(I),C),Object.defineProperty(I,"$state",{get:()=>r.state.value[e],set:p=>{y(T=>{On(T,p)})}}),r._p.forEach(p=>{On(I,a.run(()=>p({store:I,app:r._a,pinia:r,options:o})))}),h&&i&&n.hydrate&&n.hydrate(I.$state,h),u=!0,c=!0,I}function hp(e,t,n){let r,s;const i=typeof t=="function";typeof e=="string"?(r=e,s=i?n:t):(s=e,r=e.id);function a(o,l){const u=ry();return o=o||(u?Re(Ef,null):null),o&&aa(o),o=_f,o._s.has(r)||(i?kf(r,t,s,o):mp(r,s,o)),o._s.get(r)}return a.$id=r,a}function vp(e){{e=ce(e);const t={};for(const n in e){const r=e[n];(Oe(r)||ln(r))&&(t[n]=de(e,n))}return t}}const pl=hp({id:"snackbar",state:()=>({snackbar:!1,timeout:3e3,msg:"--",color:"success",icon:null}),getters:{},actions:{async success(e){this.color="success",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-check-circle"},async error(e){this.color="error",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-close-circle"},async warning(e){console.log("pinia warnign sanckbar"),this.color="warning",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-alert-circle"},async info(e){this.color="info",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-information"},async show(e){console.log("ACTION MOSTRAR SNACKBAR",e),this.color=e.color||"success",this.msg=e.msg||"prueba---",this.timeout=!!e.tiempo||3e3,this.snackbar=!0},async hide(){this.snackbar=!1,this.msg="",this.color="success",this.icon=""}}});function Of(e,t,n){const r=t.length-1;if(r<0)return e===void 0?n:e;for(let s=0;s<r;s++){if(e==null)return n;e=e[t[s]]}return e==null||e[t[r]]===void 0?n:e[t[r]]}function Br(e,t){if(e===t)return!0;if(e instanceof Date&&t instanceof Date&&e.getTime()!==t.getTime()||e!==Object(e)||t!==Object(t))return!1;const n=Object.keys(e);return n.length!==Object.keys(t).length?!1:n.every(r=>Br(e[r],t[r]))}function So(e,t,n){return e==null||!t||typeof t!="string"?n:e[t]!==void 0?e[t]:(t=t.replace(/\[(\w+)\]/g,".$1"),t=t.replace(/^\./,""),Of(e,t.split("."),n))}function an(e,t,n){if(t==null)return e===void 0?n:e;if(e!==Object(e)){if(typeof t!="function")return n;const s=t(e,n);return typeof s>"u"?n:s}if(typeof t=="string")return So(e,t,n);if(Array.isArray(t))return Of(e,t,n);if(typeof t!="function")return n;const r=t(e,n);return typeof r>"u"?n:r}function bl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Array.from({length:e},(n,r)=>t+r)}function ae(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"px";if(!(e==null||e===""))return isNaN(+e)?String(e):isFinite(+e)?`${Number(e)}${t}`:void 0}function Co(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function xo(e){return e&&"$el"in e?e.$el:e}const Bu=Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16});function $a(e,t){return t.every(n=>e.hasOwnProperty(n))}function Fs(e,t,n){const r=Object.create(null),s=Object.create(null);for(const i in e)t.some(a=>a instanceof RegExp?a.test(i):a===i)&&!(n!=null&&n.some(a=>a===i))?r[i]=e[i]:s[i]=e[i];return[r,s]}function Ds(e,t){const n={...e};return t.forEach(r=>delete n[r]),n}function wl(e){return Fs(e,["class","style","id",/^data-/])}function Dn(e){return e==null?[]:Array.isArray(e)?e:[e]}function _s(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;return Math.max(t,Math.min(n,e))}function Hu(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0";return e+n.repeat(Math.max(0,t-e.length))}function gp(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const n=[];let r=0;for(;r<e.length;)n.push(e.substr(r,t)),r+=t;return n}function Et(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const r={};for(const s in e)r[s]=e[s];for(const s in t){const i=e[s],a=t[s];if(Co(i)&&Co(a)){r[s]=Et(i,a,n);continue}if(Array.isArray(i)&&Array.isArray(a)&&n){r[s]=n(i,a);continue}r[s]=a}return r}function If(e){return e.map(t=>t.type===ke?If(t.children):t).flat()}function ar(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";if(ar.cache.has(e))return ar.cache.get(e);const t=e.replace(/[^a-z]/gi,"-").replace(/\B([A-Z])/g,"-$1").toLowerCase();return ar.cache.set(e,t),t}ar.cache=new Map;function ds(e,t){if(!t||typeof t!="object")return[];if(Array.isArray(t))return t.map(n=>ds(e,n)).flat(1);if(Array.isArray(t.children))return t.children.map(n=>ds(e,n)).flat(1);if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component];if(t.component.subTree)return ds(e,t.component.subTree).flat(1)}return[]}function Sl(e){const t=nt({}),n=S(e);return gn(()=>{for(const r in n.value)t[r]=n.value[r]},{flush:"sync"}),Qi(t)}function Vi(e,t){return e.includes(t)}const yp=/^on[^a-z]/,Cl=e=>yp.test(e);function Af(e){return e[2].toLowerCase()+e.slice(3)}const Qt=()=>[Function,Array];function Uu(e,t){return t="on"+vn(t),!!(e[t]||e[`${t}Once`]||e[`${t}Capture`]||e[`${t}OnceCapture`]||e[`${t}CaptureOnce`])}function pp(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(Array.isArray(e))for(const s of e)s(...n);else typeof e=="function"&&e(...n)}function Vf(e){const t=["button","[href]",'input:not([type="hidden"])',"select","textarea","[tabindex]"].map(n=>`${n}:not([tabindex="-1"]):not([disabled])`).join(", ");return[...e.querySelectorAll(t)]}function Pi(e,t){var s,i,a;const n=Vf(e),r=n.indexOf(document.activeElement);if(!t)(e===document.activeElement||!e.contains(document.activeElement))&&((s=n[0])==null||s.focus());else if(t==="first")(i=n[0])==null||i.focus();else if(t==="last")(a=n.at(-1))==null||a.focus();else{let o,l=r;const u=t==="next"?1:-1;do l+=u,o=n[l];while((!o||o.offsetParent==null)&&l<n.length&&l>=0);o?o.focus():Pi(e,t==="next"?"first":"last")}}const Pf=["top","bottom"],bp=["start","end","left","right"];function _o(e,t){let[n,r]=e.split(" ");return r||(r=Vi(Pf,n)?"start":Vi(bp,n)?"top":"center"),{side:zu(n,t),align:zu(r,t)}}function zu(e,t){return e==="start"?t?"right":"left":e==="end"?t?"left":"right":e}function Ba(e){return{side:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.side],align:e.align}}function Ha(e){return{side:e.side,align:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.align]}}function ju(e){return{side:e.align,align:e.side}}function Wu(e){return Vi(Pf,e.side)?"y":"x"}class Or{constructor(t){let{x:n,y:r,width:s,height:i}=t;this.x=n,this.y=r,this.width=s,this.height=i}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}}function qu(e,t){return{x:{before:Math.max(0,t.left-e.left),after:Math.max(0,e.right-t.right)},y:{before:Math.max(0,t.top-e.top),after:Math.max(0,e.bottom-t.bottom)}}}function xl(e){const t=e.getBoundingClientRect(),n=getComputedStyle(e),r=n.transform;if(r){let s,i,a,o,l;if(r.startsWith("matrix3d("))s=r.slice(9,-1).split(/, /),i=+s[0],a=+s[5],o=+s[12],l=+s[13];else if(r.startsWith("matrix("))s=r.slice(7,-1).split(/, /),i=+s[0],a=+s[3],o=+s[4],l=+s[5];else return new Or(t);const u=n.transformOrigin,c=t.x-o-(1-i)*parseFloat(u),d=t.y-l-(1-a)*parseFloat(u.slice(u.indexOf(" ")+1)),f=i?t.width/i:e.offsetWidth+1,m=a?t.height/a:e.offsetHeight+1;return new Or({x:c,y:d,width:f,height:m})}else return new Or(t)}function Cr(e,t,n){if(typeof e.animate>"u")return{finished:Promise.resolve()};let r;try{r=e.animate(t,n)}catch{return{finished:Promise.resolve()}}return typeof r.finished>"u"&&(r.finished=new Promise(s=>{r.onfinish=()=>{s(r)}})),r}const yi=new WeakMap;function wp(e,t){Object.keys(t).forEach(n=>{if(Cl(n)){const r=Af(n),s=yi.get(e);if(t[n]==null)s==null||s.forEach(i=>{const[a,o]=i;a===r&&(e.removeEventListener(r,o),s.delete(i))});else if(!s||![...s].some(i=>i[0]===r&&i[1]===t[n])){e.addEventListener(r,t[n]);const i=s||new Set;i.add([r,t[n]]),yi.has(e)||yi.set(e,i)}}else t[n]==null?e.removeAttribute(n):e.setAttribute(n,t[n])})}function Sp(e,t){Object.keys(t).forEach(n=>{if(Cl(n)){const r=Af(n),s=yi.get(e);s==null||s.forEach(i=>{const[a,o]=i;a===r&&(e.removeEventListener(r,o),s.delete(i))})}else e.removeAttribute(n)})}function Cp(e,t){t=Array.isArray(t)?t.slice(0,-1).map(n=>`'${n}'`).join(", ")+` or '${t.at(-1)}'`:`'${t}'`}const Mi=.20689655172413793,xp=e=>e>Mi**3?Math.cbrt(e):e/(3*Mi**2)+4/29,_p=e=>e>Mi?e**3:3*Mi**2*(e-4/29);function Mf(e){const t=xp,n=t(e[1]);return[116*n-16,500*(t(e[0]/.95047)-n),200*(n-t(e[2]/1.08883))]}function Ff(e){const t=_p,n=(e[0]+16)/116;return[t(n+e[1]/500)*.95047,t(n),t(n-e[2]/200)*1.08883]}const Ep=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],Tp=e=>e<=.0031308?e*12.92:1.055*e**(1/2.4)-.055,kp=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],Op=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function Df(e){const t=Array(3),n=Tp,r=Ep;for(let s=0;s<3;++s)t[s]=Math.round(_s(n(r[s][0]*e[0]+r[s][1]*e[1]+r[s][2]*e[2]))*255);return{r:t[0],g:t[1],b:t[2]}}function _l(e){let{r:t,g:n,b:r}=e;const s=[0,0,0],i=Op,a=kp;t=i(t/255),n=i(n/255),r=i(r/255);for(let o=0;o<3;++o)s[o]=a[o][0]*t+a[o][1]*n+a[o][2]*r;return s}function Zu(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}const Yu=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,Ip={rgb:(e,t,n,r)=>({r:e,g:t,b:n,a:r}),rgba:(e,t,n,r)=>({r:e,g:t,b:n,a:r}),hsl:(e,t,n,r)=>Gu({h:e,s:t,l:n,a:r}),hsla:(e,t,n,r)=>Gu({h:e,s:t,l:n,a:r}),hsv:(e,t,n,r)=>Es({h:e,s:t,v:n,a:r}),hsva:(e,t,n,r)=>Es({h:e,s:t,v:n,a:r})};function rr(e){if(typeof e=="number")return{r:(e&16711680)>>16,g:(e&65280)>>8,b:e&255};if(typeof e=="string"&&Yu.test(e)){const{groups:t}=e.match(Yu),{fn:n,values:r}=t,s=r.split(/,\s*/).map(i=>i.endsWith("%")&&["hsl","hsla","hsv","hsva"].includes(n)?parseFloat(i)/100:parseFloat(i));return Ip[n](...s)}else if(typeof e=="string"){let t=e.startsWith("#")?e.slice(1):e;return[3,4].includes(t.length)?t=t.split("").map(n=>n+n).join(""):[6,8].includes(t.length),Vp(t)}else if(typeof e=="object"){if($a(e,["r","g","b"]))return e;if($a(e,["h","s","l"]))return Es(Nf(e));if($a(e,["h","s","v"]))return Es(e)}throw new TypeError(`Invalid color: ${e==null?e:String(e)||e.constructor.name}
Expected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function Es(e){const{h:t,s:n,v:r,a:s}=e,i=o=>{const l=(o+t/60)%6;return r-r*n*Math.max(Math.min(l,4-l,1),0)},a=[i(5),i(3),i(1)].map(o=>Math.round(o*255));return{r:a[0],g:a[1],b:a[2],a:s}}function Gu(e){return Es(Nf(e))}function Nf(e){const{h:t,s:n,l:r,a:s}=e,i=r+n*Math.min(r,1-r),a=i===0?0:2-2*r/i;return{h:t,s:a,v:i,a:s}}function si(e){const t=Math.round(e).toString(16);return("00".substr(0,2-t.length)+t).toUpperCase()}function Ap(e){let{r:t,g:n,b:r,a:s}=e;return`#${[si(t),si(n),si(r),s!==void 0?si(Math.round(s*255)):""].join("")}`}function Vp(e){e=Pp(e);let[t,n,r,s]=gp(e,2).map(i=>parseInt(i,16));return s=s===void 0?s:s/255,{r:t,g:n,b:r,a:s}}function Pp(e){return e.startsWith("#")&&(e=e.slice(1)),e=e.replace(/([^0-9a-f])/gi,"F"),(e.length===3||e.length===4)&&(e=e.split("").map(t=>t+t).join("")),e.length!==6&&(e=Hu(Hu(e,6),8,"F")),e}function Mp(e,t){const n=Mf(_l(e));return n[0]=n[0]+t*10,Df(Ff(n))}function Fp(e,t){const n=Mf(_l(e));return n[0]=n[0]-t*10,Df(Ff(n))}function Dp(e){const t=rr(e);return _l(t)[1]}function z(e,t){return n=>Object.keys(e).reduce((r,s)=>{const a=typeof e[s]=="object"&&e[s]!=null&&!Array.isArray(e[s])?e[s]:{type:e[s]};return n&&s in n?r[s]={...a,default:n[s]}:r[s]=a,t&&!r[s].source&&(r[s].source=t),r},{})}const ye=z({class:[String,Array],style:{type:[String,Array,Object],default:null}},"component");function Ln(e,t){let n;function r(){n=Vs(),n.run(()=>t.length?t(()=>{n==null||n.stop(),r()}):t())}he(e,s=>{s&&!n?r():s||(n==null||n.stop(),n=void 0)},{immediate:!0}),lt(()=>{n==null||n.stop()})}const Ts=Symbol.for("vuetify:defaults");function Np(e){return K(e)}function El(){const e=Re(Ts);if(!e)throw new Error("[Vuetify] Could not find defaults instance");return e}function Bn(e,t){const n=El(),r=K(e),s=S(()=>{if(tt(t==null?void 0:t.disabled))return n.value;const a=tt(t==null?void 0:t.scoped),o=tt(t==null?void 0:t.reset),l=tt(t==null?void 0:t.root);let u=Et(r.value,{prev:n.value});if(a)return u;if(o||l){const c=Number(o||1/0);for(let d=0;d<=c&&!(!u||!("prev"in u));d++)u=u.prev;return u&&typeof l=="string"&&l in u&&(u=Et(Et(u,{prev:u}),u[l])),u}return u.prev?Et(u.prev,u):u});return wt(Ts,s),s}function Lp(e,t){var n,r;return typeof((n=e.props)==null?void 0:n[t])<"u"||typeof((r=e.props)==null?void 0:r[ar(t)])<"u"}function Rp(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:El();const r=it("useDefaults");if(t=t??r.type.name??r.type.__name,!t)throw new Error("[Vuetify] Could not determine component name");const s=S(()=>{var l;return(l=n.value)==null?void 0:l[e._as??t]}),i=new Proxy(e,{get(l,u){var d,f,m,h;const c=Reflect.get(l,u);return u==="class"||u==="style"?[(d=s.value)==null?void 0:d[u],c].filter(g=>g!=null):typeof u=="string"&&!Lp(r.vnode,u)?((f=s.value)==null?void 0:f[u])??((h=(m=n.value)==null?void 0:m.global)==null?void 0:h[u])??c:c}}),a=be();gn(()=>{if(s.value){const l=Object.entries(s.value).filter(u=>{let[c]=u;return c.startsWith(c[0].toUpperCase())});l.length&&(a.value=Object.fromEntries(l))}});function o(){Ln(a,()=>{var l;Bn(Et(((l=zp(Ts))==null?void 0:l.value)??{},a.value))})}return{props:i,provideSubDefaults:o}}function Hr(e){if(e._setup=e._setup??e.setup,!e.name)return e;if(e._setup){e.props=z(e.props??{},e.name)();const t=Object.keys(e.props);e.filterProps=function(r){return Fs(r,t,["class","style"])},e.props._as=String,e.setup=function(r,s){const i=El();if(!i.value)return e._setup(r,s);const{props:a,provideSubDefaults:o}=Rp(r,r._as??e.name,i),l=e._setup(a,s);return o(),l}}return e}function se(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return t=>(e?Hr:Rg)(t)}function lr(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"div",n=arguments.length>2?arguments[2]:void 0;return se()({name:n??vn(It(e.replace(/__/g,"-"))),props:{tag:{type:String,default:t},...ye()},setup(r,s){let{slots:i}=s;return()=>{var a;return $n(r.tag,{class:[e,r.class],style:r.style},(a=i.default)==null?void 0:a.call(i))}}})}function Lf(e){if(typeof e.getRootNode!="function"){for(;e.parentNode;)e=e.parentNode;return e!==document?null:document}const t=e.getRootNode();return t!==document&&t.getRootNode({composed:!0})!==document?null:t}const Fi="cubic-bezier(0.4, 0, 0.2, 1)",$p="cubic-bezier(0.0, 0, 0.2, 1)",Bp="cubic-bezier(0.4, 0, 1, 1)";function it(e,t){const n=gl();if(!n)throw new Error(`[Vuetify] ${e} ${t||"must be called from inside a setup function"}`);return n}function wn(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"composables";const t=it(e).type;return ar((t==null?void 0:t.aliasName)||(t==null?void 0:t.name))}let Rf=0,pi=new WeakMap;function At(){const e=it("getUid");if(pi.has(e))return pi.get(e);{const t=Rf++;return pi.set(e,t),t}}At.reset=()=>{Rf=0,pi=new WeakMap};function $f(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(t?Hp(e):Tl(e))return e;e=e.parentElement}return document.scrollingElement}function Di(e,t){const n=[];if(t&&e&&!t.contains(e))return n;for(;e&&(Tl(e)&&n.push(e),e!==t);)e=e.parentElement;return n}function Tl(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return t.overflowY==="scroll"||t.overflowY==="auto"&&e.scrollHeight>e.clientHeight}function Hp(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return["scroll","auto"].includes(t.overflowY)}const Ge=typeof window<"u",kl=Ge&&"IntersectionObserver"in window,Up=Ge&&("ontouchstart"in window||window.navigator.maxTouchPoints>0),Eo=Ge&&typeof CSS<"u"&&typeof CSS.supports<"u"&&CSS.supports("selector(:focus-visible)");function zp(e){const{provides:t}=it("injectSelf");if(t&&e in t)return t[e]}function jp(e){for(;e;){if(window.getComputedStyle(e).position==="fixed")return!0;e=e.offsetParent}return!1}function ve(e){const t=it("useRender");t.render=e}const ur=z({border:[Boolean,Number,String]},"border");function cr(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();return{borderClasses:S(()=>{const r=Oe(e)?e.value:e.border,s=[];if(r===!0||r==="")s.push(`${t}--border`);else if(typeof r=="string"||r===0)for(const i of String(r).split(" "))s.push(`border-${i}`);return s})}}const Wp=[null,"default","comfortable","compact"],Vt=z({density:{type:String,default:"default",validator:e=>Wp.includes(e)}},"density");function qt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();return{densityClasses:S(()=>`${t}--density-${e.density}`)}}const Hn=z({elevation:{type:[Number,String],validator(e){const t=parseInt(e);return!isNaN(t)&&t>=0&&t<=24}}},"elevation");function Un(e){return{elevationClasses:S(()=>{const n=Oe(e)?e.value:e.elevation,r=[];return n==null||r.push(`elevation-${n}`),r})}}const Pt=z({rounded:{type:[Boolean,Number,String],default:void 0}},"rounded");function Mt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();return{roundedClasses:S(()=>{const r=Oe(e)?e.value:e.rounded,s=[];if(r===!0||r==="")s.push(`${t}--rounded`);else if(typeof r=="string"||r===0)for(const i of String(r).split(" "))s.push(`rounded-${i}`);return s})}}const Xe=z({tag:{type:String,default:"div"}},"tag"),pr=2.4,Ku=.2126729,Ju=.7151522,Xu=.072175,qp=.55,Zp=.58,Yp=.57,Gp=.62,ii=.03,Qu=1.45,Kp=5e-4,Jp=1.25,Xp=1.25,ec=.078,tc=12.82051282051282,ai=.06,nc=.001;function rc(e,t){const n=(e.r/255)**pr,r=(e.g/255)**pr,s=(e.b/255)**pr,i=(t.r/255)**pr,a=(t.g/255)**pr,o=(t.b/255)**pr;let l=n*Ku+r*Ju+s*Xu,u=i*Ku+a*Ju+o*Xu;if(l<=ii&&(l+=(ii-l)**Qu),u<=ii&&(u+=(ii-u)**Qu),Math.abs(u-l)<Kp)return 0;let c;if(u>l){const d=(u**qp-l**Zp)*Jp;c=d<nc?0:d<ec?d-d*tc*ai:d-ai}else{const d=(u**Gp-l**Yp)*Xp;c=d>-nc?0:d>-ec?d-d*tc*ai:d+ai}return c*100}const Ni=Symbol.for("vuetify:theme"),je=z({theme:String},"theme"),Jr={defaultTheme:"light",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#6200EE","primary-darken-1":"#3700B3",secondary:"#03DAC6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-variant":"#BDBDBD","on-surface-variant":"#424242",primary:"#BB86FC","primary-darken-1":"#3700B3",secondary:"#03DAC5","secondary-darken-1":"#03DAC5",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}}};function Qp(){var n,r;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Jr;if(!e)return{...Jr,isDisabled:!0};const t={};for(const[s,i]of Object.entries(e.themes??{})){const a=i.dark||s==="dark"?(n=Jr.themes)==null?void 0:n.dark:(r=Jr.themes)==null?void 0:r.light;t[s]=Et(a,i)}return Et(Jr,{...e,themes:t})}function eb(e){const t=Qp(e),n=K(t.defaultTheme),r=K(t.themes),s=S(()=>{const c={};for(const[d,f]of Object.entries(r.value)){const m=c[d]={...f,colors:{...f.colors}};if(t.variations)for(const h of t.variations.colors){const g=m.colors[h];if(g)for(const y of["lighten","darken"]){const E=y==="lighten"?Mp:Fp;for(const _ of bl(t.variations[y],1))m.colors[`${h}-${y}-${_}`]=Ap(E(rr(g),_))}}for(const h of Object.keys(m.colors)){if(/^on-[a-z]/.test(h)||m.colors[`on-${h}`])continue;const g=`on-${h}`,y=rr(m.colors[h]),E=Math.abs(rc(rr(0),y)),_=Math.abs(rc(rr(16777215),y));m.colors[g]=_>Math.min(E,50)?"#fff":"#000"}}return c}),i=S(()=>s.value[n.value]),a=S(()=>{const c=[];i.value.dark&&Yn(c,":root",["color-scheme: dark"]),Yn(c,":root",sc(i.value));for(const[h,g]of Object.entries(s.value))Yn(c,`.v-theme--${h}`,[`color-scheme: ${g.dark?"dark":"normal"}`,...sc(g)]);const d=[],f=[],m=new Set(Object.values(s.value).flatMap(h=>Object.keys(h.colors)));for(const h of m)/^on-[a-z]/.test(h)?Yn(f,`.${h}`,[`color: rgb(var(--v-theme-${h})) !important`]):(Yn(d,`.bg-${h}`,[`--v-theme-overlay-multiplier: var(--v-theme-${h}-overlay-multiplier)`,`background-color: rgb(var(--v-theme-${h})) !important`,`color: rgb(var(--v-theme-on-${h})) !important`]),Yn(f,`.text-${h}`,[`color: rgb(var(--v-theme-${h})) !important`]),Yn(f,`.border-${h}`,[`--v-border-color: var(--v-theme-${h})`]));return c.push(...d,...f),c.map((h,g)=>g===0?h:`    ${h}`).join("")});function o(){return{style:[{children:a.value,id:"vuetify-theme-stylesheet",nonce:t.cspNonce||!1}]}}function l(c){if(t.isDisabled)return;const d=c._context.provides.usehead;if(d)if(d.push){const f=d.push(o);he(a,()=>{f.patch(o)})}else Ge?(d.addHeadObjs(S(o)),gn(()=>d.updateDOM())):d.addHeadObjs(o());else{let m=function(){if(typeof document<"u"&&!f){const h=document.createElement("style");h.type="text/css",h.id="vuetify-theme-stylesheet",t.cspNonce&&h.setAttribute("nonce",t.cspNonce),f=h,document.head.appendChild(f)}f&&(f.innerHTML=a.value)},f=Ge?document.getElementById("vuetify-theme-stylesheet"):null;he(a,m,{immediate:!0})}}const u=S(()=>t.isDisabled?void 0:`v-theme--${n.value}`);return{install:l,isDisabled:t.isDisabled,name:n,themes:r,current:i,computedThemes:s,themeClasses:u,styles:a,global:{name:n,current:i}}}function Qe(e){it("provideTheme");const t=Re(Ni,null);if(!t)throw new Error("Could not find Vuetify theme injection");const n=S(()=>e.theme??(t==null?void 0:t.name.value)),r=S(()=>t.isDisabled?void 0:`v-theme--${n.value}`),s={...t,name:n,themeClasses:r};return wt(Ni,s),s}function Yn(e,t,n){e.push(`${t} {
`,...n.map(r=>`  ${r};
`),`}
`)}function sc(e){const t=e.dark?2:1,n=e.dark?1:2,r=[];for(const[s,i]of Object.entries(e.colors)){const a=rr(i);r.push(`--v-theme-${s}: ${a.r},${a.g},${a.b}`),s.startsWith("on-")||r.push(`--v-theme-${s}-overlay-multiplier: ${Dp(i)>.18?t:n}`)}for(const[s,i]of Object.entries(e.variables)){const a=typeof i=="string"&&i.startsWith("#")?rr(i):void 0,o=a?`${a.r}, ${a.g}, ${a.b}`:void 0;r.push(`--v-${s}: ${o??i}`)}return r}function Ol(e){return Sl(()=>{const t=[],n={};return e.value.background&&(Zu(e.value.background)?n.backgroundColor=e.value.background:t.push(`bg-${e.value.background}`)),e.value.text&&(Zu(e.value.text)?(n.color=e.value.text,n.caretColor=e.value.text):t.push(`text-${e.value.text}`)),{colorClasses:t,colorStyles:n}})}function en(e,t){const n=S(()=>({text:Oe(e)?e.value:t?e[t]:null})),{colorClasses:r,colorStyles:s}=Ol(n);return{textColorClasses:r,textColorStyles:s}}function Pr(e,t){const n=S(()=>({background:Oe(e)?e.value:t?e[t]:null})),{colorClasses:r,colorStyles:s}=Ol(n);return{backgroundColorClasses:r,backgroundColorStyles:s}}const tb=["elevated","flat","tonal","outlined","text","plain"];function dr(e,t){return v(ke,null,[e&&v("span",{key:"overlay",class:`${t}__overlay`},null),v("span",{key:"underlay",class:`${t}__underlay`},null)])}const tn=z({color:String,variant:{type:String,default:"elevated",validator:e=>tb.includes(e)}},"variant");function fr(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();const n=S(()=>{const{variant:i}=tt(e);return`${t}--variant-${i}`}),{colorClasses:r,colorStyles:s}=Ol(S(()=>{const{variant:i,color:a}=tt(e);return{[["elevated","flat"].includes(i)?"background":"text"]:a}}));return{colorClasses:r,colorStyles:s,variantClasses:n}}const Bf=z({divided:Boolean,...ur(),...ye(),...Vt(),...Hn(),...Pt(),...Xe(),...je(),...tn()},"VBtnGroup"),ic=se()({name:"VBtnGroup",props:Bf(),setup(e,t){let{slots:n}=t;const{themeClasses:r}=Qe(e),{densityClasses:s}=qt(e),{borderClasses:i}=cr(e),{elevationClasses:a}=Un(e),{roundedClasses:o}=Mt(e);Bn({VBtn:{height:"auto",color:de(e,"color"),density:de(e,"density"),flat:!0,variant:de(e,"variant")}}),ve(()=>v(e.tag,{class:["v-btn-group",{"v-btn-group--divided":e.divided},r.value,i.value,s.value,a.value,o.value,e.class],style:e.style},n))}});function Be(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:d=>d,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:d=>d;const i=it("useProxiedModel"),a=K(e[t]!==void 0?e[t]:n),o=ar(t),u=S(o!==t?()=>{var d,f,m,h;return e[t],!!(((d=i.vnode.props)!=null&&d.hasOwnProperty(t)||(f=i.vnode.props)!=null&&f.hasOwnProperty(o))&&((m=i.vnode.props)!=null&&m.hasOwnProperty(`onUpdate:${t}`)||(h=i.vnode.props)!=null&&h.hasOwnProperty(`onUpdate:${o}`)))}:()=>{var d,f;return e[t],!!((d=i.vnode.props)!=null&&d.hasOwnProperty(t)&&((f=i.vnode.props)!=null&&f.hasOwnProperty(`onUpdate:${t}`)))});Ln(()=>!u.value,()=>{he(()=>e[t],d=>{a.value=d})});const c=S({get(){const d=e[t];return r(u.value?d:a.value)},set(d){const f=s(d),m=ce(u.value?e[t]:a.value);m===f||r(m)===d||(a.value=f,i==null||i.emit(`update:${t}`,f))}});return Object.defineProperty(c,"externalValue",{get:()=>u.value?e[t]:a.value}),c}const Hf=z({modelValue:{type:null,default:void 0},multiple:Boolean,mandatory:[Boolean,String],max:Number,selectedClass:String,disabled:Boolean},"group"),Uf=z({value:null,disabled:Boolean,selectedClass:String},"group-item");function zf(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;const r=it("useGroupItem");if(!r)throw new Error("[Vuetify] useGroupItem composable must be used inside a component setup function");const s=At();wt(Symbol.for(`${t.description}:id`),s);const i=Re(t,null);if(!i){if(!n)return i;throw new Error(`[Vuetify] Could not find useGroup injection with symbol ${t.description}`)}const a=de(e,"value"),o=S(()=>i.disabled.value||e.disabled);i.register({id:s,value:a,disabled:o},r),bn(()=>{i.unregister(s)});const l=S(()=>i.isSelected(s)),u=S(()=>l.value&&[i.selectedClass.value,e.selectedClass]);return he(l,c=>{r.emit("group:selected",{value:c})}),{id:s,isSelected:l,toggle:()=>i.select(s,!l.value),select:c=>i.select(s,c),selectedClass:u,value:a,disabled:o,group:i}}function jf(e,t){let n=!1;const r=nt([]),s=Be(e,"modelValue",[],f=>f==null?[]:Wf(r,Dn(f)),f=>{const m=rb(r,f);return e.multiple?m:m[0]}),i=it("useGroup");function a(f,m){const h=f,g=Symbol.for(`${t.description}:id`),E=ds(g,i==null?void 0:i.vnode).indexOf(m);E>-1?r.splice(E,0,h):r.push(h)}function o(f){if(n)return;l();const m=r.findIndex(h=>h.id===f);r.splice(m,1)}function l(){const f=r.find(m=>!m.disabled);f&&e.mandatory==="force"&&!s.value.length&&(s.value=[f.id])}pn(()=>{l()}),bn(()=>{n=!0});function u(f,m){const h=r.find(g=>g.id===f);if(!(m&&(h!=null&&h.disabled)))if(e.multiple){const g=s.value.slice(),y=g.findIndex(_=>_===f),E=~y;if(m=m??!E,E&&e.mandatory&&g.length<=1||!E&&e.max!=null&&g.length+1>e.max)return;y<0&&m?g.push(f):y>=0&&!m&&g.splice(y,1),s.value=g}else{const g=s.value.includes(f);if(e.mandatory&&g)return;s.value=m??!g?[f]:[]}}function c(f){if(e.multiple,s.value.length){const m=s.value[0],h=r.findIndex(E=>E.id===m);let g=(h+f)%r.length,y=r[g];for(;y.disabled&&g!==h;)g=(g+f)%r.length,y=r[g];if(y.disabled)return;s.value=[r[g].id]}else{const m=r.find(h=>!h.disabled);m&&(s.value=[m.id])}}const d={register:a,unregister:o,selected:s,select:u,disabled:de(e,"disabled"),prev:()=>c(r.length-1),next:()=>c(1),isSelected:f=>s.value.includes(f),selectedClass:S(()=>e.selectedClass),items:S(()=>r),getItemIndex:f=>nb(r,f)};return wt(t,d),d}function nb(e,t){const n=Wf(e,[t]);return n.length?e.findIndex(r=>r.id===n[0]):-1}function Wf(e,t){const n=[];return t.forEach(r=>{const s=e.find(a=>Br(r,a.value)),i=e[r];(s==null?void 0:s.value)!=null?n.push(s.id):i!=null&&n.push(i.id)}),n}function rb(e,t){const n=[];return t.forEach(r=>{const s=e.findIndex(i=>i.id===r);if(~s){const i=e[s];n.push(i.value!=null?i.value:s)}}),n}const qf=Symbol.for("vuetify:v-btn-toggle"),sb=z({...Bf(),...Hf()},"VBtnToggle");se()({name:"VBtnToggle",props:sb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{isSelected:r,next:s,prev:i,select:a,selected:o}=jf(e,qf);return ve(()=>{const[l]=ic.filterProps(e);return v(ic,ue({class:["v-btn-toggle",e.class]},l,{style:e.style}),{default:()=>{var u;return[(u=n.default)==null?void 0:u.call(n,{isSelected:r,next:s,prev:i,select:a,selected:o})]}})}),{next:s,prev:i,select:a}}});const ib=z({defaults:Object,disabled:Boolean,reset:[Number,String],root:[Boolean,String],scoped:Boolean},"VDefaultsProvider"),Le=se(!1)({name:"VDefaultsProvider",props:ib(),setup(e,t){let{slots:n}=t;const{defaults:r,disabled:s,reset:i,root:a,scoped:o}=Qi(e);return Bn(r,{reset:i,root:a,scoped:o,disabled:s}),()=>{var l;return(l=n.default)==null?void 0:l.call(n)}}});const ab={collapse:"mdi-chevron-up",complete:"mdi-check",cancel:"mdi-close-circle",close:"mdi-close",delete:"mdi-close-circle",clear:"mdi-close-circle",success:"mdi-check-circle",info:"mdi-information",warning:"mdi-alert-circle",error:"mdi-close-circle",prev:"mdi-chevron-left",next:"mdi-chevron-right",checkboxOn:"mdi-checkbox-marked",checkboxOff:"mdi-checkbox-blank-outline",checkboxIndeterminate:"mdi-minus-box",delimiter:"mdi-circle",sortAsc:"mdi-arrow-up",sortDesc:"mdi-arrow-down",expand:"mdi-chevron-down",menu:"mdi-menu",subgroup:"mdi-menu-down",dropdown:"mdi-menu-down",radioOn:"mdi-radiobox-marked",radioOff:"mdi-radiobox-blank",edit:"mdi-pencil",ratingEmpty:"mdi-star-outline",ratingFull:"mdi-star",ratingHalf:"mdi-star-half-full",loading:"mdi-cached",first:"mdi-page-first",last:"mdi-page-last",unfold:"mdi-unfold-more-horizontal",file:"mdi-paperclip",plus:"mdi-plus",minus:"mdi-minus",calendar:"mdi-calendar"},ob={component:e=>$n(Yf,{...e,class:"mdi"})},Ae=[String,Function,Object,Array],To=Symbol.for("vuetify:icons"),oa=z({icon:{type:Ae},tag:{type:String,required:!0}},"icon"),ac=se()({name:"VComponentIcon",props:oa(),setup(e,t){let{slots:n}=t;return()=>{const r=e.icon;return v(e.tag,null,{default:()=>{var s;return[e.icon?v(r,null,null):(s=n.default)==null?void 0:s.call(n)]}})}}}),Zf=Hr({name:"VSvgIcon",inheritAttrs:!1,props:oa(),setup(e,t){let{attrs:n}=t;return()=>v(e.tag,ue(n,{style:null}),{default:()=>[v("svg",{class:"v-icon__svg",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true"},[Array.isArray(e.icon)?e.icon.map(r=>Array.isArray(r)?v("path",{d:r[0],"fill-opacity":r[1]},null):v("path",{d:r},null)):v("path",{d:e.icon},null)])]})}});Hr({name:"VLigatureIcon",props:oa(),setup(e){return()=>v(e.tag,null,{default:()=>[e.icon]})}});const Yf=Hr({name:"VClassIcon",props:oa(),setup(e){return()=>v(e.tag,{class:e.icon},null)}}),lb={svg:{component:Zf},class:{component:Yf}};function ub(e){return Et({defaultSet:"mdi",sets:{...lb,mdi:ob},aliases:{...ab,vuetify:["M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z",["M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z",.6]],"vuetify-outline":"svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z"}},e)}const cb=e=>{const t=Re(To);if(!t)throw new Error("Missing Vuetify Icons provide!");return{iconData:S(()=>{var l;const r=tt(e);if(!r)return{component:ac};let s=r;if(typeof s=="string"&&(s=s.trim(),s.startsWith("$")&&(s=(l=t.aliases)==null?void 0:l[s.slice(1)])),!s)throw new Error(`Could not find aliased icon "${r}"`);if(Array.isArray(s))return{component:Zf,icon:s};if(typeof s!="string")return{component:ac,icon:s};const i=Object.keys(t.sets).find(u=>typeof s=="string"&&s.startsWith(`${u}:`)),a=i?s.slice(i.length+1):s;return{component:t.sets[i??t.defaultSet].component,icon:a}})}},db=["x-small","small","default","large","x-large"],Ns=z({size:{type:[String,Number],default:"default"}},"size");function Ls(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();return Sl(()=>{let n,r;return Vi(db,e.size)?n=`${t}--size-${e.size}`:e.size&&(r={width:ae(e.size),height:ae(e.size)}),{sizeClasses:n,sizeStyles:r}})}const fb=z({color:String,start:Boolean,end:Boolean,icon:Ae,...ye(),...Ns(),...Xe({tag:"i"}),...je()},"VIcon"),et=se()({name:"VIcon",props:fb(),setup(e,t){let{attrs:n,slots:r}=t;const s=K(),{themeClasses:i}=Qe(e),{iconData:a}=cb(S(()=>s.value||e.icon)),{sizeClasses:o}=Ls(e),{textColorClasses:l,textColorStyles:u}=en(de(e,"color"));return ve(()=>{var d,f;const c=(d=r.default)==null?void 0:d.call(r);return c&&(s.value=(f=If(c).filter(m=>m.type===Ms&&m.children&&typeof m.children=="string")[0])==null?void 0:f.children),v(a.value.component,{tag:e.tag,icon:a.value.icon,class:["v-icon","notranslate",i.value,o.value,l.value,{"v-icon--clickable":!!n.onClick,"v-icon--start":e.start,"v-icon--end":e.end},e.class],style:[o.value?void 0:{fontSize:ae(e.size),height:ae(e.size),width:ae(e.size)},u.value,e.style],role:n.onClick?"button":void 0,"aria-hidden":!n.onClick},{default:()=>[c]})}),{}}});function Gf(e,t){const n=K(),r=be(!1);if(kl){const s=new IntersectionObserver(i=>{e==null||e(i,s),r.value=!!i.find(a=>a.isIntersecting)},t);bn(()=>{s.disconnect()}),he(n,(i,a)=>{a&&(s.unobserve(a),r.value=!1),i&&s.observe(i)},{flush:"post"})}return{intersectionRef:n,isIntersecting:r}}function la(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"content";const n=K(),r=K();if(Ge){const s=new ResizeObserver(i=>{e==null||e(i,s),i.length&&(t==="content"?r.value=i[0].contentRect:r.value=i[0].target.getBoundingClientRect())});bn(()=>{s.disconnect()}),he(n,(i,a)=>{a&&(s.unobserve(xo(a)),r.value=void 0),i&&s.observe(xo(i))},{flush:"post"})}return{resizeRef:n,contentRect:Ps(r)}}const mb=z({bgColor:String,color:String,indeterminate:[Boolean,String],modelValue:{type:[Number,String],default:0},rotate:{type:[Number,String],default:0},width:{type:[Number,String],default:4},...ye(),...Ns(),...Xe({tag:"div"}),...je()},"VProgressCircular"),hb=se()({name:"VProgressCircular",props:mb(),setup(e,t){let{slots:n}=t;const r=20,s=2*Math.PI*r,i=K(),{themeClasses:a}=Qe(e),{sizeClasses:o,sizeStyles:l}=Ls(e),{textColorClasses:u,textColorStyles:c}=en(de(e,"color")),{textColorClasses:d,textColorStyles:f}=en(de(e,"bgColor")),{intersectionRef:m,isIntersecting:h}=Gf(),{resizeRef:g,contentRect:y}=la(),E=S(()=>Math.max(0,Math.min(100,parseFloat(e.modelValue)))),_=S(()=>Number(e.width)),w=S(()=>l.value?Number(e.size):y.value?y.value.width:Math.max(_.value,32)),O=S(()=>r/(1-_.value/w.value)*2),I=S(()=>_.value/w.value*O.value),k=S(()=>ae((100-E.value)/100*s));return gn(()=>{m.value=i.value,g.value=i.value}),ve(()=>v(e.tag,{ref:i,class:["v-progress-circular",{"v-progress-circular--indeterminate":!!e.indeterminate,"v-progress-circular--visible":h.value,"v-progress-circular--disable-shrink":e.indeterminate==="disable-shrink"},a.value,o.value,u.value,e.class],style:[l.value,c.value,e.style],role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.indeterminate?void 0:E.value},{default:()=>[v("svg",{style:{transform:`rotate(calc(-90deg + ${Number(e.rotate)}deg))`},xmlns:"http://www.w3.org/2000/svg",viewBox:`0 0 ${O.value} ${O.value}`},[v("circle",{class:["v-progress-circular__underlay",d.value],style:f.value,fill:"transparent",cx:"50%",cy:"50%",r,"stroke-width":I.value,"stroke-dasharray":s,"stroke-dashoffset":0},null),v("circle",{class:"v-progress-circular__overlay",fill:"transparent",cx:"50%",cy:"50%",r,"stroke-width":I.value,"stroke-dasharray":s,"stroke-dashoffset":k.value},null)]),n.default&&v("div",{class:"v-progress-circular__content"},[n.default({value:E.value})])]})),{}}}),zn=z({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},"dimension");function jn(e){return{dimensionStyles:S(()=>({height:ae(e.height),maxHeight:ae(e.maxHeight),maxWidth:ae(e.maxWidth),minHeight:ae(e.minHeight),minWidth:ae(e.minWidth),width:ae(e.width)}))}}const vb={badge:"Badge",close:"Close",dataIterator:{noResultsText:"No matching records found",loadingText:"Loading items..."},dataTable:{itemsPerPageText:"Rows per page:",ariaLabel:{sortDescending:"Sorted descending.",sortAscending:"Sorted ascending.",sortNone:"Not sorted.",activateNone:"Activate to remove sorting.",activateDescending:"Activate to sort descending.",activateAscending:"Activate to sort ascending."},sortBy:"Sort by"},dataFooter:{itemsPerPageText:"Items per page:",itemsPerPageAll:"All",nextPage:"Next page",prevPage:"Previous page",firstPage:"First page",lastPage:"Last page",pageText:"{0}-{1} of {2}"},dateRangeInput:{divider:"to"},datePicker:{ok:"OK",cancel:"Cancel",range:{title:"Select dates",header:"Enter dates"},title:"Select date",header:"Enter date",input:{placeholder:"Enter date"}},noDataText:"No data available",carousel:{prev:"Previous visual",next:"Next visual",ariaLabel:{delimiter:"Carousel slide {0} of {1}"}},calendar:{moreEvents:"{0} more"},input:{clear:"Clear {0}",prependAction:"{0} prepended action",appendAction:"{0} appended action"},fileInput:{counter:"{0} files",counterSize:"{0} files ({1} in total)"},timePicker:{am:"AM",pm:"PM"},pagination:{ariaLabel:{root:"Pagination Navigation",next:"Next page",previous:"Previous page",page:"Go to page {0}",currentPage:"Page {0}, Current page",first:"First page",last:"Last page"}},rating:{ariaLabel:{item:"Rating {0} of {1}"}},loading:"Loading...",infiniteScroll:{loadMore:"Load more",empty:"No more"}},gb={af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1},oc="$vuetify.",lc=(e,t)=>e.replace(/\{(\d+)\}/g,(n,r)=>String(t[+r])),Kf=(e,t,n)=>function(r){for(var s=arguments.length,i=new Array(s>1?s-1:0),a=1;a<s;a++)i[a-1]=arguments[a];if(!r.startsWith(oc))return lc(r,i);const o=r.replace(oc,""),l=e.value&&n.value[e.value],u=t.value&&n.value[t.value];let c=So(l,o,null);return c||(`${r}${e.value}`,c=So(u,o,null)),c||(c=r),typeof c!="string"&&(c=r),lc(c,i)};function Jf(e,t){return(n,r)=>new Intl.NumberFormat([e.value,t.value],r).format(n)}function Ua(e,t,n){const r=Be(e,t,e[t]??n.value);return r.value=e[t]??n.value,he(n,s=>{e[t]==null&&(r.value=n.value)}),r}function Xf(e){return t=>{const n=Ua(t,"locale",e.current),r=Ua(t,"fallback",e.fallback),s=Ua(t,"messages",e.messages);return{name:"vuetify",current:n,fallback:r,messages:s,t:Kf(n,r,s),n:Jf(n,r),provide:Xf({current:n,fallback:r,messages:s})}}}function yb(e){const t=be((e==null?void 0:e.locale)??"en"),n=be((e==null?void 0:e.fallback)??"en"),r=K({en:vb,...e==null?void 0:e.messages});return{name:"vuetify",current:t,fallback:n,messages:r,t:Kf(t,n,r),n:Jf(t,n),provide:Xf({current:t,fallback:n,messages:r})}}const Li=Symbol.for("vuetify:locale");function pb(e){return e.name!=null}function bb(e){const t=e!=null&&e.adapter&&pb(e==null?void 0:e.adapter)?e==null?void 0:e.adapter:yb(e),n=wb(t,e);return{...t,...n}}function ua(){const e=Re(Li);if(!e)throw new Error("[Vuetify] Could not find injected locale instance");return e}function wb(e,t){const n=K((t==null?void 0:t.rtl)??gb),r=S(()=>n.value[e.current.value]??!1);return{isRtl:r,rtl:n,rtlClasses:S(()=>`v-locale--is-${r.value?"rtl":"ltr"}`)}}function mr(){const e=Re(Li);if(!e)throw new Error("[Vuetify] Could not find injected rtl instance");return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}const uc={center:"center",top:"bottom",bottom:"top",left:"right",right:"left"},Rs=z({location:String},"location");function $s(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2?arguments[2]:void 0;const{isRtl:r}=mr();return{locationStyles:S(()=>{if(!e.location)return{};const{side:i,align:a}=_o(e.location.split(" ").length>1?e.location:`${e.location} center`,r.value);function o(u){return n?n(u):0}const l={};return i!=="center"&&(t?l[uc[i]]=`calc(100% - ${o(i)}px)`:l[i]=0),a!=="center"?t?l[uc[a]]=`calc(100% - ${o(a)}px)`:l[a]=0:(i==="center"?l.top=l.left="50%":l[{top:"left",bottom:"left",left:"top",right:"top"}[i]]="50%",l.transform={top:"translateX(-50%)",bottom:"translateX(-50%)",left:"translateY(-50%)",right:"translateY(-50%)",center:"translate(-50%, -50%)"}[i]),l})}}const Sb=z({absolute:Boolean,active:{type:Boolean,default:!0},bgColor:String,bgOpacity:[Number,String],bufferValue:{type:[Number,String],default:0},clickable:Boolean,color:String,height:{type:[Number,String],default:4},indeterminate:Boolean,max:{type:[Number,String],default:100},modelValue:{type:[Number,String],default:0},reverse:Boolean,stream:Boolean,striped:Boolean,roundedBar:Boolean,...ye(),...Rs({location:"top"}),...Pt(),...Xe(),...je()},"VProgressLinear"),Cb=se()({name:"VProgressLinear",props:Sb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"modelValue"),{isRtl:s,rtlClasses:i}=mr(),{themeClasses:a}=Qe(e),{locationStyles:o}=$s(e),{textColorClasses:l,textColorStyles:u}=en(e,"color"),{backgroundColorClasses:c,backgroundColorStyles:d}=Pr(S(()=>e.bgColor||e.color)),{backgroundColorClasses:f,backgroundColorStyles:m}=Pr(e,"color"),{roundedClasses:h}=Mt(e),{intersectionRef:g,isIntersecting:y}=Gf(),E=S(()=>parseInt(e.max,10)),_=S(()=>parseInt(e.height,10)),w=S(()=>parseFloat(e.bufferValue)/E.value*100),O=S(()=>parseFloat(r.value)/E.value*100),I=S(()=>s.value!==e.reverse),k=S(()=>e.indeterminate?"fade-transition":"slide-x-transition"),C=S(()=>e.bgOpacity==null?e.bgOpacity:parseFloat(e.bgOpacity));function p(T){if(!g.value)return;const{left:V,right:$,width:P}=g.value.getBoundingClientRect(),R=I.value?P-T.clientX+($-P):T.clientX-V;r.value=Math.round(R/P*E.value)}return ve(()=>v(e.tag,{ref:g,class:["v-progress-linear",{"v-progress-linear--absolute":e.absolute,"v-progress-linear--active":e.active&&y.value,"v-progress-linear--reverse":I.value,"v-progress-linear--rounded":e.rounded,"v-progress-linear--rounded-bar":e.roundedBar,"v-progress-linear--striped":e.striped},h.value,a.value,i.value,e.class],style:[{bottom:e.location==="bottom"?0:void 0,top:e.location==="top"?0:void 0,height:e.active?ae(_.value):0,"--v-progress-linear-height":ae(_.value),...o.value},e.style],role:"progressbar","aria-hidden":e.active?"false":"true","aria-valuemin":"0","aria-valuemax":e.max,"aria-valuenow":e.indeterminate?void 0:O.value,onClick:e.clickable&&p},{default:()=>[e.stream&&v("div",{key:"stream",class:["v-progress-linear__stream",l.value],style:{...u.value,[I.value?"left":"right"]:ae(-_.value),borderTop:`${ae(_.value/2)} dotted`,opacity:C.value,top:`calc(50% - ${ae(_.value/4)})`,width:ae(100-w.value,"%"),"--v-progress-linear-stream-to":ae(_.value*(I.value?1:-1))}},null),v("div",{class:["v-progress-linear__background",c.value],style:[d.value,{opacity:C.value,width:ae(e.stream?w.value:100,"%")}]},null),v(mn,{name:k.value},{default:()=>[e.indeterminate?v("div",{class:"v-progress-linear__indeterminate"},[["long","short"].map(T=>v("div",{key:T,class:["v-progress-linear__indeterminate",T,f.value],style:m.value},null))]):v("div",{class:["v-progress-linear__determinate",f.value],style:[m.value,{width:ae(O.value,"%")}]},null)]}),n.default&&v("div",{class:"v-progress-linear__content"},[n.default({value:O.value,buffer:w.value})])]})),{}}}),Il=z({loading:[Boolean,String]},"loader");function Al(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();return{loaderClasses:S(()=>({[`${t}--loading`]:e.loading}))}}function Qf(e,t){var r;let{slots:n}=t;return v("div",{class:`${e.name}__loader`},[((r=n.default)==null?void 0:r.call(n,{color:e.color,isActive:e.active}))||v(Cb,{active:e.active,color:e.color,height:"2",indeterminate:!0},null)])}const xb=["static","relative","fixed","absolute","sticky"],ca=z({position:{type:String,validator:e=>xb.includes(e)}},"position");function da(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();return{positionClasses:S(()=>e.position?`${t}--${e.position}`:void 0)}}function _b(){var e,t;return(t=(e=it("useRouter"))==null?void 0:e.proxy)==null?void 0:t.$router}function fa(e,t){const n=Yg("RouterLink"),r=S(()=>!!(e.href||e.to)),s=S(()=>(r==null?void 0:r.value)||Uu(t,"click")||Uu(e,"click"));if(typeof n=="string")return{isLink:r,isClickable:s,href:de(e,"href")};const i=e.to?n.useLink(e):void 0;return{isLink:r,isClickable:s,route:i==null?void 0:i.route,navigate:i==null?void 0:i.navigate,isActive:i&&S(()=>{var a,o;return e.exact?(a=i.isExactActive)==null?void 0:a.value:(o=i.isActive)==null?void 0:o.value}),href:S(()=>e.to?i==null?void 0:i.route.value.href:e.href)}}const ma=z({href:String,replace:Boolean,to:[String,Object],exact:Boolean},"router");let za=!1;function Eb(e,t){let n=!1,r,s;Ge&&(ut(()=>{window.addEventListener("popstate",i),r=e==null?void 0:e.beforeEach((a,o,l)=>{za?n?t(l):l():setTimeout(()=>n?t(l):l()),za=!0}),s=e==null?void 0:e.afterEach(()=>{za=!1})}),lt(()=>{window.removeEventListener("popstate",i),r==null||r(),s==null||s()}));function i(a){var o;(o=a.state)!=null&&o.replaced||(n=!0,setTimeout(()=>n=!1))}}function Tb(e,t){he(()=>{var n;return(n=e.isActive)==null?void 0:n.value},n=>{e.isLink.value&&n&&t&&ut(()=>{t(!0)})},{immediate:!0})}const ko=Symbol("rippleStop"),kb=80;function cc(e,t){e.style.transform=t,e.style.webkitTransform=t}function Oo(e){return e.constructor.name==="TouchEvent"}function em(e){return e.constructor.name==="KeyboardEvent"}const Ob=function(e,t){var d;let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=0,s=0;if(!em(e)){const f=t.getBoundingClientRect(),m=Oo(e)?e.touches[e.touches.length-1]:e;r=m.clientX-f.left,s=m.clientY-f.top}let i=0,a=.3;(d=t._ripple)!=null&&d.circle?(a=.15,i=t.clientWidth/2,i=n.center?i:i+Math.sqrt((r-i)**2+(s-i)**2)/4):i=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2;const o=`${(t.clientWidth-i*2)/2}px`,l=`${(t.clientHeight-i*2)/2}px`,u=n.center?o:`${r-i}px`,c=n.center?l:`${s-i}px`;return{radius:i,scale:a,x:u,y:c,centerX:o,centerY:l}},Ri={show(e,t){var m;let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!((m=t==null?void 0:t._ripple)!=null&&m.enabled))return;const r=document.createElement("span"),s=document.createElement("span");r.appendChild(s),r.className="v-ripple__container",n.class&&(r.className+=` ${n.class}`);const{radius:i,scale:a,x:o,y:l,centerX:u,centerY:c}=Ob(e,t,n),d=`${i*2}px`;s.className="v-ripple__animation",s.style.width=d,s.style.height=d,t.appendChild(r);const f=window.getComputedStyle(t);f&&f.position==="static"&&(t.style.position="relative",t.dataset.previousPosition="static"),s.classList.add("v-ripple__animation--enter"),s.classList.add("v-ripple__animation--visible"),cc(s,`translate(${o}, ${l}) scale3d(${a},${a},${a})`),s.dataset.activated=String(performance.now()),setTimeout(()=>{s.classList.remove("v-ripple__animation--enter"),s.classList.add("v-ripple__animation--in"),cc(s,`translate(${u}, ${c}) scale3d(1,1,1)`)},0)},hide(e){var i;if(!((i=e==null?void 0:e._ripple)!=null&&i.enabled))return;const t=e.getElementsByClassName("v-ripple__animation");if(t.length===0)return;const n=t[t.length-1];if(n.dataset.isHiding)return;n.dataset.isHiding="true";const r=performance.now()-Number(n.dataset.activated),s=Math.max(250-r,0);setTimeout(()=>{n.classList.remove("v-ripple__animation--in"),n.classList.add("v-ripple__animation--out"),setTimeout(()=>{var o;e.getElementsByClassName("v-ripple__animation").length===1&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),((o=n.parentNode)==null?void 0:o.parentNode)===e&&e.removeChild(n.parentNode)},300)},s)}};function tm(e){return typeof e>"u"||!!e}function ks(e){const t={},n=e.currentTarget;if(!(!(n!=null&&n._ripple)||n._ripple.touched||e[ko])){if(e[ko]=!0,Oo(e))n._ripple.touched=!0,n._ripple.isTouch=!0;else if(n._ripple.isTouch)return;if(t.center=n._ripple.centered||em(e),n._ripple.class&&(t.class=n._ripple.class),Oo(e)){if(n._ripple.showTimerCommit)return;n._ripple.showTimerCommit=()=>{Ri.show(e,n,t)},n._ripple.showTimer=window.setTimeout(()=>{var r;(r=n==null?void 0:n._ripple)!=null&&r.showTimerCommit&&(n._ripple.showTimerCommit(),n._ripple.showTimerCommit=null)},kb)}else Ri.show(e,n,t)}}function dc(e){e[ko]=!0}function yt(e){const t=e.currentTarget;if(t!=null&&t._ripple){if(window.clearTimeout(t._ripple.showTimer),e.type==="touchend"&&t._ripple.showTimerCommit){t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null,t._ripple.showTimer=window.setTimeout(()=>{yt(e)});return}window.setTimeout(()=>{t._ripple&&(t._ripple.touched=!1)}),Ri.hide(t)}}function nm(e){const t=e.currentTarget;t!=null&&t._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}let Os=!1;function rm(e){!Os&&(e.keyCode===Bu.enter||e.keyCode===Bu.space)&&(Os=!0,ks(e))}function sm(e){Os=!1,yt(e)}function im(e){Os&&(Os=!1,yt(e))}function am(e,t,n){const{value:r,modifiers:s}=t,i=tm(r);if(i||Ri.hide(e),e._ripple=e._ripple??{},e._ripple.enabled=i,e._ripple.centered=s.center,e._ripple.circle=s.circle,Co(r)&&r.class&&(e._ripple.class=r.class),i&&!n){if(s.stop){e.addEventListener("touchstart",dc,{passive:!0}),e.addEventListener("mousedown",dc);return}e.addEventListener("touchstart",ks,{passive:!0}),e.addEventListener("touchend",yt,{passive:!0}),e.addEventListener("touchmove",nm,{passive:!0}),e.addEventListener("touchcancel",yt),e.addEventListener("mousedown",ks),e.addEventListener("mouseup",yt),e.addEventListener("mouseleave",yt),e.addEventListener("keydown",rm),e.addEventListener("keyup",sm),e.addEventListener("blur",im),e.addEventListener("dragstart",yt,{passive:!0})}else!i&&n&&om(e)}function om(e){e.removeEventListener("mousedown",ks),e.removeEventListener("touchstart",ks),e.removeEventListener("touchend",yt),e.removeEventListener("touchmove",nm),e.removeEventListener("touchcancel",yt),e.removeEventListener("mouseup",yt),e.removeEventListener("mouseleave",yt),e.removeEventListener("keydown",rm),e.removeEventListener("keyup",sm),e.removeEventListener("dragstart",yt),e.removeEventListener("blur",im)}function Ib(e,t){am(e,t,!1)}function Ab(e){delete e._ripple,om(e)}function Vb(e,t){if(t.value===t.oldValue)return;const n=tm(t.oldValue);am(e,t,n)}const Bs={mounted:Ib,unmounted:Ab,updated:Vb},Pb=z({active:{type:Boolean,default:void 0},symbol:{type:null,default:qf},flat:Boolean,icon:[Boolean,String,Function,Object],prependIcon:Ae,appendIcon:Ae,block:Boolean,stacked:Boolean,ripple:{type:[Boolean,Object],default:!0},text:String,...ur(),...ye(),...Vt(),...zn(),...Hn(),...Uf(),...Il(),...Rs(),...ca(),...Pt(),...ma(),...Ns(),...Xe({tag:"button"}),...je(),...tn({variant:"elevated"})},"VBtn"),pt=se()({name:"VBtn",directives:{Ripple:Bs},props:Pb(),emits:{"group:selected":e=>!0},setup(e,t){let{attrs:n,slots:r}=t;const{themeClasses:s}=Qe(e),{borderClasses:i}=cr(e),{colorClasses:a,colorStyles:o,variantClasses:l}=fr(e),{densityClasses:u}=qt(e),{dimensionStyles:c}=jn(e),{elevationClasses:d}=Un(e),{loaderClasses:f}=Al(e),{locationStyles:m}=$s(e),{positionClasses:h}=da(e),{roundedClasses:g}=Mt(e),{sizeClasses:y,sizeStyles:E}=Ls(e),_=zf(e,e.symbol,!1),w=fa(e,n),O=S(()=>{var T;return e.active!==void 0?e.active:w.isLink.value?(T=w.isActive)==null?void 0:T.value:_==null?void 0:_.isSelected.value}),I=S(()=>(_==null?void 0:_.disabled.value)||e.disabled),k=S(()=>e.variant==="elevated"&&!(e.disabled||e.flat||e.border)),C=S(()=>{if(e.value!==void 0)return Object(e.value)===e.value?JSON.stringify(e.value,null,0):e.value});function p(T){var V;I.value||w.isLink.value&&(T.metaKey||T.ctrlKey||T.shiftKey||T.button!==0||n.target==="_blank")||((V=w.navigate)==null||V.call(w,T),_==null||_.toggle())}return Tb(w,_==null?void 0:_.select),ve(()=>{var F,G;const T=w.isLink.value?"a":e.tag,V=!!(e.prependIcon||r.prepend),$=!!(e.appendIcon||r.append),P=!!(e.icon&&e.icon!==!0),R=(_==null?void 0:_.isSelected.value)&&(!w.isLink.value||((F=w.isActive)==null?void 0:F.value))||!_||((G=w.isActive)==null?void 0:G.value);return ft(v(T,{type:T==="a"?void 0:"button",class:["v-btn",_==null?void 0:_.selectedClass.value,{"v-btn--active":O.value,"v-btn--block":e.block,"v-btn--disabled":I.value,"v-btn--elevated":k.value,"v-btn--flat":e.flat,"v-btn--icon":!!e.icon,"v-btn--loading":e.loading,"v-btn--stacked":e.stacked},s.value,i.value,R?a.value:void 0,u.value,d.value,f.value,h.value,g.value,y.value,l.value,e.class],style:[R?o.value:void 0,c.value,m.value,E.value,e.style],disabled:I.value||void 0,href:w.href.value,onClick:p,value:C.value},{default:()=>{var Z;return[dr(!0,"v-btn"),!e.icon&&V&&v("span",{key:"prepend",class:"v-btn__prepend"},[r.prepend?v(Le,{key:"prepend-defaults",disabled:!e.prependIcon,defaults:{VIcon:{icon:e.prependIcon}}},r.prepend):v(et,{key:"prepend-icon",icon:e.prependIcon},null)]),v("span",{class:"v-btn__content","data-no-activator":""},[!r.default&&P?v(et,{key:"content-icon",icon:e.icon},null):v(Le,{key:"content-defaults",disabled:!P,defaults:{VIcon:{icon:e.icon}}},{default:()=>{var ee;return[((ee=r.default)==null?void 0:ee.call(r))??e.text]}})]),!e.icon&&$&&v("span",{key:"append",class:"v-btn__append"},[r.append?v(Le,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VIcon:{icon:e.appendIcon}}},r.append):v(et,{key:"append-icon",icon:e.appendIcon},null)]),!!e.loading&&v("span",{key:"loader",class:"v-btn__loader"},[((Z=r.loader)==null?void 0:Z.call(r))??v(hb,{color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0,size:"23",width:"2"},null)])]}}),[[dn("ripple"),!I.value&&e.ripple,null]])}),{}}});function ja(e,t){return{x:e.x+t.x,y:e.y+t.y}}function Mb(e,t){return{x:e.x-t.x,y:e.y-t.y}}function fc(e,t){if(e.side==="top"||e.side==="bottom"){const{side:n,align:r}=e,s=r==="left"?0:r==="center"?t.width/2:r==="right"?t.width:r,i=n==="top"?0:n==="bottom"?t.height:n;return ja({x:s,y:i},t)}else if(e.side==="left"||e.side==="right"){const{side:n,align:r}=e,s=n==="left"?0:n==="right"?t.width:n,i=r==="top"?0:r==="center"?t.height/2:r==="bottom"?t.height:r;return ja({x:s,y:i},t)}return ja({x:t.width/2,y:t.height/2},t)}const lm={static:Nb,connected:Rb},Fb=z({locationStrategy:{type:[String,Function],default:"static",validator:e=>typeof e=="function"||e in lm},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies");function Db(e,t){const n=K({}),r=K();Ge&&(Ln(()=>!!(t.isActive.value&&e.locationStrategy),i=>{var a,o;he(()=>e.locationStrategy,i),lt(()=>{r.value=void 0}),typeof e.locationStrategy=="function"?r.value=(a=e.locationStrategy(t,e,n))==null?void 0:a.updateLocation:r.value=(o=lm[e.locationStrategy](t,e,n))==null?void 0:o.updateLocation}),window.addEventListener("resize",s,{passive:!0}),lt(()=>{window.removeEventListener("resize",s),r.value=void 0}));function s(i){var a;(a=r.value)==null||a.call(r,i)}return{contentStyles:n,updateLocation:r}}function Nb(){}function Lb(e,t){t?e.style.removeProperty("left"):e.style.removeProperty("right");const n=xl(e);return t?n.x+=parseFloat(e.style.right||0):n.x-=parseFloat(e.style.left||0),n.y-=parseFloat(e.style.top||0),n}function Rb(e,t,n){jp(e.activatorEl.value)&&Object.assign(n.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0});const{preferredAnchor:s,preferredOrigin:i}=Sl(()=>{const h=_o(t.location,e.isRtl.value),g=t.origin==="overlap"?h:t.origin==="auto"?Ba(h):_o(t.origin,e.isRtl.value);return h.side===g.side&&h.align===Ha(g).align?{preferredAnchor:ju(h),preferredOrigin:ju(g)}:{preferredAnchor:h,preferredOrigin:g}}),[a,o,l,u]=["minWidth","minHeight","maxWidth","maxHeight"].map(h=>S(()=>{const g=parseFloat(t[h]);return isNaN(g)?1/0:g})),c=S(()=>{if(Array.isArray(t.offset))return t.offset;if(typeof t.offset=="string"){const h=t.offset.split(" ").map(parseFloat);return h.length<2&&h.push(0),h}return typeof t.offset=="number"?[t.offset,0]:[0,0]});let d=!1;const f=new ResizeObserver(()=>{d&&m()});he([e.activatorEl,e.contentEl],(h,g)=>{let[y,E]=h,[_,w]=g;_&&f.unobserve(_),y&&f.observe(y),w&&f.unobserve(w),E&&f.observe(E)},{immediate:!0}),lt(()=>{f.disconnect()});function m(){if(d=!1,requestAnimationFrame(()=>{requestAnimationFrame(()=>d=!0)}),!e.activatorEl.value||!e.contentEl.value)return;const h=e.activatorEl.value.getBoundingClientRect(),g=Lb(e.contentEl.value,e.isRtl.value),y=Di(e.contentEl.value),E=12;y.length||(y.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(g.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),g.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)));const _=y.reduce(($,P)=>{const R=P.getBoundingClientRect(),F=new Or({x:P===document.documentElement?0:R.x,y:P===document.documentElement?0:R.y,width:P.clientWidth,height:P.clientHeight});return $?new Or({x:Math.max($.left,F.left),y:Math.max($.top,F.top),width:Math.min($.right,F.right)-Math.max($.left,F.left),height:Math.min($.bottom,F.bottom)-Math.max($.top,F.top)}):F},void 0);_.x+=E,_.y+=E,_.width-=E*2,_.height-=E*2;let w={anchor:s.value,origin:i.value};function O($){const P=new Or(g),R=fc($.anchor,h),F=fc($.origin,P);let{x:G,y:Z}=Mb(R,F);switch($.anchor.side){case"top":Z-=c.value[0];break;case"bottom":Z+=c.value[0];break;case"left":G-=c.value[0];break;case"right":G+=c.value[0];break}switch($.anchor.align){case"top":Z-=c.value[1];break;case"bottom":Z+=c.value[1];break;case"left":G-=c.value[1];break;case"right":G+=c.value[1];break}return P.x+=G,P.y+=Z,P.width=Math.min(P.width,l.value),P.height=Math.min(P.height,u.value),{overflows:qu(P,_),x:G,y:Z}}let I=0,k=0;const C={x:0,y:0},p={x:!1,y:!1};let T=-1;for(;!(T++>10);){const{x:$,y:P,overflows:R}=O(w);I+=$,k+=P,g.x+=$,g.y+=P;{const F=Wu(w.anchor),G=R.x.before||R.x.after,Z=R.y.before||R.y.after;let ee=!1;if(["x","y"].forEach(U=>{if(U==="x"&&G&&!p.x||U==="y"&&Z&&!p.y){const W={anchor:{...w.anchor},origin:{...w.origin}},fe=U==="x"?F==="y"?Ha:Ba:F==="y"?Ba:Ha;W.anchor=fe(W.anchor),W.origin=fe(W.origin);const{overflows:oe}=O(W);(oe[U].before<=R[U].before&&oe[U].after<=R[U].after||oe[U].before+oe[U].after<(R[U].before+R[U].after)/2)&&(w=W,ee=p[U]=!0)}}),ee)continue}R.x.before&&(I+=R.x.before,g.x+=R.x.before),R.x.after&&(I-=R.x.after,g.x-=R.x.after),R.y.before&&(k+=R.y.before,g.y+=R.y.before),R.y.after&&(k-=R.y.after,g.y-=R.y.after);{const F=qu(g,_);C.x=_.width-F.x.before-F.x.after,C.y=_.height-F.y.before-F.y.after,I+=F.x.before,g.x+=F.x.before,k+=F.y.before,g.y+=F.y.before}break}const V=Wu(w.anchor);return Object.assign(n.value,{"--v-overlay-anchor-origin":`${w.anchor.side} ${w.anchor.align}`,transformOrigin:`${w.origin.side} ${w.origin.align}`,top:ae(Wa(k)),left:e.isRtl.value?void 0:ae(Wa(I)),right:e.isRtl.value?ae(Wa(-I)):void 0,minWidth:ae(V==="y"?Math.min(a.value,h.width):a.value),maxWidth:ae(mc(_s(C.x,a.value===1/0?0:a.value,l.value))),maxHeight:ae(mc(_s(C.y,o.value===1/0?0:o.value,u.value)))}),{available:C,contentBox:g}}return he(()=>[s.value,i.value,t.offset,t.minWidth,t.minHeight,t.maxWidth,t.maxHeight],()=>m()),ut(()=>{const h=m();if(!h)return;const{available:g,contentBox:y}=h;y.height>g.y&&requestAnimationFrame(()=>{m(),requestAnimationFrame(()=>{m()})})}),{updateLocation:m}}function Wa(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function mc(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let Io=!0;const $i=[];function $b(e){!Io||$i.length?($i.push(e),Ao()):(Io=!1,e(),Ao())}let hc=-1;function Ao(){cancelAnimationFrame(hc),hc=requestAnimationFrame(()=>{const e=$i.shift();e&&e(),$i.length?Ao():Io=!0})}const bi={none:null,close:Ub,block:zb,reposition:jb},Bb=z({scrollStrategy:{type:[String,Function],default:"block",validator:e=>typeof e=="function"||e in bi}},"VOverlay-scroll-strategies");function Hb(e,t){if(!Ge)return;let n;gn(async()=>{n==null||n.stop(),t.isActive.value&&e.scrollStrategy&&(n=Vs(),await ut(),n.active&&n.run(()=>{var r;typeof e.scrollStrategy=="function"?e.scrollStrategy(t,e,n):(r=bi[e.scrollStrategy])==null||r.call(bi,t,e,n)}))}),lt(()=>{n==null||n.stop()})}function Ub(e){function t(n){e.isActive.value=!1}um(e.activatorEl.value??e.contentEl.value,t)}function zb(e,t){var a;const n=(a=e.root.value)==null?void 0:a.offsetParent,r=[...new Set([...Di(e.activatorEl.value,t.contained?n:void 0),...Di(e.contentEl.value,t.contained?n:void 0)])].filter(o=>!o.classList.contains("v-overlay-scroll-blocked")),s=window.innerWidth-document.documentElement.offsetWidth,i=(o=>Tl(o)&&o)(n||document.documentElement);i&&e.root.value.classList.add("v-overlay--scroll-blocked"),r.forEach((o,l)=>{o.style.setProperty("--v-body-scroll-x",ae(-o.scrollLeft)),o.style.setProperty("--v-body-scroll-y",ae(-o.scrollTop)),o!==document.documentElement&&o.style.setProperty("--v-scrollbar-offset",ae(s)),o.classList.add("v-overlay-scroll-blocked")}),lt(()=>{r.forEach((o,l)=>{const u=parseFloat(o.style.getPropertyValue("--v-body-scroll-x")),c=parseFloat(o.style.getPropertyValue("--v-body-scroll-y"));o.style.removeProperty("--v-body-scroll-x"),o.style.removeProperty("--v-body-scroll-y"),o.style.removeProperty("--v-scrollbar-offset"),o.classList.remove("v-overlay-scroll-blocked"),o.scrollLeft=-u,o.scrollTop=-c}),i&&e.root.value.classList.remove("v-overlay--scroll-blocked")})}function jb(e,t,n){let r=!1,s=-1,i=-1;function a(o){$b(()=>{var c,d;const l=performance.now();(d=(c=e.updateLocation).value)==null||d.call(c,o),r=(performance.now()-l)/(1e3/60)>2})}i=(typeof requestIdleCallback>"u"?o=>o():requestIdleCallback)(()=>{n.run(()=>{um(e.activatorEl.value??e.contentEl.value,o=>{r?(cancelAnimationFrame(s),s=requestAnimationFrame(()=>{s=requestAnimationFrame(()=>{a(o)})})):a(o)})})}),lt(()=>{typeof cancelIdleCallback<"u"&&cancelIdleCallback(i),cancelAnimationFrame(s)})}function um(e,t){const n=[document,...Di(e)];n.forEach(r=>{r.addEventListener("scroll",t,{passive:!0})}),lt(()=>{n.forEach(r=>{r.removeEventListener("scroll",t)})})}const Vo=Symbol.for("vuetify:v-menu"),Wb=z({closeDelay:[Number,String],openDelay:[Number,String]},"delay");function qb(e,t){const n={},r=s=>()=>{if(!Ge)return Promise.resolve(!0);const i=s==="openDelay";return n.closeDelay&&window.clearTimeout(n.closeDelay),delete n.closeDelay,n.openDelay&&window.clearTimeout(n.openDelay),delete n.openDelay,new Promise(a=>{const o=parseInt(e[s]??0,10);n[s]=window.setTimeout(()=>{t==null||t(i),a(i)},o)})};return{runCloseDelay:r("closeDelay"),runOpenDelay:r("openDelay")}}const Zb=z({activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...Wb()},"VOverlay-activator");function Yb(e,t){let{isActive:n,isTop:r}=t;const s=K();let i=!1,a=!1,o=!0;const l=S(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),u=S(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!l.value),{runOpenDelay:c,runCloseDelay:d}=qb(e,w=>{w===(e.openOnHover&&i||l.value&&a)&&!(e.openOnHover&&n.value&&!r.value)&&(n.value!==w&&(o=!0),n.value=w)}),f={onClick:w=>{w.stopPropagation(),s.value=w.currentTarget||w.target,n.value=!n.value},onMouseenter:w=>{var O;(O=w.sourceCapabilities)!=null&&O.firesTouchEvents||(i=!0,s.value=w.currentTarget||w.target,c())},onMouseleave:w=>{i=!1,d()},onFocus:w=>{Eo&&!w.target.matches(":focus-visible")||(a=!0,w.stopPropagation(),s.value=w.currentTarget||w.target,c())},onBlur:w=>{a=!1,w.stopPropagation(),d()}},m=S(()=>{const w={};return u.value&&(w.onClick=f.onClick),e.openOnHover&&(w.onMouseenter=f.onMouseenter,w.onMouseleave=f.onMouseleave),l.value&&(w.onFocus=f.onFocus,w.onBlur=f.onBlur),w}),h=S(()=>{const w={};if(e.openOnHover&&(w.onMouseenter=()=>{i=!0,c()},w.onMouseleave=()=>{i=!1,d()}),l.value&&(w.onFocusin=()=>{a=!0,c()},w.onFocusout=()=>{a=!1,d()}),e.closeOnContentClick){const O=Re(Vo,null);w.onClick=()=>{n.value=!1,O==null||O.closeParents()}}return w}),g=S(()=>{const w={};return e.openOnHover&&(w.onMouseenter=()=>{o&&(i=!0,o=!1,c())},w.onMouseleave=()=>{i=!1,d()}),w});he(r,w=>{w&&(e.openOnHover&&!i&&(!l.value||!a)||l.value&&!a&&(!e.openOnHover||!i))&&(n.value=!1)});const y=K();gn(()=>{y.value&&ut(()=>{s.value=xo(y.value)})});const E=it("useActivator");let _;return he(()=>!!e.activator,w=>{w&&Ge?(_=Vs(),_.run(()=>{Gb(e,E,{activatorEl:s,activatorEvents:m})})):_&&_.stop()},{flush:"post",immediate:!0}),lt(()=>{_==null||_.stop()}),{activatorEl:s,activatorRef:y,activatorEvents:m,contentEvents:h,scrimEvents:g}}function Gb(e,t,n){let{activatorEl:r,activatorEvents:s}=n;he(()=>e.activator,(l,u)=>{if(u&&l!==u){const c=o(u);c&&a(c)}l&&ut(()=>i())},{immediate:!0}),he(()=>e.activatorProps,()=>{i()}),lt(()=>{a()});function i(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:o(),u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;l&&wp(l,ue(s.value,u))}function a(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:o(),u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;l&&Sp(l,ue(s.value,u))}function o(){var c,d;let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator,u;if(l)if(l==="parent"){let f=(d=(c=t==null?void 0:t.proxy)==null?void 0:c.$el)==null?void 0:d.parentNode;for(;f.hasAttribute("data-no-activator");)f=f.parentNode;u=f}else typeof l=="string"?u=document.querySelector(l):"$el"in l?u=l.$el:u=l;return r.value=(u==null?void 0:u.nodeType)===Node.ELEMENT_NODE?u:null,r.value}}const ha=["sm","md","lg","xl","xxl"],Po=Symbol.for("vuetify:display"),vc={mobileBreakpoint:"lg",thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},Kb=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:vc;return Et(vc,e)};function gc(e){return Ge&&!e?window.innerWidth:typeof e=="object"&&e.clientWidth||0}function yc(e){return Ge&&!e?window.innerHeight:typeof e=="object"&&e.clientHeight||0}function pc(e){const t=Ge&&!e?window.navigator.userAgent:"ssr";function n(h){return!!t.match(h)}const r=n(/android/i),s=n(/iphone|ipad|ipod/i),i=n(/cordova/i),a=n(/electron/i),o=n(/chrome/i),l=n(/edge/i),u=n(/firefox/i),c=n(/opera/i),d=n(/win/i),f=n(/mac/i),m=n(/linux/i);return{android:r,ios:s,cordova:i,electron:a,chrome:o,edge:l,firefox:u,opera:c,win:d,mac:f,linux:m,touch:Up,ssr:t==="ssr"}}function Jb(e,t){const{thresholds:n,mobileBreakpoint:r}=Kb(e),s=be(yc(t)),i=be(pc(t)),a=nt({}),o=be(gc(t));function l(){s.value=yc(),o.value=gc()}function u(){l(),i.value=pc()}return gn(()=>{const c=o.value<n.sm,d=o.value<n.md&&!c,f=o.value<n.lg&&!(d||c),m=o.value<n.xl&&!(f||d||c),h=o.value<n.xxl&&!(m||f||d||c),g=o.value>=n.xxl,y=c?"xs":d?"sm":f?"md":m?"lg":h?"xl":"xxl",E=typeof r=="number"?r:n[r],_=o.value<E;a.xs=c,a.sm=d,a.md=f,a.lg=m,a.xl=h,a.xxl=g,a.smAndUp=!c,a.mdAndUp=!(c||d),a.lgAndUp=!(c||d||f),a.xlAndUp=!(c||d||f||m),a.smAndDown=!(f||m||h||g),a.mdAndDown=!(m||h||g),a.lgAndDown=!(h||g),a.xlAndDown=!g,a.name=y,a.height=s.value,a.width=o.value,a.mobile=_,a.mobileBreakpoint=r,a.platform=i.value,a.thresholds=n}),Ge&&window.addEventListener("resize",l,{passive:!0}),{...Qi(a),update:u,ssr:!!t}}function cm(){const e=Re(Po);if(!e)throw new Error("Could not find Vuetify display injection");return e}function Xb(){if(!Ge)return be(!1);const{ssr:e}=cm();if(e){const t=be(!1);return pn(()=>{t.value=!0}),t}else return be(!0)}const Qb=z({eager:Boolean},"lazy");function e0(e,t){const n=be(!1),r=S(()=>n.value||e.eager||t.value);he(t,()=>n.value=!0);function s(){e.eager||(n.value=!1)}return{isBooted:n,hasContent:r,onAfterLeave:s}}function Hs(){const t=it("useScopeId").vnode.scopeId;return{scopeId:t?{[t]:""}:void 0}}const bc=Symbol.for("vuetify:stack"),Xr=nt([]);function t0(e,t,n){const r=it("useStack"),s=!n,i=Re(bc,void 0),a=nt({activeChildren:new Set});wt(bc,a);const o=be(+t.value);Ln(e,()=>{var d;const c=(d=Xr.at(-1))==null?void 0:d[1];o.value=c?c+10:+t.value,s&&Xr.push([r.uid,o.value]),i==null||i.activeChildren.add(r.uid),lt(()=>{if(s){const f=ce(Xr).findIndex(m=>m[0]===r.uid);Xr.splice(f,1)}i==null||i.activeChildren.delete(r.uid)})});const l=be(!0);s&&gn(()=>{var d;const c=((d=Xr.at(-1))==null?void 0:d[0])===r.uid;setTimeout(()=>l.value=c)});const u=S(()=>!a.activeChildren.size);return{globalTop:Ps(l),localTop:u,stackStyles:S(()=>({zIndex:o.value}))}}function n0(e){return{teleportTarget:S(()=>{const n=e.value;if(n===!0||!Ge)return;const r=n===!1?document.body:typeof n=="string"?document.querySelector(n):n;if(r==null)return;let s=r.querySelector(":scope > .v-overlay-container");return s||(s=document.createElement("div"),s.className="v-overlay-container",r.appendChild(s)),s})}}const Us=z({transition:{type:[Boolean,String,Object],default:"fade-transition",validator:e=>e!==!0}},"transition"),Pn=(e,t)=>{let{slots:n}=t;const{transition:r,disabled:s,...i}=e,{component:a=mn,...o}=typeof r=="object"?r:{};return $n(a,ue(typeof r=="string"?{name:s?"":r}:o,i,{disabled:s}),n)};function r0(){return!0}function dm(e,t,n){if(!e||fm(e,n)===!1)return!1;const r=Lf(t);if(typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&r.host===e.target)return!1;const s=(typeof n.value=="object"&&n.value.include||(()=>[]))();return s.push(t),!s.some(i=>i==null?void 0:i.contains(e.target))}function fm(e,t){return(typeof t.value=="object"&&t.value.closeConditional||r0)(e)}function s0(e,t,n){const r=typeof n.value=="function"?n.value:n.value.handler;t._clickOutside.lastMousedownWasOutside&&dm(e,t,n)&&setTimeout(()=>{fm(e,n)&&r&&r(e)},0)}function wc(e,t){const n=Lf(e);t(document),typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&t(n)}const i0={mounted(e,t){const n=s=>s0(s,e,t),r=s=>{e._clickOutside.lastMousedownWasOutside=dm(s,e,t)};wc(e,s=>{s.addEventListener("click",n,!0),s.addEventListener("mousedown",r,!0)}),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[t.instance.$.uid]={onClick:n,onMousedown:r}},unmounted(e,t){e._clickOutside&&(wc(e,n=>{var i;if(!n||!((i=e._clickOutside)!=null&&i[t.instance.$.uid]))return;const{onClick:r,onMousedown:s}=e._clickOutside[t.instance.$.uid];n.removeEventListener("click",r,!0),n.removeEventListener("mousedown",s,!0)}),delete e._clickOutside[t.instance.$.uid])}};function a0(e){const{modelValue:t,color:n,...r}=e;return v(mn,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&v("div",ue({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},r),null)]})}const zs=z({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...Zb(),...ye(),...zn(),...Qb(),...Fb(),...Bb(),...je(),...Us()},"VOverlay"),Rn=se()({name:"VOverlay",directives:{ClickOutside:i0},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...zs()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,afterLeave:()=>!0},setup(e,t){let{slots:n,attrs:r,emit:s}=t;const i=Be(e,"modelValue"),a=S({get:()=>i.value,set:W=>{W&&e.disabled||(i.value=W)}}),{teleportTarget:o}=n0(S(()=>e.attach||e.contained)),{themeClasses:l}=Qe(e),{rtlClasses:u,isRtl:c}=mr(),{hasContent:d,onAfterLeave:f}=e0(e,a),m=Pr(S(()=>typeof e.scrim=="string"?e.scrim:null)),{globalTop:h,localTop:g,stackStyles:y}=t0(a,de(e,"zIndex"),e._disableGlobalStack),{activatorEl:E,activatorRef:_,activatorEvents:w,contentEvents:O,scrimEvents:I}=Yb(e,{isActive:a,isTop:g}),{dimensionStyles:k}=jn(e),C=Xb(),{scopeId:p}=Hs();he(()=>e.disabled,W=>{W&&(a.value=!1)});const T=K(),V=K(),{contentStyles:$,updateLocation:P}=Db(e,{isRtl:c,contentEl:V,activatorEl:E,isActive:a});Hb(e,{root:T,contentEl:V,activatorEl:E,isActive:a,updateLocation:P});function R(W){s("click:outside",W),e.persistent?U():a.value=!1}function F(){return a.value&&h.value}Ge&&he(a,W=>{W?window.addEventListener("keydown",G):window.removeEventListener("keydown",G)},{immediate:!0});function G(W){var fe,oe;W.key==="Escape"&&h.value&&(e.persistent?U():(a.value=!1,(fe=V.value)!=null&&fe.contains(document.activeElement)&&((oe=E.value)==null||oe.focus())))}const Z=_b();Ln(()=>e.closeOnBack,()=>{Eb(Z,W=>{h.value&&a.value?(W(!1),e.persistent?U():a.value=!1):W()})});const ee=K();he(()=>a.value&&(e.absolute||e.contained)&&o.value==null,W=>{if(W){const fe=$f(T.value);fe&&fe!==document.scrollingElement&&(ee.value=fe.scrollTop)}});function U(){e.noClickAnimation||V.value&&Cr(V.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:Fi})}return ve(()=>{var W;return v(ke,null,[(W=n.activator)==null?void 0:W.call(n,{isActive:a.value,props:ue({ref:_},w.value,e.activatorProps)}),C.value&&d.value&&v(vy,{disabled:!o.value,to:o.value},{default:()=>[v("div",ue({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":a.value,"v-overlay--contained":e.contained},l.value,u.value,e.class],style:[y.value,{top:ae(ee.value)},e.style],ref:T},p,r),[v(a0,ue({color:m,modelValue:a.value&&!!e.scrim},I.value),null),v(Pn,{appear:!0,persisted:!0,transition:e.transition,target:E.value,onAfterLeave:()=>{f(),s("afterLeave")}},{default:()=>{var fe;return[ft(v("div",ue({ref:V,class:["v-overlay__content",e.contentClass],style:[k.value,$.value]},O.value,e.contentProps),[(fe=n.default)==null?void 0:fe.call(n,{isActive:a})]),[[$r,a.value],[dn("click-outside"),{handler:R,closeConditional:F,include:()=>[E.value]}]])]}})])]})])}),{activatorEl:E,animateClick:U,contentEl:V,globalTop:h,localTop:g,updateLocation:P}}}),qa=Symbol("Forwarded refs");function Za(e,t){let n=e;for(;n;){const r=Reflect.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function Ur(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e[qa]=n,new Proxy(e,{get(s,i){if(Reflect.has(s,i))return Reflect.get(s,i);if(!(typeof i=="symbol"||i.startsWith("__"))){for(const a of n)if(a.value&&Reflect.has(a.value,i)){const o=Reflect.get(a.value,i);return typeof o=="function"?o.bind(a.value):o}}},has(s,i){if(Reflect.has(s,i))return!0;if(typeof i=="symbol"||i.startsWith("__"))return!1;for(const a of n)if(a.value&&Reflect.has(a.value,i))return!0;return!1},getOwnPropertyDescriptor(s,i){var o;const a=Reflect.getOwnPropertyDescriptor(s,i);if(a)return a;if(!(typeof i=="symbol"||i.startsWith("__"))){for(const l of n){if(!l.value)continue;const u=Za(l.value,i)??("_"in l.value?Za((o=l.value._)==null?void 0:o.setupState,i):void 0);if(u)return u}for(const l of n){const u=l.value&&l.value[qa];if(!u)continue;const c=u.slice();for(;c.length;){const d=c.shift(),f=Za(d.value,i);if(f)return f;const m=d.value&&d.value[qa];m&&c.push(...m)}}}}})}const o0=z({multiLine:Boolean,timeout:{type:[Number,String],default:5e3},vertical:Boolean,...Rs({location:"bottom"}),...ca(),...Pt(),...tn(),...je(),...Ds(zs({transition:"v-snackbar-transition"}),["persistent","noClickAnimation","scrim","scrollStrategy"])},"VSnackbar"),l0=se()({name:"VSnackbar",props:o0(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"modelValue"),{locationStyles:s}=$s(e),{positionClasses:i}=da(e),{scopeId:a}=Hs(),{themeClasses:o}=Qe(e),{colorClasses:l,colorStyles:u,variantClasses:c}=fr(e),{roundedClasses:d}=Mt(e),f=K();he(r,h),he(()=>e.timeout,h),pn(()=>{r.value&&h()});let m=-1;function h(){window.clearTimeout(m);const y=Number(e.timeout);!r.value||y===-1||(m=window.setTimeout(()=>{r.value=!1},y))}function g(){window.clearTimeout(m)}return ve(()=>{const[y]=Rn.filterProps(e);return v(Rn,ue({ref:f,class:["v-snackbar",{"v-snackbar--active":r.value,"v-snackbar--multi-line":e.multiLine&&!e.vertical,"v-snackbar--vertical":e.vertical},i.value,e.class],style:e.style},y,{modelValue:r.value,"onUpdate:modelValue":E=>r.value=E,contentProps:ue({class:["v-snackbar__wrapper",o.value,l.value,d.value,c.value],style:[s.value,u.value],onPointerenter:g,onPointerleave:h},y.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:"none",_disableGlobalStack:!0},a),{default:()=>[dr(!1,"v-snackbar"),n.default&&v("div",{class:"v-snackbar__content",role:"status","aria-live":"polite"},[n.default()]),n.actions&&v(Le,{defaults:{VBtn:{variant:"text",ripple:!1}}},{default:()=>[v("div",{class:"v-snackbar__actions"},[n.actions()])]})],activator:n.activator})}),Ur({},f)}}),u0={__name:"snackbar",setup(e){const{snackbar:t,timeout:n,msg:r,color:s,icon:i}=vp(pl());return(a,o)=>(Ye(),tr("div",null,[tt(t)?(Ye(),gt(l0,{key:0,modelValue:tt(t),"onUpdate:modelValue":o[1]||(o[1]=l=>Oe(t)?t.value=l:null),timeout:tt(n),color:tt(s)},{actions:re(()=>[v(pt,{color:"white",variant:"text",onClick:o[0]||(o[0]=l=>t.value=!1)},{default:re(()=>[Ze(" Cerrar ")]),_:1})]),default:re(()=>[tt(i)?(Ye(),gt(et,{key:0,icon:tt(i)},null,8,["icon"])):nr("",!0),Ze(" "+Se(tt(r))+" ",1)]),_:1},8,["modelValue","timeout","color"])):nr("",!0)]))}};function mm(e,t){return function(){return e.apply(t,arguments)}}const{toString:c0}=Object.prototype,{getPrototypeOf:Vl}=Object,va=(e=>t=>{const n=c0.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),nn=e=>(e=e.toLowerCase(),t=>va(t)===e),ga=e=>t=>typeof t===e,{isArray:zr}=Array,Is=ga("undefined");function d0(e){return e!==null&&!Is(e)&&e.constructor!==null&&!Is(e.constructor)&&Ot(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const hm=nn("ArrayBuffer");function f0(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&hm(e.buffer),t}const m0=ga("string"),Ot=ga("function"),vm=ga("number"),ya=e=>e!==null&&typeof e=="object",h0=e=>e===!0||e===!1,wi=e=>{if(va(e)!=="object")return!1;const t=Vl(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},v0=nn("Date"),g0=nn("File"),y0=nn("Blob"),p0=nn("FileList"),b0=e=>ya(e)&&Ot(e.pipe),w0=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ot(e.append)&&((t=va(e))==="formdata"||t==="object"&&Ot(e.toString)&&e.toString()==="[object FormData]"))},S0=nn("URLSearchParams"),C0=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function js(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),zr(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),a=i.length;let o;for(r=0;r<a;r++)o=i[r],t.call(null,e[o],o,e)}}function gm(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const ym=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),pm=e=>!Is(e)&&e!==ym;function Mo(){const{caseless:e}=pm(this)&&this||{},t={},n=(r,s)=>{const i=e&&gm(t,s)||s;wi(t[i])&&wi(r)?t[i]=Mo(t[i],r):wi(r)?t[i]=Mo({},r):zr(r)?t[i]=r.slice():t[i]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&js(arguments[r],n);return t}const x0=(e,t,n,{allOwnKeys:r}={})=>(js(t,(s,i)=>{n&&Ot(s)?e[i]=mm(s,n):e[i]=s},{allOwnKeys:r}),e),_0=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),E0=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},T0=(e,t,n,r)=>{let s,i,a;const o={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)a=s[i],(!r||r(a,e,t))&&!o[a]&&(t[a]=e[a],o[a]=!0);e=n!==!1&&Vl(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},k0=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},O0=e=>{if(!e)return null;if(zr(e))return e;let t=e.length;if(!vm(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},I0=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Vl(Uint8Array)),A0=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let s;for(;(s=r.next())&&!s.done;){const i=s.value;t.call(e,i[0],i[1])}},V0=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},P0=nn("HTMLFormElement"),M0=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Sc=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),F0=nn("RegExp"),bm=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};js(n,(s,i)=>{t(s,i,e)!==!1&&(r[i]=s)}),Object.defineProperties(e,r)},D0=e=>{bm(e,(t,n)=>{if(Ot(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Ot(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},N0=(e,t)=>{const n={},r=s=>{s.forEach(i=>{n[i]=!0})};return zr(e)?r(e):r(String(e).split(t)),n},L0=()=>{},R0=(e,t)=>(e=+e,Number.isFinite(e)?e:t),Ya="abcdefghijklmnopqrstuvwxyz",Cc="0123456789",wm={DIGIT:Cc,ALPHA:Ya,ALPHA_DIGIT:Ya+Ya.toUpperCase()+Cc},$0=(e=16,t=wm.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n};function B0(e){return!!(e&&Ot(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const H0=e=>{const t=new Array(10),n=(r,s)=>{if(ya(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const i=zr(r)?[]:{};return js(r,(a,o)=>{const l=n(a,s+1);!Is(l)&&(i[o]=l)}),t[s]=void 0,i}}return r};return n(e,0)},U0=nn("AsyncFunction"),z0=e=>e&&(ya(e)||Ot(e))&&Ot(e.then)&&Ot(e.catch),M={isArray:zr,isArrayBuffer:hm,isBuffer:d0,isFormData:w0,isArrayBufferView:f0,isString:m0,isNumber:vm,isBoolean:h0,isObject:ya,isPlainObject:wi,isUndefined:Is,isDate:v0,isFile:g0,isBlob:y0,isRegExp:F0,isFunction:Ot,isStream:b0,isURLSearchParams:S0,isTypedArray:I0,isFileList:p0,forEach:js,merge:Mo,extend:x0,trim:C0,stripBOM:_0,inherits:E0,toFlatObject:T0,kindOf:va,kindOfTest:nn,endsWith:k0,toArray:O0,forEachEntry:A0,matchAll:V0,isHTMLForm:P0,hasOwnProperty:Sc,hasOwnProp:Sc,reduceDescriptors:bm,freezeMethods:D0,toObjectSet:N0,toCamelCase:M0,noop:L0,toFiniteNumber:R0,findKey:gm,global:ym,isContextDefined:pm,ALPHABET:wm,generateString:$0,isSpecCompliantForm:B0,toJSONObject:H0,isAsyncFn:U0,isThenable:z0};function Ee(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s)}M.inherits(Ee,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:M.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const Sm=Ee.prototype,Cm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Cm[e]={value:e}});Object.defineProperties(Ee,Cm);Object.defineProperty(Sm,"isAxiosError",{value:!0});Ee.from=(e,t,n,r,s,i)=>{const a=Object.create(Sm);return M.toFlatObject(e,a,function(l){return l!==Error.prototype},o=>o!=="isAxiosError"),Ee.call(a,e.message,t,n,r,s),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const j0=null;function Fo(e){return M.isPlainObject(e)||M.isArray(e)}function xm(e){return M.endsWith(e,"[]")?e.slice(0,-2):e}function xc(e,t,n){return e?e.concat(t).map(function(s,i){return s=xm(s),!n&&i?"["+s+"]":s}).join(n?".":""):t}function W0(e){return M.isArray(e)&&!e.some(Fo)}const q0=M.toFlatObject(M,{},null,function(t){return/^is[A-Z]/.test(t)});function pa(e,t,n){if(!M.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=M.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,y){return!M.isUndefined(y[g])});const r=n.metaTokens,s=n.visitor||c,i=n.dots,a=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&M.isSpecCompliantForm(t);if(!M.isFunction(s))throw new TypeError("visitor must be a function");function u(h){if(h===null)return"";if(M.isDate(h))return h.toISOString();if(!l&&M.isBlob(h))throw new Ee("Blob is not supported. Use a Buffer instead.");return M.isArrayBuffer(h)||M.isTypedArray(h)?l&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function c(h,g,y){let E=h;if(h&&!y&&typeof h=="object"){if(M.endsWith(g,"{}"))g=r?g:g.slice(0,-2),h=JSON.stringify(h);else if(M.isArray(h)&&W0(h)||(M.isFileList(h)||M.endsWith(g,"[]"))&&(E=M.toArray(h)))return g=xm(g),E.forEach(function(w,O){!(M.isUndefined(w)||w===null)&&t.append(a===!0?xc([g],O,i):a===null?g:g+"[]",u(w))}),!1}return Fo(h)?!0:(t.append(xc(y,g,i),u(h)),!1)}const d=[],f=Object.assign(q0,{defaultVisitor:c,convertValue:u,isVisitable:Fo});function m(h,g){if(!M.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+g.join("."));d.push(h),M.forEach(h,function(E,_){(!(M.isUndefined(E)||E===null)&&s.call(t,E,M.isString(_)?_.trim():_,g,f))===!0&&m(E,g?g.concat(_):[_])}),d.pop()}}if(!M.isObject(e))throw new TypeError("data must be an object");return m(e),t}function _c(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Pl(e,t){this._pairs=[],e&&pa(e,this,t)}const _m=Pl.prototype;_m.append=function(t,n){this._pairs.push([t,n])};_m.toString=function(t){const n=t?function(r){return t.call(this,r,_c)}:_c;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Z0(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Em(e,t,n){if(!t)return e;const r=n&&n.encode||Z0,s=n&&n.serialize;let i;if(s?i=s(t,n):i=M.isURLSearchParams(t)?t.toString():new Pl(t,n).toString(r),i){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Y0{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){M.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ec=Y0,Tm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},G0=typeof URLSearchParams<"u"?URLSearchParams:Pl,K0=typeof FormData<"u"?FormData:null,J0=typeof Blob<"u"?Blob:null,X0=(()=>{let e;return typeof navigator<"u"&&((e=navigator.product)==="ReactNative"||e==="NativeScript"||e==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),Q0=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Xt={isBrowser:!0,classes:{URLSearchParams:G0,FormData:K0,Blob:J0},isStandardBrowserEnv:X0,isStandardBrowserWebWorkerEnv:Q0,protocols:["http","https","file","blob","url","data"]};function ew(e,t){return pa(e,new Xt.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,i){return Xt.isNode&&M.isBuffer(n)?(this.append(r,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function tw(e){return M.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function nw(e){const t={},n=Object.keys(e);let r;const s=n.length;let i;for(r=0;r<s;r++)i=n[r],t[i]=e[i];return t}function km(e){function t(n,r,s,i){let a=n[i++];const o=Number.isFinite(+a),l=i>=n.length;return a=!a&&M.isArray(s)?s.length:a,l?(M.hasOwnProp(s,a)?s[a]=[s[a],r]:s[a]=r,!o):((!s[a]||!M.isObject(s[a]))&&(s[a]=[]),t(n,r,s[a],i)&&M.isArray(s[a])&&(s[a]=nw(s[a])),!o)}if(M.isFormData(e)&&M.isFunction(e.entries)){const n={};return M.forEachEntry(e,(r,s)=>{t(tw(r),s,n,0)}),n}return null}const rw={"Content-Type":void 0};function sw(e,t,n){if(M.isString(e))try{return(t||JSON.parse)(e),M.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const ba={transitional:Tm,adapter:["xhr","http"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,i=M.isObject(t);if(i&&M.isHTMLForm(t)&&(t=new FormData(t)),M.isFormData(t))return s&&s?JSON.stringify(km(t)):t;if(M.isArrayBuffer(t)||M.isBuffer(t)||M.isStream(t)||M.isFile(t)||M.isBlob(t))return t;if(M.isArrayBufferView(t))return t.buffer;if(M.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return ew(t,this.formSerializer).toString();if((o=M.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return pa(o?{"files[]":t}:t,l&&new l,this.formSerializer)}}return i||s?(n.setContentType("application/json",!1),sw(t)):t}],transformResponse:[function(t){const n=this.transitional||ba.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(t&&M.isString(t)&&(r&&!this.responseType||s)){const a=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(o){if(a)throw o.name==="SyntaxError"?Ee.from(o,Ee.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Xt.classes.FormData,Blob:Xt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};M.forEach(["delete","get","head"],function(t){ba.headers[t]={}});M.forEach(["post","put","patch"],function(t){ba.headers[t]=M.merge(rw)});const Ml=ba,iw=M.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),aw=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),n=a.substring(0,s).trim().toLowerCase(),r=a.substring(s+1).trim(),!(!n||t[n]&&iw[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Tc=Symbol("internals");function Qr(e){return e&&String(e).trim().toLowerCase()}function Si(e){return e===!1||e==null?e:M.isArray(e)?e.map(Si):String(e)}function ow(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const lw=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ga(e,t,n,r,s){if(M.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!M.isString(t)){if(M.isString(r))return t.indexOf(r)!==-1;if(M.isRegExp(r))return r.test(t)}}function uw(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function cw(e,t){const n=M.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,i,a){return this[r].call(this,t,s,i,a)},configurable:!0})})}class wa{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function i(o,l,u){const c=Qr(l);if(!c)throw new Error("header name must be a non-empty string");const d=M.findKey(s,c);(!d||s[d]===void 0||u===!0||u===void 0&&s[d]!==!1)&&(s[d||l]=Si(o))}const a=(o,l)=>M.forEach(o,(u,c)=>i(u,c,l));return M.isPlainObject(t)||t instanceof this.constructor?a(t,n):M.isString(t)&&(t=t.trim())&&!lw(t)?a(aw(t),n):t!=null&&i(n,t,r),this}get(t,n){if(t=Qr(t),t){const r=M.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return ow(s);if(M.isFunction(n))return n.call(this,s,r);if(M.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Qr(t),t){const r=M.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Ga(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function i(a){if(a=Qr(a),a){const o=M.findKey(r,a);o&&(!n||Ga(r,r[o],o,n))&&(delete r[o],s=!0)}}return M.isArray(t)?t.forEach(i):i(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const i=n[r];(!t||Ga(this,this[i],i,t,!0))&&(delete this[i],s=!0)}return s}normalize(t){const n=this,r={};return M.forEach(this,(s,i)=>{const a=M.findKey(r,i);if(a){n[a]=Si(s),delete n[i];return}const o=t?uw(i):String(i).trim();o!==i&&delete n[i],n[o]=Si(s),r[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return M.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&M.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Tc]=this[Tc]={accessors:{}}).accessors,s=this.prototype;function i(a){const o=Qr(a);r[o]||(cw(s,a),r[o]=!0)}return M.isArray(t)?t.forEach(i):i(t),this}}wa.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);M.freezeMethods(wa.prototype);M.freezeMethods(wa);const un=wa;function Ka(e,t){const n=this||Ml,r=t||n,s=un.from(r.headers);let i=r.data;return M.forEach(e,function(o){i=o.call(n,i,s.normalize(),t?t.status:void 0)}),s.normalize(),i}function Om(e){return!!(e&&e.__CANCEL__)}function Ws(e,t,n){Ee.call(this,e??"canceled",Ee.ERR_CANCELED,t,n),this.name="CanceledError"}M.inherits(Ws,Ee,{__CANCEL__:!0});function dw(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Ee("Request failed with status code "+n.status,[Ee.ERR_BAD_REQUEST,Ee.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}const fw=Xt.isStandardBrowserEnv?function(){return{write:function(n,r,s,i,a,o){const l=[];l.push(n+"="+encodeURIComponent(r)),M.isNumber(s)&&l.push("expires="+new Date(s).toGMTString()),M.isString(i)&&l.push("path="+i),M.isString(a)&&l.push("domain="+a),o===!0&&l.push("secure"),document.cookie=l.join("; ")},read:function(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function mw(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function hw(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function Im(e,t){return e&&!mw(t)?hw(e,t):t}const vw=Xt.isStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function s(i){let a=i;return t&&(n.setAttribute("href",a),a=n.href),n.setAttribute("href",a),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=s(window.location.href),function(a){const o=M.isString(a)?s(a):a;return o.protocol===r.protocol&&o.host===r.host}}():function(){return function(){return!0}}();function gw(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function yw(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,i=0,a;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=r[i];a||(a=u),n[s]=l,r[s]=u;let d=i,f=0;for(;d!==s;)f+=n[d++],d=d%e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),u-a<t)return;const m=c&&u-c;return m?Math.round(f*1e3/m):void 0}}function kc(e,t){let n=0;const r=yw(50,250);return s=>{const i=s.loaded,a=s.lengthComputable?s.total:void 0,o=i-n,l=r(o),u=i<=a;n=i;const c={loaded:i,total:a,progress:a?i/a:void 0,bytes:o,rate:l||void 0,estimated:l&&a&&u?(a-i)/l:void 0,event:s};c[t?"download":"upload"]=!0,e(c)}}const pw=typeof XMLHttpRequest<"u",bw=pw&&function(e){return new Promise(function(n,r){let s=e.data;const i=un.from(e.headers).normalize(),a=e.responseType;let o;function l(){e.cancelToken&&e.cancelToken.unsubscribe(o),e.signal&&e.signal.removeEventListener("abort",o)}M.isFormData(s)&&(Xt.isStandardBrowserEnv||Xt.isStandardBrowserWebWorkerEnv?i.setContentType(!1):i.setContentType("multipart/form-data;",!1));let u=new XMLHttpRequest;if(e.auth){const m=e.auth.username||"",h=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";i.set("Authorization","Basic "+btoa(m+":"+h))}const c=Im(e.baseURL,e.url);u.open(e.method.toUpperCase(),Em(c,e.params,e.paramsSerializer),!0),u.timeout=e.timeout;function d(){if(!u)return;const m=un.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders()),g={data:!a||a==="text"||a==="json"?u.responseText:u.response,status:u.status,statusText:u.statusText,headers:m,config:e,request:u};dw(function(E){n(E),l()},function(E){r(E),l()},g),u=null}if("onloadend"in u?u.onloadend=d:u.onreadystatechange=function(){!u||u.readyState!==4||u.status===0&&!(u.responseURL&&u.responseURL.indexOf("file:")===0)||setTimeout(d)},u.onabort=function(){u&&(r(new Ee("Request aborted",Ee.ECONNABORTED,e,u)),u=null)},u.onerror=function(){r(new Ee("Network Error",Ee.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let h=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const g=e.transitional||Tm;e.timeoutErrorMessage&&(h=e.timeoutErrorMessage),r(new Ee(h,g.clarifyTimeoutError?Ee.ETIMEDOUT:Ee.ECONNABORTED,e,u)),u=null},Xt.isStandardBrowserEnv){const m=(e.withCredentials||vw(c))&&e.xsrfCookieName&&fw.read(e.xsrfCookieName);m&&i.set(e.xsrfHeaderName,m)}s===void 0&&i.setContentType(null),"setRequestHeader"in u&&M.forEach(i.toJSON(),function(h,g){u.setRequestHeader(g,h)}),M.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),a&&a!=="json"&&(u.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&u.addEventListener("progress",kc(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&u.upload&&u.upload.addEventListener("progress",kc(e.onUploadProgress)),(e.cancelToken||e.signal)&&(o=m=>{u&&(r(!m||m.type?new Ws(null,e,u):m),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(o),e.signal&&(e.signal.aborted?o():e.signal.addEventListener("abort",o)));const f=gw(c);if(f&&Xt.protocols.indexOf(f)===-1){r(new Ee("Unsupported protocol "+f+":",Ee.ERR_BAD_REQUEST,e));return}u.send(s||null)})},Ci={http:j0,xhr:bw};M.forEach(Ci,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ww={getAdapter:e=>{e=M.isArray(e)?e:[e];const{length:t}=e;let n,r;for(let s=0;s<t&&(n=e[s],!(r=M.isString(n)?Ci[n.toLowerCase()]:n));s++);if(!r)throw r===!1?new Ee(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(M.hasOwnProp(Ci,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`);if(!M.isFunction(r))throw new TypeError("adapter is not a function");return r},adapters:Ci};function Ja(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ws(null,e)}function Oc(e){return Ja(e),e.headers=un.from(e.headers),e.data=Ka.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ww.getAdapter(e.adapter||Ml.adapter)(e).then(function(r){return Ja(e),r.data=Ka.call(e,e.transformResponse,r),r.headers=un.from(r.headers),r},function(r){return Om(r)||(Ja(e),r&&r.response&&(r.response.data=Ka.call(e,e.transformResponse,r.response),r.response.headers=un.from(r.response.headers))),Promise.reject(r)})}const Ic=e=>e instanceof un?e.toJSON():e;function Mr(e,t){t=t||{};const n={};function r(u,c,d){return M.isPlainObject(u)&&M.isPlainObject(c)?M.merge.call({caseless:d},u,c):M.isPlainObject(c)?M.merge({},c):M.isArray(c)?c.slice():c}function s(u,c,d){if(M.isUndefined(c)){if(!M.isUndefined(u))return r(void 0,u,d)}else return r(u,c,d)}function i(u,c){if(!M.isUndefined(c))return r(void 0,c)}function a(u,c){if(M.isUndefined(c)){if(!M.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function o(u,c,d){if(d in t)return r(u,c);if(d in e)return r(void 0,u)}const l={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:o,headers:(u,c)=>s(Ic(u),Ic(c),!0)};return M.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=l[c]||s,f=d(e[c],t[c],c);M.isUndefined(f)&&d!==o||(n[c]=f)}),n}const Am="1.4.0",Fl={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Fl[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ac={};Fl.transitional=function(t,n,r){function s(i,a){return"[Axios v"+Am+"] Transitional option '"+i+"'"+a+(r?". "+r:"")}return(i,a,o)=>{if(t===!1)throw new Ee(s(a," has been removed"+(n?" in "+n:"")),Ee.ERR_DEPRECATED);return n&&!Ac[a]&&(Ac[a]=!0,console.warn(s(a," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,a,o):!0}};function Sw(e,t,n){if(typeof e!="object")throw new Ee("options must be an object",Ee.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const i=r[s],a=t[i];if(a){const o=e[i],l=o===void 0||a(o,i,e);if(l!==!0)throw new Ee("option "+i+" must be "+l,Ee.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Ee("Unknown option "+i,Ee.ERR_BAD_OPTION)}}const Do={assertOptions:Sw,validators:Fl},_n=Do.validators;class Bi{constructor(t){this.defaults=t,this.interceptors={request:new Ec,response:new Ec}}request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Mr(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:i}=n;r!==void 0&&Do.assertOptions(r,{silentJSONParsing:_n.transitional(_n.boolean),forcedJSONParsing:_n.transitional(_n.boolean),clarifyTimeoutError:_n.transitional(_n.boolean)},!1),s!=null&&(M.isFunction(s)?n.paramsSerializer={serialize:s}:Do.assertOptions(s,{encode:_n.function,serialize:_n.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let a;a=i&&M.merge(i.common,i[n.method]),a&&M.forEach(["delete","get","head","post","put","patch","common"],h=>{delete i[h]}),n.headers=un.concat(a,i);const o=[];let l=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(l=l&&g.synchronous,o.unshift(g.fulfilled,g.rejected))});const u=[];this.interceptors.response.forEach(function(g){u.push(g.fulfilled,g.rejected)});let c,d=0,f;if(!l){const h=[Oc.bind(this),void 0];for(h.unshift.apply(h,o),h.push.apply(h,u),f=h.length,c=Promise.resolve(n);d<f;)c=c.then(h[d++],h[d++]);return c}f=o.length;let m=n;for(d=0;d<f;){const h=o[d++],g=o[d++];try{m=h(m)}catch(y){g.call(this,y);break}}try{c=Oc.call(this,m)}catch(h){return Promise.reject(h)}for(d=0,f=u.length;d<f;)c=c.then(u[d++],u[d++]);return c}getUri(t){t=Mr(this.defaults,t);const n=Im(t.baseURL,t.url);return Em(n,t.params,t.paramsSerializer)}}M.forEach(["delete","get","head","options"],function(t){Bi.prototype[t]=function(n,r){return this.request(Mr(r||{},{method:t,url:n,data:(r||{}).data}))}});M.forEach(["post","put","patch"],function(t){function n(r){return function(i,a,o){return this.request(Mr(o||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:a}))}}Bi.prototype[t]=n(),Bi.prototype[t+"Form"]=n(!0)});const xi=Bi;class Dl{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const r=this;this.promise.then(s=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](s);r._listeners=null}),this.promise.then=s=>{let i;const a=new Promise(o=>{r.subscribe(o),i=o}).then(s);return a.cancel=function(){r.unsubscribe(i)},a},t(function(i,a,o){r.reason||(r.reason=new Ws(i,a,o),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new Dl(function(s){t=s}),cancel:t}}}const Cw=Dl;function xw(e){return function(n){return e.apply(null,n)}}function _w(e){return M.isObject(e)&&e.isAxiosError===!0}const No={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(No).forEach(([e,t])=>{No[t]=e});const Ew=No;function Vm(e){const t=new xi(e),n=mm(xi.prototype.request,t);return M.extend(n,xi.prototype,t,{allOwnKeys:!0}),M.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Vm(Mr(e,s))},n}const Je=Vm(Ml);Je.Axios=xi;Je.CanceledError=Ws;Je.CancelToken=Cw;Je.isCancel=Om;Je.VERSION=Am;Je.toFormData=pa;Je.AxiosError=Ee;Je.Cancel=Je.CanceledError;Je.all=function(t){return Promise.all(t)};Je.spread=xw;Je.isAxiosError=_w;Je.mergeConfig=Mr;Je.AxiosHeaders=un;Je.formToJSON=e=>km(M.isHTMLForm(e)?new FormData(e):e);Je.HttpStatusCode=Ew;Je.default=Je;const Tw=Je,Sa=window.location.origin,Ca=window.location.pathname.split("/");`${Sa}${Ca[1]}`;`${Sa}${Ca[1]}`;const kw=`${Sa}/${Ca[1]}/logout.php`,Ow=`${Sa}/${Ca[1]}`,Nl=window.location.pathname;Nl.split("/");console.log("path",Nl);Nl[1];const Ll=window.location.href,Iw=Ll.lastIndexOf("/"),Aw=Ll.lastIndexOf("/",Iw-1),Vw=Ll.slice(0,Aw),Jt=Tw.create();var Pw=localStorage.getItem("token");Jt.interceptors.request.use(async e=>(e.baseURL=Vw,e.headers={"Content-Type":"application/json",Authorization:`Bearer ${Pw}`,Accept:"application/json","Access-Control-Allow-Methods":"GET,PUT,POST"},e.withCredentials=!1,e.crossDomain=!0,e),e=>{Promise.reject(e)});Jt.interceptors.response.use(e=>e,async function(e){if((e==null?void 0:e.response.status)==401){window.parent.location.replace=kw;return}return(e==null?void 0:e.response.status)==404&&(window.parent.location.href=Ow),Promise.reject(e)});class hr extends Error{}class Mw extends hr{constructor(t){super(`Invalid DateTime: ${t.toMessage()}`)}}class Fw extends hr{constructor(t){super(`Invalid Interval: ${t.toMessage()}`)}}class Dw extends hr{constructor(t){super(`Invalid Duration: ${t.toMessage()}`)}}class is extends hr{}class Pm extends hr{constructor(t){super(`Invalid unit ${t}`)}}class _t extends hr{}class En extends hr{constructor(){super("Zone is an abstract class")}}const q="numeric",Wt="short",bt="long",Hi={year:q,month:q,day:q},Mm={year:q,month:Wt,day:q},Nw={year:q,month:Wt,day:q,weekday:Wt},Fm={year:q,month:bt,day:q},Dm={year:q,month:bt,day:q,weekday:bt},Nm={hour:q,minute:q},Lm={hour:q,minute:q,second:q},Rm={hour:q,minute:q,second:q,timeZoneName:Wt},$m={hour:q,minute:q,second:q,timeZoneName:bt},Bm={hour:q,minute:q,hourCycle:"h23"},Hm={hour:q,minute:q,second:q,hourCycle:"h23"},Um={hour:q,minute:q,second:q,hourCycle:"h23",timeZoneName:Wt},zm={hour:q,minute:q,second:q,hourCycle:"h23",timeZoneName:bt},jm={year:q,month:q,day:q,hour:q,minute:q},Wm={year:q,month:q,day:q,hour:q,minute:q,second:q},qm={year:q,month:Wt,day:q,hour:q,minute:q},Zm={year:q,month:Wt,day:q,hour:q,minute:q,second:q},Lw={year:q,month:Wt,day:q,weekday:Wt,hour:q,minute:q},Ym={year:q,month:bt,day:q,hour:q,minute:q,timeZoneName:Wt},Gm={year:q,month:bt,day:q,hour:q,minute:q,second:q,timeZoneName:Wt},Km={year:q,month:bt,day:q,weekday:bt,hour:q,minute:q,timeZoneName:bt},Jm={year:q,month:bt,day:q,weekday:bt,hour:q,minute:q,second:q,timeZoneName:bt};class qs{get type(){throw new En}get name(){throw new En}get ianaName(){return this.name}get isUniversal(){throw new En}offsetName(t,n){throw new En}formatOffset(t,n){throw new En}offset(t){throw new En}equals(t){throw new En}get isValid(){throw new En}}let Xa=null;class xa extends qs{static get instance(){return Xa===null&&(Xa=new xa),Xa}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(t,{format:n,locale:r}){return Qm(t,n,r)}formatOffset(t,n){return ms(this.offset(t),n)}offset(t){return-new Date(t).getTimezoneOffset()}equals(t){return t.type==="system"}get isValid(){return!0}}let _i={};function Rw(e){return _i[e]||(_i[e]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),_i[e]}const $w={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Bw(e,t){const n=e.format(t).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,s,i,a,o,l,u,c]=r;return[a,s,i,o,l,u,c]}function Hw(e,t){const n=e.formatToParts(t),r=[];for(let s=0;s<n.length;s++){const{type:i,value:a}=n[s],o=$w[i];i==="era"?r[o]=a:we(o)||(r[o]=parseInt(a,10))}return r}let oi={};class hn extends qs{static create(t){return oi[t]||(oi[t]=new hn(t)),oi[t]}static resetCache(){oi={},_i={}}static isValidSpecifier(t){return this.isValidZone(t)}static isValidZone(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch{return!1}}constructor(t){super(),this.zoneName=t,this.valid=hn.isValidZone(t)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(t,{format:n,locale:r}){return Qm(t,n,r,this.name)}formatOffset(t,n){return ms(this.offset(t),n)}offset(t){const n=new Date(t);if(isNaN(n))return NaN;const r=Rw(this.name);let[s,i,a,o,l,u,c]=r.formatToParts?Hw(r,n):Bw(r,n);o==="BC"&&(s=-Math.abs(s)+1);const f=Bl({year:s,month:i,day:a,hour:l===24?0:l,minute:u,second:c,millisecond:0});let m=+n;const h=m%1e3;return m-=h>=0?h:1e3+h,(f-m)/(60*1e3)}equals(t){return t.type==="iana"&&t.name===this.name}get isValid(){return this.valid}}let Vc={};function Uw(e,t={}){const n=JSON.stringify([e,t]);let r=Vc[n];return r||(r=new Intl.ListFormat(e,t),Vc[n]=r),r}let Lo={};function Ro(e,t={}){const n=JSON.stringify([e,t]);let r=Lo[n];return r||(r=new Intl.DateTimeFormat(e,t),Lo[n]=r),r}let $o={};function zw(e,t={}){const n=JSON.stringify([e,t]);let r=$o[n];return r||(r=new Intl.NumberFormat(e,t),$o[n]=r),r}let Bo={};function jw(e,t={}){const{base:n,...r}=t,s=JSON.stringify([e,r]);let i=Bo[s];return i||(i=new Intl.RelativeTimeFormat(e,t),Bo[s]=i),i}let as=null;function Ww(){return as||(as=new Intl.DateTimeFormat().resolvedOptions().locale,as)}function qw(e){const t=e.indexOf("-x-");t!==-1&&(e=e.substring(0,t));const n=e.indexOf("-u-");if(n===-1)return[e];{let r,s;try{r=Ro(e).resolvedOptions(),s=e}catch{const l=e.substring(0,n);r=Ro(l).resolvedOptions(),s=l}const{numberingSystem:i,calendar:a}=r;return[s,i,a]}}function Zw(e,t,n){return(n||t)&&(e.includes("-u-")||(e+="-u"),n&&(e+=`-ca-${n}`),t&&(e+=`-nu-${t}`)),e}function Yw(e){const t=[];for(let n=1;n<=12;n++){const r=Q.utc(2016,n,1);t.push(e(r))}return t}function Gw(e){const t=[];for(let n=1;n<=7;n++){const r=Q.utc(2016,11,13+n);t.push(e(r))}return t}function li(e,t,n,r,s){const i=e.listingMode(n);return i==="error"?null:i==="en"?r(t):s(t)}function Kw(e){return e.numberingSystem&&e.numberingSystem!=="latn"?!1:e.numberingSystem==="latn"||!e.locale||e.locale.startsWith("en")||new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem==="latn"}class Jw{constructor(t,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;const{padTo:s,floor:i,...a}=r;if(!n||Object.keys(a).length>0){const o={useGrouping:!1,...r};r.padTo>0&&(o.minimumIntegerDigits=r.padTo),this.inf=zw(t,o)}}format(t){if(this.inf){const n=this.floor?Math.floor(t):t;return this.inf.format(n)}else{const n=this.floor?Math.floor(t):$l(t,3);return qe(n,this.padTo)}}}class Xw{constructor(t,n,r){this.opts=r,this.originalZone=void 0;let s;if(this.opts.timeZone)this.dt=t;else if(t.zone.type==="fixed"){const a=-1*(t.offset/60),o=a>=0?`Etc/GMT+${a}`:`Etc/GMT${a}`;t.offset!==0&&hn.create(o).valid?(s=o,this.dt=t):(s="UTC",this.dt=t.offset===0?t:t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone)}else t.zone.type==="system"?this.dt=t:t.zone.type==="iana"?(this.dt=t,s=t.zone.name):(s="UTC",this.dt=t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone);const i={...this.opts};i.timeZone=i.timeZone||s,this.dtf=Ro(n,i)}format(){return this.originalZone?this.formatToParts().map(({value:t})=>t).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const t=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?t.map(n=>{if(n.type==="timeZoneName"){const r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):t}resolvedOptions(){return this.dtf.resolvedOptions()}}class Qw{constructor(t,n,r){this.opts={style:"long",...r},!n&&Xm()&&(this.rtf=jw(t,r))}format(t,n){return this.rtf?this.rtf.format(t,n):gS(n,t,this.opts.numeric,this.opts.style!=="long")}formatToParts(t,n){return this.rtf?this.rtf.formatToParts(t,n):[]}}class Me{static fromOpts(t){return Me.create(t.locale,t.numberingSystem,t.outputCalendar,t.defaultToEN)}static create(t,n,r,s=!1){const i=t||Ue.defaultLocale,a=i||(s?"en-US":Ww()),o=n||Ue.defaultNumberingSystem,l=r||Ue.defaultOutputCalendar;return new Me(a,o,l,i)}static resetCache(){as=null,Lo={},$o={},Bo={}}static fromObject({locale:t,numberingSystem:n,outputCalendar:r}={}){return Me.create(t,n,r)}constructor(t,n,r,s){const[i,a,o]=qw(t);this.locale=i,this.numberingSystem=n||a||null,this.outputCalendar=r||o||null,this.intl=Zw(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=s,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=Kw(this)),this.fastNumbersCached}listingMode(){const t=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return t&&n?"en":"intl"}clone(t){return!t||Object.getOwnPropertyNames(t).length===0?this:Me.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,t.defaultToEN||!1)}redefaultToEN(t={}){return this.clone({...t,defaultToEN:!0})}redefaultToSystem(t={}){return this.clone({...t,defaultToEN:!1})}months(t,n=!1,r=!0){return li(this,t,r,nh,()=>{const s=n?{month:t,day:"numeric"}:{month:t},i=n?"format":"standalone";return this.monthsCache[i][t]||(this.monthsCache[i][t]=Yw(a=>this.extract(a,s,"month"))),this.monthsCache[i][t]})}weekdays(t,n=!1,r=!0){return li(this,t,r,ih,()=>{const s=n?{weekday:t,year:"numeric",month:"long",day:"numeric"}:{weekday:t},i=n?"format":"standalone";return this.weekdaysCache[i][t]||(this.weekdaysCache[i][t]=Gw(a=>this.extract(a,s,"weekday"))),this.weekdaysCache[i][t]})}meridiems(t=!0){return li(this,void 0,t,()=>ah,()=>{if(!this.meridiemCache){const n={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[Q.utc(2016,11,13,9),Q.utc(2016,11,13,19)].map(r=>this.extract(r,n,"dayperiod"))}return this.meridiemCache})}eras(t,n=!0){return li(this,t,n,oh,()=>{const r={era:t};return this.eraCache[t]||(this.eraCache[t]=[Q.utc(-40,1,1),Q.utc(2017,1,1)].map(s=>this.extract(s,r,"era"))),this.eraCache[t]})}extract(t,n,r){const s=this.dtFormatter(t,n),i=s.formatToParts(),a=i.find(o=>o.type.toLowerCase()===r);return a?a.value:null}numberFormatter(t={}){return new Jw(this.intl,t.forceSimple||this.fastNumbers,t)}dtFormatter(t,n={}){return new Xw(t,this.intl,n)}relFormatter(t={}){return new Qw(this.intl,this.isEnglish(),t)}listFormatter(t={}){return Uw(this.intl,t)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar}}let Qa=null;class ot extends qs{static get utcInstance(){return Qa===null&&(Qa=new ot(0)),Qa}static instance(t){return t===0?ot.utcInstance:new ot(t)}static parseSpecifier(t){if(t){const n=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new ot(Ea(n[1],n[2]))}return null}constructor(t){super(),this.fixed=t}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${ms(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${ms(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(t,n){return ms(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(t){return t.type==="fixed"&&t.fixed===this.fixed}get isValid(){return!0}}class eS extends qs{constructor(t){super(),this.zoneName=t}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function Vn(e,t){if(we(e)||e===null)return t;if(e instanceof qs)return e;if(tS(e)){const n=e.toLowerCase();return n==="default"?t:n==="local"||n==="system"?xa.instance:n==="utc"||n==="gmt"?ot.utcInstance:ot.parseSpecifier(n)||hn.create(e)}else return or(e)?ot.instance(e):typeof e=="object"&&e.offset&&typeof e.offset=="number"?e:new eS(e)}let Pc=()=>Date.now(),Mc="system",Fc=null,Dc=null,Nc=null,Lc=60,Rc;class Ue{static get now(){return Pc}static set now(t){Pc=t}static set defaultZone(t){Mc=t}static get defaultZone(){return Vn(Mc,xa.instance)}static get defaultLocale(){return Fc}static set defaultLocale(t){Fc=t}static get defaultNumberingSystem(){return Dc}static set defaultNumberingSystem(t){Dc=t}static get defaultOutputCalendar(){return Nc}static set defaultOutputCalendar(t){Nc=t}static get twoDigitCutoffYear(){return Lc}static set twoDigitCutoffYear(t){Lc=t%100}static get throwOnInvalid(){return Rc}static set throwOnInvalid(t){Rc=t}static resetCaches(){Me.resetCache(),hn.resetCache()}}function we(e){return typeof e>"u"}function or(e){return typeof e=="number"}function _a(e){return typeof e=="number"&&e%1===0}function tS(e){return typeof e=="string"}function nS(e){return Object.prototype.toString.call(e)==="[object Date]"}function Xm(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function rS(e){return Array.isArray(e)?e:[e]}function $c(e,t,n){if(e.length!==0)return e.reduce((r,s)=>{const i=[t(s),s];return r&&n(r[0],i[0])===r[0]?r:i},null)[1]}function sS(e,t){return t.reduce((n,r)=>(n[r]=e[r],n),{})}function Fr(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function on(e,t,n){return _a(e)&&e>=t&&e<=n}function iS(e,t){return e-t*Math.floor(e/t)}function qe(e,t=2){const n=e<0;let r;return n?r="-"+(""+-e).padStart(t,"0"):r=(""+e).padStart(t,"0"),r}function An(e){if(!(we(e)||e===null||e===""))return parseInt(e,10)}function Gn(e){if(!(we(e)||e===null||e===""))return parseFloat(e)}function Rl(e){if(!(we(e)||e===null||e==="")){const t=parseFloat("0."+e)*1e3;return Math.floor(t)}}function $l(e,t,n=!1){const r=10**t;return(n?Math.trunc:Math.round)(e*r)/r}function Zs(e){return e%4===0&&(e%100!==0||e%400===0)}function fs(e){return Zs(e)?366:365}function Ui(e,t){const n=iS(t-1,12)+1,r=e+(t-n)/12;return n===2?Zs(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function Bl(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t),t.setUTCFullYear(e.year,e.month-1,e.day)),+t}function zi(e){const t=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7,n=e-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return t===4||r===3?53:52}function Ho(e){return e>99?e:e>Ue.twoDigitCutoffYear?1900+e:2e3+e}function Qm(e,t,n,r=null){const s=new Date(e),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);const a={timeZoneName:t,...i},o=new Intl.DateTimeFormat(n,a).formatToParts(s).find(l=>l.type.toLowerCase()==="timezonename");return o?o.value:null}function Ea(e,t){let n=parseInt(e,10);Number.isNaN(n)&&(n=0);const r=parseInt(t,10)||0,s=n<0||Object.is(n,-0)?-r:r;return n*60+s}function eh(e){const t=Number(e);if(typeof e=="boolean"||e===""||Number.isNaN(t))throw new _t(`Invalid unit value ${e}`);return t}function ji(e,t){const n={};for(const r in e)if(Fr(e,r)){const s=e[r];if(s==null)continue;n[t(r)]=eh(s)}return n}function ms(e,t){const n=Math.trunc(Math.abs(e/60)),r=Math.trunc(Math.abs(e%60)),s=e>=0?"+":"-";switch(t){case"short":return`${s}${qe(n,2)}:${qe(r,2)}`;case"narrow":return`${s}${n}${r>0?`:${r}`:""}`;case"techie":return`${s}${qe(n,2)}${qe(r,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function Ta(e){return sS(e,["hour","minute","second","millisecond"])}const aS=["January","February","March","April","May","June","July","August","September","October","November","December"],th=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],oS=["J","F","M","A","M","J","J","A","S","O","N","D"];function nh(e){switch(e){case"narrow":return[...oS];case"short":return[...th];case"long":return[...aS];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const rh=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],sh=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],lS=["M","T","W","T","F","S","S"];function ih(e){switch(e){case"narrow":return[...lS];case"short":return[...sh];case"long":return[...rh];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const ah=["AM","PM"],uS=["Before Christ","Anno Domini"],cS=["BC","AD"],dS=["B","A"];function oh(e){switch(e){case"narrow":return[...dS];case"short":return[...cS];case"long":return[...uS];default:return null}}function fS(e){return ah[e.hour<12?0:1]}function mS(e,t){return ih(t)[e.weekday-1]}function hS(e,t){return nh(t)[e.month-1]}function vS(e,t){return oh(t)[e.year<0?0:1]}function gS(e,t,n="always",r=!1){const s={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(e)===-1;if(n==="auto"&&i){const d=e==="days";switch(t){case 1:return d?"tomorrow":`next ${s[e][0]}`;case-1:return d?"yesterday":`last ${s[e][0]}`;case 0:return d?"today":`this ${s[e][0]}`}}const a=Object.is(t,-0)||t<0,o=Math.abs(t),l=o===1,u=s[e],c=r?l?u[1]:u[2]||u[1]:l?s[e][0]:e;return a?`${o} ${c} ago`:`in ${o} ${c}`}function Bc(e,t){let n="";for(const r of e)r.literal?n+=r.val:n+=t(r.val);return n}const yS={D:Hi,DD:Mm,DDD:Fm,DDDD:Dm,t:Nm,tt:Lm,ttt:Rm,tttt:$m,T:Bm,TT:Hm,TTT:Um,TTTT:zm,f:jm,ff:qm,fff:Ym,ffff:Km,F:Wm,FF:Zm,FFF:Gm,FFFF:Jm};class st{static create(t,n={}){return new st(t,n)}static parseFormat(t){let n=null,r="",s=!1;const i=[];for(let a=0;a<t.length;a++){const o=t.charAt(a);o==="'"?(r.length>0&&i.push({literal:s||/^\s+$/.test(r),val:r}),n=null,r="",s=!s):s||o===n?r+=o:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=o,n=o)}return r.length>0&&i.push({literal:s||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(t){return yS[t]}constructor(t,n){this.opts=n,this.loc=t,this.systemLoc=null}formatWithSystemDefault(t,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,{...this.opts,...n}).format()}formatDateTime(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n}).format()}formatDateTimeParts(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n}).formatToParts()}formatInterval(t,n={}){return this.loc.dtFormatter(t.start,{...this.opts,...n}).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())}resolvedOptions(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n}).resolvedOptions()}num(t,n=0){if(this.opts.forceSimple)return qe(t,n);const r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(t)}formatDateTimeFromString(t,n){const r=this.loc.listingMode()==="en",s=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(m,h)=>this.loc.extract(t,m,h),a=m=>t.isOffsetFixed&&t.offset===0&&m.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,m.format):"",o=()=>r?fS(t):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(m,h)=>r?hS(t,m):i(h?{month:m}:{month:m,day:"numeric"},"month"),u=(m,h)=>r?mS(t,m):i(h?{weekday:m}:{weekday:m,month:"long",day:"numeric"},"weekday"),c=m=>{const h=st.macroTokenToFormatOpts(m);return h?this.formatWithSystemDefault(t,h):m},d=m=>r?vS(t,m):i({era:m},"era"),f=m=>{switch(m){case"S":return this.num(t.millisecond);case"u":case"SSS":return this.num(t.millisecond,3);case"s":return this.num(t.second);case"ss":return this.num(t.second,2);case"uu":return this.num(Math.floor(t.millisecond/10),2);case"uuu":return this.num(Math.floor(t.millisecond/100));case"m":return this.num(t.minute);case"mm":return this.num(t.minute,2);case"h":return this.num(t.hour%12===0?12:t.hour%12);case"hh":return this.num(t.hour%12===0?12:t.hour%12,2);case"H":return this.num(t.hour);case"HH":return this.num(t.hour,2);case"Z":return a({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return a({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return a({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:this.loc.locale});case"z":return t.zoneName;case"a":return o();case"d":return s?i({day:"numeric"},"day"):this.num(t.day);case"dd":return s?i({day:"2-digit"},"day"):this.num(t.day,2);case"c":return this.num(t.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(t.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return s?i({month:"numeric",day:"numeric"},"month"):this.num(t.month);case"LL":return s?i({month:"2-digit",day:"numeric"},"month"):this.num(t.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return s?i({month:"numeric"},"month"):this.num(t.month);case"MM":return s?i({month:"2-digit"},"month"):this.num(t.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return s?i({year:"numeric"},"year"):this.num(t.year);case"yy":return s?i({year:"2-digit"},"year"):this.num(t.year.toString().slice(-2),2);case"yyyy":return s?i({year:"numeric"},"year"):this.num(t.year,4);case"yyyyyy":return s?i({year:"numeric"},"year"):this.num(t.year,6);case"G":return d("short");case"GG":return d("long");case"GGGGG":return d("narrow");case"kk":return this.num(t.weekYear.toString().slice(-2),2);case"kkkk":return this.num(t.weekYear,4);case"W":return this.num(t.weekNumber);case"WW":return this.num(t.weekNumber,2);case"o":return this.num(t.ordinal);case"ooo":return this.num(t.ordinal,3);case"q":return this.num(t.quarter);case"qq":return this.num(t.quarter,2);case"X":return this.num(Math.floor(t.ts/1e3));case"x":return this.num(t.ts);default:return c(m)}};return Bc(st.parseFormat(n),f)}formatDurationFromString(t,n){const r=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},s=l=>u=>{const c=r(u);return c?this.num(l.get(c),u.length):u},i=st.parseFormat(n),a=i.reduce((l,{literal:u,val:c})=>u?l:l.concat(c),[]),o=t.shiftTo(...a.map(r).filter(l=>l));return Bc(i,s(o))}}class Ut{constructor(t,n){this.reason=t,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const lh=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function jr(...e){const t=e.reduce((n,r)=>n+r.source,"");return RegExp(`^${t}$`)}function Wr(...e){return t=>e.reduce(([n,r,s],i)=>{const[a,o,l]=i(t,s);return[{...n,...a},o||r,l]},[{},null,1]).slice(0,2)}function qr(e,...t){if(e==null)return[null,null];for(const[n,r]of t){const s=n.exec(e);if(s)return r(s)}return[null,null]}function uh(...e){return(t,n)=>{const r={};let s;for(s=0;s<e.length;s++)r[e[s]]=An(t[n+s]);return[r,null,n+s]}}const ch=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,pS=`(?:${ch.source}?(?:\\[(${lh.source})\\])?)?`,Hl=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,dh=RegExp(`${Hl.source}${pS}`),Ul=RegExp(`(?:T${dh.source})?`),bS=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,wS=/(\d{4})-?W(\d\d)(?:-?(\d))?/,SS=/(\d{4})-?(\d{3})/,CS=uh("weekYear","weekNumber","weekDay"),xS=uh("year","ordinal"),_S=/(\d{4})-(\d\d)-(\d\d)/,fh=RegExp(`${Hl.source} ?(?:${ch.source}|(${lh.source}))?`),ES=RegExp(`(?: ${fh.source})?`);function Ir(e,t,n){const r=e[t];return we(r)?n:An(r)}function TS(e,t){return[{year:Ir(e,t),month:Ir(e,t+1,1),day:Ir(e,t+2,1)},null,t+3]}function Zr(e,t){return[{hours:Ir(e,t,0),minutes:Ir(e,t+1,0),seconds:Ir(e,t+2,0),milliseconds:Rl(e[t+3])},null,t+4]}function Ys(e,t){const n=!e[t]&&!e[t+1],r=Ea(e[t+1],e[t+2]),s=n?null:ot.instance(r);return[{},s,t+3]}function Gs(e,t){const n=e[t]?hn.create(e[t]):null;return[{},n,t+1]}const kS=RegExp(`^T?${Hl.source}$`),OS=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function IS(e){const[t,n,r,s,i,a,o,l,u]=e,c=t[0]==="-",d=l&&l[0]==="-",f=(m,h=!1)=>m!==void 0&&(h||m&&c)?-m:m;return[{years:f(Gn(n)),months:f(Gn(r)),weeks:f(Gn(s)),days:f(Gn(i)),hours:f(Gn(a)),minutes:f(Gn(o)),seconds:f(Gn(l),l==="-0"),milliseconds:f(Rl(u),d)}]}const AS={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function zl(e,t,n,r,s,i,a){const o={year:t.length===2?Ho(An(t)):An(t),month:th.indexOf(n)+1,day:An(r),hour:An(s),minute:An(i)};return a&&(o.second=An(a)),e&&(o.weekday=e.length>3?rh.indexOf(e)+1:sh.indexOf(e)+1),o}const VS=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function PS(e){const[,t,n,r,s,i,a,o,l,u,c,d]=e,f=zl(t,s,r,n,i,a,o);let m;return l?m=AS[l]:u?m=0:m=Ea(c,d),[f,new ot(m)]}function MS(e){return e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}const FS=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,DS=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,NS=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Hc(e){const[,t,n,r,s,i,a,o]=e;return[zl(t,s,r,n,i,a,o),ot.utcInstance]}function LS(e){const[,t,n,r,s,i,a,o]=e;return[zl(t,o,n,r,s,i,a),ot.utcInstance]}const RS=jr(bS,Ul),$S=jr(wS,Ul),BS=jr(SS,Ul),HS=jr(dh),mh=Wr(TS,Zr,Ys,Gs),US=Wr(CS,Zr,Ys,Gs),zS=Wr(xS,Zr,Ys,Gs),jS=Wr(Zr,Ys,Gs);function WS(e){return qr(e,[RS,mh],[$S,US],[BS,zS],[HS,jS])}function qS(e){return qr(MS(e),[VS,PS])}function ZS(e){return qr(e,[FS,Hc],[DS,Hc],[NS,LS])}function YS(e){return qr(e,[OS,IS])}const GS=Wr(Zr);function KS(e){return qr(e,[kS,GS])}const JS=jr(_S,ES),XS=jr(fh),QS=Wr(Zr,Ys,Gs);function e1(e){return qr(e,[JS,mh],[XS,QS])}const t1="Invalid Duration",hh={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},n1={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...hh},xt=146097/400,br=146097/4800,r1={years:{quarters:4,months:12,weeks:xt/7,days:xt,hours:xt*24,minutes:xt*24*60,seconds:xt*24*60*60,milliseconds:xt*24*60*60*1e3},quarters:{months:3,weeks:xt/28,days:xt/4,hours:xt*24/4,minutes:xt*24*60/4,seconds:xt*24*60*60/4,milliseconds:xt*24*60*60*1e3/4},months:{weeks:br/7,days:br,hours:br*24,minutes:br*24*60,seconds:br*24*60*60,milliseconds:br*24*60*60*1e3},...hh},Kn=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],s1=Kn.slice(0).reverse();function Tn(e,t,n=!1){const r={values:n?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix};return new xe(r)}function i1(e){return e<0?Math.floor(e):Math.ceil(e)}function vh(e,t,n,r,s){const i=e[s][n],a=t[n]/i,o=Math.sign(a)===Math.sign(r[s]),l=!o&&r[s]!==0&&Math.abs(a)<=1?i1(a):Math.trunc(a);r[s]+=l,t[n]-=l*i}function a1(e,t){s1.reduce((n,r)=>we(t[r])?n:(n&&vh(e,t,n,t,r),r),null)}function o1(e){const t={};for(const[n,r]of Object.entries(e))r!==0&&(t[n]=r);return t}class xe{constructor(t){const n=t.conversionAccuracy==="longterm"||!1;let r=n?r1:n1;t.matrix&&(r=t.matrix),this.values=t.values,this.loc=t.loc||Me.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(t,n){return xe.fromObject({milliseconds:t},n)}static fromObject(t,n={}){if(t==null||typeof t!="object")throw new _t(`Duration.fromObject: argument expected to be an object, got ${t===null?"null":typeof t}`);return new xe({values:ji(t,xe.normalizeUnit),loc:Me.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(t){if(or(t))return xe.fromMillis(t);if(xe.isDuration(t))return t;if(typeof t=="object")return xe.fromObject(t);throw new _t(`Unknown duration argument ${t} of type ${typeof t}`)}static fromISO(t,n){const[r]=YS(t);return r?xe.fromObject(r,n):xe.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static fromISOTime(t,n){const[r]=KS(t);return r?xe.fromObject(r,n):xe.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static invalid(t,n=null){if(!t)throw new _t("need to specify a reason the Duration is invalid");const r=t instanceof Ut?t:new Ut(t,n);if(Ue.throwOnInvalid)throw new Dw(r);return new xe({invalid:r})}static normalizeUnit(t){const n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t&&t.toLowerCase()];if(!n)throw new Pm(t);return n}static isDuration(t){return t&&t.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(t,n={}){const r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?st.create(this.loc,r).formatDurationFromString(this,t):t1}toHuman(t={}){const n=Kn.map(r=>{const s=this.values[r];return we(s)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...t,unit:r.slice(0,-1)}).format(s)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:t.listStyle||"narrow",...t}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let t="P";return this.years!==0&&(t+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(t+=this.months+this.quarters*3+"M"),this.weeks!==0&&(t+=this.weeks+"W"),this.days!==0&&(t+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(t+="T"),this.hours!==0&&(t+=this.hours+"H"),this.minutes!==0&&(t+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(t+=$l(this.seconds+this.milliseconds/1e3,3)+"S"),t==="P"&&(t+="T0S"),t}toISOTime(t={}){if(!this.isValid)return null;const n=this.toMillis();if(n<0||n>=864e5)return null;t={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...t};const r=this.shiftTo("hours","minutes","seconds","milliseconds");let s=t.format==="basic"?"hhmm":"hh:mm";(!t.suppressSeconds||r.seconds!==0||r.milliseconds!==0)&&(s+=t.format==="basic"?"ss":":ss",(!t.suppressMilliseconds||r.milliseconds!==0)&&(s+=".SSS"));let i=r.toFormat(s);return t.includePrefix&&(i="T"+i),i}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.as("milliseconds")}valueOf(){return this.toMillis()}plus(t){if(!this.isValid)return this;const n=xe.fromDurationLike(t),r={};for(const s of Kn)(Fr(n.values,s)||Fr(this.values,s))&&(r[s]=n.get(s)+this.get(s));return Tn(this,{values:r},!0)}minus(t){if(!this.isValid)return this;const n=xe.fromDurationLike(t);return this.plus(n.negate())}mapUnits(t){if(!this.isValid)return this;const n={};for(const r of Object.keys(this.values))n[r]=eh(t(this.values[r],r));return Tn(this,{values:n},!0)}get(t){return this[xe.normalizeUnit(t)]}set(t){if(!this.isValid)return this;const n={...this.values,...ji(t,xe.normalizeUnit)};return Tn(this,{values:n})}reconfigure({locale:t,numberingSystem:n,conversionAccuracy:r,matrix:s}={}){const a={loc:this.loc.clone({locale:t,numberingSystem:n}),matrix:s,conversionAccuracy:r};return Tn(this,a)}as(t){return this.isValid?this.shiftTo(t).get(t):NaN}normalize(){if(!this.isValid)return this;const t=this.toObject();return a1(this.matrix,t),Tn(this,{values:t},!0)}rescale(){if(!this.isValid)return this;const t=o1(this.normalize().shiftToAll().toObject());return Tn(this,{values:t},!0)}shiftTo(...t){if(!this.isValid)return this;if(t.length===0)return this;t=t.map(a=>xe.normalizeUnit(a));const n={},r={},s=this.toObject();let i;for(const a of Kn)if(t.indexOf(a)>=0){i=a;let o=0;for(const u in r)o+=this.matrix[u][a]*r[u],r[u]=0;or(s[a])&&(o+=s[a]);const l=Math.trunc(o);n[a]=l,r[a]=(o*1e3-l*1e3)/1e3;for(const u in s)Kn.indexOf(u)>Kn.indexOf(a)&&vh(this.matrix,s,u,n,a)}else or(s[a])&&(r[a]=s[a]);for(const a in r)r[a]!==0&&(n[i]+=a===i?r[a]:r[a]/this.matrix[i][a]);return Tn(this,{values:n},!0).normalize()}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const t={};for(const n of Object.keys(this.values))t[n]=this.values[n]===0?0:-this.values[n];return Tn(this,{values:t},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(t){if(!this.isValid||!t.isValid||!this.loc.equals(t.loc))return!1;function n(r,s){return r===void 0||r===0?s===void 0||s===0:r===s}for(const r of Kn)if(!n(this.values[r],t.values[r]))return!1;return!0}}const wr="Invalid Interval";function l1(e,t){return!e||!e.isValid?Ne.invalid("missing or invalid start"):!t||!t.isValid?Ne.invalid("missing or invalid end"):t<e?Ne.invalid("end before start",`The end of an interval must be after its start, but you had start=${e.toISO()} and end=${t.toISO()}`):null}class Ne{constructor(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}static invalid(t,n=null){if(!t)throw new _t("need to specify a reason the Interval is invalid");const r=t instanceof Ut?t:new Ut(t,n);if(Ue.throwOnInvalid)throw new Fw(r);return new Ne({invalid:r})}static fromDateTimes(t,n){const r=ns(t),s=ns(n),i=l1(r,s);return i??new Ne({start:r,end:s})}static after(t,n){const r=xe.fromDurationLike(n),s=ns(t);return Ne.fromDateTimes(s,s.plus(r))}static before(t,n){const r=xe.fromDurationLike(n),s=ns(t);return Ne.fromDateTimes(s.minus(r),s)}static fromISO(t,n){const[r,s]=(t||"").split("/",2);if(r&&s){let i,a;try{i=Q.fromISO(r,n),a=i.isValid}catch{a=!1}let o,l;try{o=Q.fromISO(s,n),l=o.isValid}catch{l=!1}if(a&&l)return Ne.fromDateTimes(i,o);if(a){const u=xe.fromISO(s,n);if(u.isValid)return Ne.after(i,u)}else if(l){const u=xe.fromISO(r,n);if(u.isValid)return Ne.before(o,u)}}return Ne.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static isInterval(t){return t&&t.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(t="milliseconds"){return this.isValid?this.toDuration(t).get(t):NaN}count(t="milliseconds"){if(!this.isValid)return NaN;const n=this.start.startOf(t),r=this.end.startOf(t);return Math.floor(r.diff(n,t).get(t))+(r.valueOf()!==this.end.valueOf())}hasSame(t){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,t):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(t){return this.isValid?this.s>t:!1}isBefore(t){return this.isValid?this.e<=t:!1}contains(t){return this.isValid?this.s<=t&&this.e>t:!1}set({start:t,end:n}={}){return this.isValid?Ne.fromDateTimes(t||this.s,n||this.e):this}splitAt(...t){if(!this.isValid)return[];const n=t.map(ns).filter(a=>this.contains(a)).sort(),r=[];let{s}=this,i=0;for(;s<this.e;){const a=n[i]||this.e,o=+a>+this.e?this.e:a;r.push(Ne.fromDateTimes(s,o)),s=o,i+=1}return r}splitBy(t){const n=xe.fromDurationLike(t);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,s=1,i;const a=[];for(;r<this.e;){const o=this.start.plus(n.mapUnits(l=>l*s));i=+o>+this.e?this.e:o,a.push(Ne.fromDateTimes(r,i)),r=i,s+=1}return a}divideEqually(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]}overlaps(t){return this.e>t.s&&this.s<t.e}abutsStart(t){return this.isValid?+this.e==+t.s:!1}abutsEnd(t){return this.isValid?+t.e==+this.s:!1}engulfs(t){return this.isValid?this.s<=t.s&&this.e>=t.e:!1}equals(t){return!this.isValid||!t.isValid?!1:this.s.equals(t.s)&&this.e.equals(t.e)}intersection(t){if(!this.isValid)return this;const n=this.s>t.s?this.s:t.s,r=this.e<t.e?this.e:t.e;return n>=r?null:Ne.fromDateTimes(n,r)}union(t){if(!this.isValid)return this;const n=this.s<t.s?this.s:t.s,r=this.e>t.e?this.e:t.e;return Ne.fromDateTimes(n,r)}static merge(t){const[n,r]=t.sort((s,i)=>s.s-i.s).reduce(([s,i],a)=>i?i.overlaps(a)||i.abutsStart(a)?[s,i.union(a)]:[s.concat([i]),a]:[s,a],[[],null]);return r&&n.push(r),n}static xor(t){let n=null,r=0;const s=[],i=t.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),a=Array.prototype.concat(...i),o=a.sort((l,u)=>l.time-u.time);for(const l of o)r+=l.type==="s"?1:-1,r===1?n=l.time:(n&&+n!=+l.time&&s.push(Ne.fromDateTimes(n,l.time)),n=null);return Ne.merge(s)}difference(...t){return Ne.xor([this].concat(t)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:wr}toLocaleString(t=Hi,n={}){return this.isValid?st.create(this.s.loc.clone(n),t).formatInterval(this):wr}toISO(t){return this.isValid?`${this.s.toISO(t)}/${this.e.toISO(t)}`:wr}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:wr}toISOTime(t){return this.isValid?`${this.s.toISOTime(t)}/${this.e.toISOTime(t)}`:wr}toFormat(t,{separator:n=" – "}={}){return this.isValid?`${this.s.toFormat(t)}${n}${this.e.toFormat(t)}`:wr}toDuration(t,n){return this.isValid?this.e.diff(this.s,t,n):xe.invalid(this.invalidReason)}mapEndpoints(t){return Ne.fromDateTimes(t(this.s),t(this.e))}}class ui{static hasDST(t=Ue.defaultZone){const n=Q.now().setZone(t).set({month:12});return!t.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(t){return hn.isValidZone(t)}static normalizeZone(t){return Vn(t,Ue.defaultZone)}static months(t="long",{locale:n=null,numberingSystem:r=null,locObj:s=null,outputCalendar:i="gregory"}={}){return(s||Me.create(n,r,i)).months(t)}static monthsFormat(t="long",{locale:n=null,numberingSystem:r=null,locObj:s=null,outputCalendar:i="gregory"}={}){return(s||Me.create(n,r,i)).months(t,!0)}static weekdays(t="long",{locale:n=null,numberingSystem:r=null,locObj:s=null}={}){return(s||Me.create(n,r,null)).weekdays(t)}static weekdaysFormat(t="long",{locale:n=null,numberingSystem:r=null,locObj:s=null}={}){return(s||Me.create(n,r,null)).weekdays(t,!0)}static meridiems({locale:t=null}={}){return Me.create(t).meridiems()}static eras(t="short",{locale:n=null}={}){return Me.create(n,null,"gregory").eras(t)}static features(){return{relative:Xm()}}}function Uc(e,t){const n=s=>s.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(t)-n(e);return Math.floor(xe.fromMillis(r).as("days"))}function u1(e,t,n){const r=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{const c=Uc(l,u);return(c-c%7)/7}],["days",Uc]],s={},i=e;let a,o;for(const[l,u]of r)n.indexOf(l)>=0&&(a=l,s[l]=u(e,t),o=i.plus(s),o>t?(s[l]--,e=i.plus(s)):e=o);return[e,s,o,a]}function c1(e,t,n,r){let[s,i,a,o]=u1(e,t,n);const l=t-s,u=n.filter(d=>["hours","minutes","seconds","milliseconds"].indexOf(d)>=0);u.length===0&&(a<t&&(a=s.plus({[o]:1})),a!==s&&(i[o]=(i[o]||0)+l/(a-s)));const c=xe.fromObject(i,r);return u.length>0?xe.fromMillis(l,r).shiftTo(...u).plus(c):c}const jl={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},zc={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},d1=jl.hanidec.replace(/[\[|\]]/g,"").split("");function f1(e){let t=parseInt(e,10);if(isNaN(t)){t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(e[n].search(jl.hanidec)!==-1)t+=d1.indexOf(e[n]);else for(const s in zc){const[i,a]=zc[s];r>=i&&r<=a&&(t+=r-i)}}return parseInt(t,10)}else return t}function Lt({numberingSystem:e},t=""){return new RegExp(`${jl[e||"latn"]}${t}`)}const m1="missing Intl.DateTimeFormat.formatToParts support";function Te(e,t=n=>n){return{regex:e,deser:([n])=>t(f1(n))}}const h1=String.fromCharCode(160),gh=`[ ${h1}]`,yh=new RegExp(gh,"g");function v1(e){return e.replace(/\./g,"\\.?").replace(yh,gh)}function jc(e){return e.replace(/\./g,"").replace(yh," ").toLowerCase()}function Rt(e,t){return e===null?null:{regex:RegExp(e.map(v1).join("|")),deser:([n])=>e.findIndex(r=>jc(n)===jc(r))+t}}function Wc(e,t){return{regex:e,deser:([,n,r])=>Ea(n,r),groups:t}}function ci(e){return{regex:e,deser:([t])=>t}}function g1(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function y1(e,t){const n=Lt(t),r=Lt(t,"{2}"),s=Lt(t,"{3}"),i=Lt(t,"{4}"),a=Lt(t,"{6}"),o=Lt(t,"{1,2}"),l=Lt(t,"{1,3}"),u=Lt(t,"{1,6}"),c=Lt(t,"{1,9}"),d=Lt(t,"{2,4}"),f=Lt(t,"{4,6}"),m=y=>({regex:RegExp(g1(y.val)),deser:([E])=>E,literal:!0}),g=(y=>{if(e.literal)return m(y);switch(y.val){case"G":return Rt(t.eras("short",!1),0);case"GG":return Rt(t.eras("long",!1),0);case"y":return Te(u);case"yy":return Te(d,Ho);case"yyyy":return Te(i);case"yyyyy":return Te(f);case"yyyyyy":return Te(a);case"M":return Te(o);case"MM":return Te(r);case"MMM":return Rt(t.months("short",!0,!1),1);case"MMMM":return Rt(t.months("long",!0,!1),1);case"L":return Te(o);case"LL":return Te(r);case"LLL":return Rt(t.months("short",!1,!1),1);case"LLLL":return Rt(t.months("long",!1,!1),1);case"d":return Te(o);case"dd":return Te(r);case"o":return Te(l);case"ooo":return Te(s);case"HH":return Te(r);case"H":return Te(o);case"hh":return Te(r);case"h":return Te(o);case"mm":return Te(r);case"m":return Te(o);case"q":return Te(o);case"qq":return Te(r);case"s":return Te(o);case"ss":return Te(r);case"S":return Te(l);case"SSS":return Te(s);case"u":return ci(c);case"uu":return ci(o);case"uuu":return Te(n);case"a":return Rt(t.meridiems(),0);case"kkkk":return Te(i);case"kk":return Te(d,Ho);case"W":return Te(o);case"WW":return Te(r);case"E":case"c":return Te(n);case"EEE":return Rt(t.weekdays("short",!1,!1),1);case"EEEE":return Rt(t.weekdays("long",!1,!1),1);case"ccc":return Rt(t.weekdays("short",!0,!1),1);case"cccc":return Rt(t.weekdays("long",!0,!1),1);case"Z":case"ZZ":return Wc(new RegExp(`([+-]${o.source})(?::(${r.source}))?`),2);case"ZZZ":return Wc(new RegExp(`([+-]${o.source})(${r.source})?`),2);case"z":return ci(/[a-z_+-/]{1,256}?/i);case" ":return ci(/[^\S\n\r]/);default:return m(y)}})(e)||{invalidReason:m1};return g.token=e,g}const p1={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour:{numeric:"h","2-digit":"hh"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function b1(e,t){const{type:n,value:r}=e;if(n==="literal"){const a=/^\s+$/.test(r);return{literal:!a,val:a?" ":r}}const s=t[n];let i=p1[n];if(typeof i=="object"&&(i=i[s]),i)return{literal:!1,val:i}}function w1(e){return[`^${e.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,e]}function S1(e,t,n){const r=e.match(t);if(r){const s={};let i=1;for(const a in n)if(Fr(n,a)){const o=n[a],l=o.groups?o.groups+1:1;!o.literal&&o.token&&(s[o.token.val[0]]=o.deser(r.slice(i,i+l))),i+=l}return[r,s]}else return[r,{}]}function C1(e){const t=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}};let n=null,r;return we(e.z)||(n=hn.create(e.z)),we(e.Z)||(n||(n=new ot(e.Z)),r=e.Z),we(e.q)||(e.M=(e.q-1)*3+1),we(e.h)||(e.h<12&&e.a===1?e.h+=12:e.h===12&&e.a===0&&(e.h=0)),e.G===0&&e.y&&(e.y=-e.y),we(e.u)||(e.S=Rl(e.u)),[Object.keys(e).reduce((i,a)=>{const o=t(a);return o&&(i[o]=e[a]),i},{}),n,r]}let eo=null;function x1(){return eo||(eo=Q.fromMillis(1555555555555)),eo}function _1(e,t){if(e.literal)return e;const n=st.macroTokenToFormatOpts(e.val),r=wh(n,t);return r==null||r.includes(void 0)?e:r}function ph(e,t){return Array.prototype.concat(...e.map(n=>_1(n,t)))}function bh(e,t,n){const r=ph(st.parseFormat(n),e),s=r.map(a=>y1(a,e)),i=s.find(a=>a.invalidReason);if(i)return{input:t,tokens:r,invalidReason:i.invalidReason};{const[a,o]=w1(s),l=RegExp(a,"i"),[u,c]=S1(t,l,o),[d,f,m]=c?C1(c):[null,null,void 0];if(Fr(c,"a")&&Fr(c,"H"))throw new is("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:r,regex:l,rawMatches:u,matches:c,result:d,zone:f,specificOffset:m}}}function E1(e,t,n){const{result:r,zone:s,specificOffset:i,invalidReason:a}=bh(e,t,n);return[r,s,i,a]}function wh(e,t){return e?st.create(t,e).formatDateTimeParts(x1()).map(s=>b1(s,e)):null}const Sh=[0,31,59,90,120,151,181,212,243,273,304,334],Ch=[0,31,60,91,121,152,182,213,244,274,305,335];function Tt(e,t){return new Ut("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function xh(e,t,n){const r=new Date(Date.UTC(e,t-1,n));e<100&&e>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const s=r.getUTCDay();return s===0?7:s}function _h(e,t,n){return n+(Zs(e)?Ch:Sh)[t-1]}function Eh(e,t){const n=Zs(e)?Ch:Sh,r=n.findIndex(i=>i<t),s=t-n[r];return{month:r+1,day:s}}function Uo(e){const{year:t,month:n,day:r}=e,s=_h(t,n,r),i=xh(t,n,r);let a=Math.floor((s-i+10)/7),o;return a<1?(o=t-1,a=zi(o)):a>zi(t)?(o=t+1,a=1):o=t,{weekYear:o,weekNumber:a,weekday:i,...Ta(e)}}function qc(e){const{weekYear:t,weekNumber:n,weekday:r}=e,s=xh(t,1,4),i=fs(t);let a=n*7+r-s-3,o;a<1?(o=t-1,a+=fs(o)):a>i?(o=t+1,a-=fs(t)):o=t;const{month:l,day:u}=Eh(o,a);return{year:o,month:l,day:u,...Ta(e)}}function to(e){const{year:t,month:n,day:r}=e,s=_h(t,n,r);return{year:t,ordinal:s,...Ta(e)}}function Zc(e){const{year:t,ordinal:n}=e,{month:r,day:s}=Eh(t,n);return{year:t,month:r,day:s,...Ta(e)}}function T1(e){const t=_a(e.weekYear),n=on(e.weekNumber,1,zi(e.weekYear)),r=on(e.weekday,1,7);return t?n?r?!1:Tt("weekday",e.weekday):Tt("week",e.week):Tt("weekYear",e.weekYear)}function k1(e){const t=_a(e.year),n=on(e.ordinal,1,fs(e.year));return t?n?!1:Tt("ordinal",e.ordinal):Tt("year",e.year)}function Th(e){const t=_a(e.year),n=on(e.month,1,12),r=on(e.day,1,Ui(e.year,e.month));return t?n?r?!1:Tt("day",e.day):Tt("month",e.month):Tt("year",e.year)}function kh(e){const{hour:t,minute:n,second:r,millisecond:s}=e,i=on(t,0,23)||t===24&&n===0&&r===0&&s===0,a=on(n,0,59),o=on(r,0,59),l=on(s,0,999);return i?a?o?l?!1:Tt("millisecond",s):Tt("second",r):Tt("minute",n):Tt("hour",t)}const no="Invalid DateTime",Yc=864e13;function di(e){return new Ut("unsupported zone",`the zone "${e.name}" is not supported`)}function ro(e){return e.weekData===null&&(e.weekData=Uo(e.c)),e.weekData}function es(e,t){const n={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new Q({...n,...t,old:n})}function Oh(e,t,n){let r=e-t*60*1e3;const s=n.offset(r);if(t===s)return[r,t];r-=(s-t)*60*1e3;const i=n.offset(r);return s===i?[r,s]:[e-Math.min(s,i)*60*1e3,Math.max(s,i)]}function Gc(e,t){e+=t*60*1e3;const n=new Date(e);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function Ei(e,t,n){return Oh(Bl(e),t,n)}function Kc(e,t){const n=e.o,r=e.c.year+Math.trunc(t.years),s=e.c.month+Math.trunc(t.months)+Math.trunc(t.quarters)*3,i={...e.c,year:r,month:s,day:Math.min(e.c.day,Ui(r,s))+Math.trunc(t.days)+Math.trunc(t.weeks)*7},a=xe.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),o=Bl(i);let[l,u]=Oh(o,n,e.zone);return a!==0&&(l+=a,u=e.zone.offset(l)),{ts:l,o:u}}function ts(e,t,n,r,s,i){const{setZone:a,zone:o}=n;if(e&&Object.keys(e).length!==0||t){const l=t||o,u=Q.fromObject(e,{...n,zone:l,specificOffset:i});return a?u:u.setZone(o)}else return Q.invalid(new Ut("unparsable",`the input "${s}" can't be parsed as ${r}`))}function fi(e,t,n=!0){return e.isValid?st.create(Me.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function so(e,t){const n=e.c.year>9999||e.c.year<0;let r="";return n&&e.c.year>=0&&(r+="+"),r+=qe(e.c.year,n?6:4),t?(r+="-",r+=qe(e.c.month),r+="-",r+=qe(e.c.day)):(r+=qe(e.c.month),r+=qe(e.c.day)),r}function Jc(e,t,n,r,s,i){let a=qe(e.c.hour);return t?(a+=":",a+=qe(e.c.minute),(e.c.second!==0||!n)&&(a+=":")):a+=qe(e.c.minute),(e.c.second!==0||!n)&&(a+=qe(e.c.second),(e.c.millisecond!==0||!r)&&(a+=".",a+=qe(e.c.millisecond,3))),s&&(e.isOffsetFixed&&e.offset===0&&!i?a+="Z":e.o<0?(a+="-",a+=qe(Math.trunc(-e.o/60)),a+=":",a+=qe(Math.trunc(-e.o%60))):(a+="+",a+=qe(Math.trunc(e.o/60)),a+=":",a+=qe(Math.trunc(e.o%60)))),i&&(a+="["+e.zone.ianaName+"]"),a}const Ih={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},O1={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},I1={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Ah=["year","month","day","hour","minute","second","millisecond"],A1=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],V1=["year","ordinal","hour","minute","second","millisecond"];function Xc(e){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new Pm(e);return t}function Qc(e,t){const n=Vn(t.zone,Ue.defaultZone),r=Me.fromObject(t),s=Ue.now();let i,a;if(we(e.year))i=s;else{for(const u of Ah)we(e[u])&&(e[u]=Ih[u]);const o=Th(e)||kh(e);if(o)return Q.invalid(o);const l=n.offset(s);[i,a]=Ei(e,l,n)}return new Q({ts:i,zone:n,loc:r,o:a})}function ed(e,t,n){const r=we(n.round)?!0:n.round,s=(a,o)=>(a=$l(a,r||n.calendary?0:2,!0),t.loc.clone(n).relFormatter(n).format(a,o)),i=a=>n.calendary?t.hasSame(e,a)?0:t.startOf(a).diff(e.startOf(a),a).get(a):t.diff(e,a).get(a);if(n.unit)return s(i(n.unit),n.unit);for(const a of n.units){const o=i(a);if(Math.abs(o)>=1)return s(o,a)}return s(e>t?-0:0,n.units[n.units.length-1])}function td(e){let t={},n;return e.length>0&&typeof e[e.length-1]=="object"?(t=e[e.length-1],n=Array.from(e).slice(0,e.length-1)):n=Array.from(e),[t,n]}class Q{constructor(t){const n=t.zone||Ue.defaultZone;let r=t.invalid||(Number.isNaN(t.ts)?new Ut("invalid input"):null)||(n.isValid?null:di(n));this.ts=we(t.ts)?Ue.now():t.ts;let s=null,i=null;if(!r)if(t.old&&t.old.ts===this.ts&&t.old.zone.equals(n))[s,i]=[t.old.c,t.old.o];else{const o=n.offset(this.ts);s=Gc(this.ts,o),r=Number.isNaN(s.year)?new Ut("invalid input"):null,s=r?null:s,i=r?null:o}this._zone=n,this.loc=t.loc||Me.create(),this.invalid=r,this.weekData=null,this.c=s,this.o=i,this.isLuxonDateTime=!0}static now(){return new Q({})}static local(){const[t,n]=td(arguments),[r,s,i,a,o,l,u]=n;return Qc({year:r,month:s,day:i,hour:a,minute:o,second:l,millisecond:u},t)}static utc(){const[t,n]=td(arguments),[r,s,i,a,o,l,u]=n;return t.zone=ot.utcInstance,Qc({year:r,month:s,day:i,hour:a,minute:o,second:l,millisecond:u},t)}static fromJSDate(t,n={}){const r=nS(t)?t.valueOf():NaN;if(Number.isNaN(r))return Q.invalid("invalid input");const s=Vn(n.zone,Ue.defaultZone);return s.isValid?new Q({ts:r,zone:s,loc:Me.fromObject(n)}):Q.invalid(di(s))}static fromMillis(t,n={}){if(or(t))return t<-Yc||t>Yc?Q.invalid("Timestamp out of range"):new Q({ts:t,zone:Vn(n.zone,Ue.defaultZone),loc:Me.fromObject(n)});throw new _t(`fromMillis requires a numerical input, but received a ${typeof t} with value ${t}`)}static fromSeconds(t,n={}){if(or(t))return new Q({ts:t*1e3,zone:Vn(n.zone,Ue.defaultZone),loc:Me.fromObject(n)});throw new _t("fromSeconds requires a numerical input")}static fromObject(t,n={}){t=t||{};const r=Vn(n.zone,Ue.defaultZone);if(!r.isValid)return Q.invalid(di(r));const s=Ue.now(),i=we(n.specificOffset)?r.offset(s):n.specificOffset,a=ji(t,Xc),o=!we(a.ordinal),l=!we(a.year),u=!we(a.month)||!we(a.day),c=l||u,d=a.weekYear||a.weekNumber,f=Me.fromObject(n);if((c||o)&&d)throw new is("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&o)throw new is("Can't mix ordinal dates with month/day");const m=d||a.weekday&&!c;let h,g,y=Gc(s,i);m?(h=A1,g=O1,y=Uo(y)):o?(h=V1,g=I1,y=to(y)):(h=Ah,g=Ih);let E=!1;for(const p of h){const T=a[p];we(T)?E?a[p]=g[p]:a[p]=y[p]:E=!0}const _=m?T1(a):o?k1(a):Th(a),w=_||kh(a);if(w)return Q.invalid(w);const O=m?qc(a):o?Zc(a):a,[I,k]=Ei(O,i,r),C=new Q({ts:I,zone:r,o:k,loc:f});return a.weekday&&c&&t.weekday!==C.weekday?Q.invalid("mismatched weekday",`you can't specify both a weekday of ${a.weekday} and a date of ${C.toISO()}`):C}static fromISO(t,n={}){const[r,s]=WS(t);return ts(r,s,n,"ISO 8601",t)}static fromRFC2822(t,n={}){const[r,s]=qS(t);return ts(r,s,n,"RFC 2822",t)}static fromHTTP(t,n={}){const[r,s]=ZS(t);return ts(r,s,n,"HTTP",n)}static fromFormat(t,n,r={}){if(we(t)||we(n))throw new _t("fromFormat requires an input string and a format");const{locale:s=null,numberingSystem:i=null}=r,a=Me.fromOpts({locale:s,numberingSystem:i,defaultToEN:!0}),[o,l,u,c]=E1(a,t,n);return c?Q.invalid(c):ts(o,l,r,`format ${n}`,t,u)}static fromString(t,n,r={}){return Q.fromFormat(t,n,r)}static fromSQL(t,n={}){const[r,s]=e1(t);return ts(r,s,n,"SQL",t)}static invalid(t,n=null){if(!t)throw new _t("need to specify a reason the DateTime is invalid");const r=t instanceof Ut?t:new Ut(t,n);if(Ue.throwOnInvalid)throw new Mw(r);return new Q({invalid:r})}static isDateTime(t){return t&&t.isLuxonDateTime||!1}static parseFormatForOpts(t,n={}){const r=wh(t,Me.fromObject(n));return r?r.map(s=>s?s.val:null).join(""):null}static expandFormat(t,n={}){return ph(st.parseFormat(t),Me.fromObject(n)).map(s=>s.val).join("")}get(t){return this[t]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?ro(this).weekYear:NaN}get weekNumber(){return this.isValid?ro(this).weekNumber:NaN}get weekday(){return this.isValid?ro(this).weekday:NaN}get ordinal(){return this.isValid?to(this.c).ordinal:NaN}get monthShort(){return this.isValid?ui.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?ui.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?ui.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?ui.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}get isInLeapYear(){return Zs(this.year)}get daysInMonth(){return Ui(this.year,this.month)}get daysInYear(){return this.isValid?fs(this.year):NaN}get weeksInWeekYear(){return this.isValid?zi(this.weekYear):NaN}resolvedLocaleOptions(t={}){const{locale:n,numberingSystem:r,calendar:s}=st.create(this.loc.clone(t),t).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:s}}toUTC(t=0,n={}){return this.setZone(ot.instance(t),n)}toLocal(){return this.setZone(Ue.defaultZone)}setZone(t,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(t=Vn(t,Ue.defaultZone),t.equals(this.zone))return this;if(t.isValid){let s=this.ts;if(n||r){const i=t.offset(this.ts),a=this.toObject();[s]=Ei(a,i,t)}return es(this,{ts:s,zone:t})}else return Q.invalid(di(t))}reconfigure({locale:t,numberingSystem:n,outputCalendar:r}={}){const s=this.loc.clone({locale:t,numberingSystem:n,outputCalendar:r});return es(this,{loc:s})}setLocale(t){return this.reconfigure({locale:t})}set(t){if(!this.isValid)return this;const n=ji(t,Xc),r=!we(n.weekYear)||!we(n.weekNumber)||!we(n.weekday),s=!we(n.ordinal),i=!we(n.year),a=!we(n.month)||!we(n.day),o=i||a,l=n.weekYear||n.weekNumber;if((o||s)&&l)throw new is("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(a&&s)throw new is("Can't mix ordinal dates with month/day");let u;r?u=qc({...Uo(this.c),...n}):we(n.ordinal)?(u={...this.toObject(),...n},we(n.day)&&(u.day=Math.min(Ui(u.year,u.month),u.day))):u=Zc({...to(this.c),...n});const[c,d]=Ei(u,this.o,this.zone);return es(this,{ts:c,o:d})}plus(t){if(!this.isValid)return this;const n=xe.fromDurationLike(t);return es(this,Kc(this,n))}minus(t){if(!this.isValid)return this;const n=xe.fromDurationLike(t).negate();return es(this,Kc(this,n))}startOf(t){if(!this.isValid)return this;const n={},r=xe.normalizeUnit(t);switch(r){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0;break}if(r==="weeks"&&(n.weekday=1),r==="quarters"){const s=Math.ceil(this.month/3);n.month=(s-1)*3+1}return this.set(n)}endOf(t){return this.isValid?this.plus({[t]:1}).startOf(t).minus(1):this}toFormat(t,n={}){return this.isValid?st.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,t):no}toLocaleString(t=Hi,n={}){return this.isValid?st.create(this.loc.clone(n),t).formatDateTime(this):no}toLocaleParts(t={}){return this.isValid?st.create(this.loc.clone(t),t).formatDateTimeParts(this):[]}toISO({format:t="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:s=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;const a=t==="extended";let o=so(this,a);return o+="T",o+=Jc(this,a,n,r,s,i),o}toISODate({format:t="extended"}={}){return this.isValid?so(this,t==="extended"):null}toISOWeekDate(){return fi(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:t=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:s=!1,extendedZone:i=!1,format:a="extended"}={}){return this.isValid?(s?"T":"")+Jc(this,a==="extended",n,t,r,i):null}toRFC2822(){return fi(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return fi(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?so(this,!0):null}toSQLTime({includeOffset:t=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let s="HH:mm:ss.SSS";return(n||t)&&(r&&(s+=" "),n?s+="z":t&&(s+="ZZ")),fi(this,s,!0)}toSQL(t={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(t)}`:null}toString(){return this.isValid?this.toISO():no}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(t={}){if(!this.isValid)return{};const n={...this.c};return t.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(t,n="milliseconds",r={}){if(!this.isValid||!t.isValid)return xe.invalid("created by diffing an invalid DateTime");const s={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=rS(n).map(xe.normalizeUnit),a=t.valueOf()>this.valueOf(),o=a?this:t,l=a?t:this,u=c1(o,l,i,s);return a?u.negate():u}diffNow(t="milliseconds",n={}){return this.diff(Q.now(),t,n)}until(t){return this.isValid?Ne.fromDateTimes(this,t):this}hasSame(t,n){if(!this.isValid)return!1;const r=t.valueOf(),s=this.setZone(t.zone,{keepLocalTime:!0});return s.startOf(n)<=r&&r<=s.endOf(n)}equals(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)}toRelative(t={}){if(!this.isValid)return null;const n=t.base||Q.fromObject({},{zone:this.zone}),r=t.padding?this<n?-t.padding:t.padding:0;let s=["years","months","days","hours","minutes","seconds"],i=t.unit;return Array.isArray(t.unit)&&(s=t.unit,i=void 0),ed(n,this.plus(r),{...t,numeric:"always",units:s,unit:i})}toRelativeCalendar(t={}){return this.isValid?ed(t.base||Q.fromObject({},{zone:this.zone}),this,{...t,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...t){if(!t.every(Q.isDateTime))throw new _t("min requires all arguments be DateTimes");return $c(t,n=>n.valueOf(),Math.min)}static max(...t){if(!t.every(Q.isDateTime))throw new _t("max requires all arguments be DateTimes");return $c(t,n=>n.valueOf(),Math.max)}static fromFormatExplain(t,n,r={}){const{locale:s=null,numberingSystem:i=null}=r,a=Me.fromOpts({locale:s,numberingSystem:i,defaultToEN:!0});return bh(a,t,n)}static fromStringExplain(t,n,r={}){return Q.fromFormatExplain(t,n,r)}static get DATE_SHORT(){return Hi}static get DATE_MED(){return Mm}static get DATE_MED_WITH_WEEKDAY(){return Nw}static get DATE_FULL(){return Fm}static get DATE_HUGE(){return Dm}static get TIME_SIMPLE(){return Nm}static get TIME_WITH_SECONDS(){return Lm}static get TIME_WITH_SHORT_OFFSET(){return Rm}static get TIME_WITH_LONG_OFFSET(){return $m}static get TIME_24_SIMPLE(){return Bm}static get TIME_24_WITH_SECONDS(){return Hm}static get TIME_24_WITH_SHORT_OFFSET(){return Um}static get TIME_24_WITH_LONG_OFFSET(){return zm}static get DATETIME_SHORT(){return jm}static get DATETIME_SHORT_WITH_SECONDS(){return Wm}static get DATETIME_MED(){return qm}static get DATETIME_MED_WITH_SECONDS(){return Zm}static get DATETIME_MED_WITH_WEEKDAY(){return Lw}static get DATETIME_FULL(){return Ym}static get DATETIME_FULL_WITH_SECONDS(){return Gm}static get DATETIME_HUGE(){return Km}static get DATETIME_HUGE_WITH_SECONDS(){return Jm}}function ns(e){if(Q.isDateTime(e))return e;if(e&&e.valueOf&&or(e.valueOf()))return Q.fromJSDate(e);if(e&&typeof e=="object")return Q.fromObject(e);throw new _t(`Unknown datetime argument: ${e}, of type ${typeof e}`)}const P1=e=>{e=M1(e);for(var t=e.slice(-4,-1),n=e.substr(e.length-1),r=(t.length==0?"":`${t}-`)+n,s=4;s<e.length;s+=3)r=e.slice(-3-s,-s)+"."+r;return r},M1=e=>typeof e=="string"?e.replace(/^0+|[^0-9kK]+/g,"").toUpperCase():"";const Vh=se()({name:"VCardActions",props:ye(),setup(e,t){let{slots:n}=t;return Bn({VBtn:{variant:"text"}}),ve(()=>{var r;return v("div",{class:["v-card-actions",e.class],style:e.style},[(r=n.default)==null?void 0:r.call(n)])}),{}}}),F1=lr("v-card-subtitle"),Ph=lr("v-card-title");function D1(e){return{aspectStyles:S(()=>{const t=Number(e.aspectRatio);return t?{paddingBottom:String(1/t*100)+"%"}:void 0})}}const Mh=z({aspectRatio:[String,Number],contentClass:String,inline:Boolean,...ye(),...zn()},"VResponsive"),Wi=se()({name:"VResponsive",props:Mh(),setup(e,t){let{slots:n}=t;const{aspectStyles:r}=D1(e),{dimensionStyles:s}=jn(e);return ve(()=>{var i;return v("div",{class:["v-responsive",{"v-responsive--inline":e.inline},e.class],style:[s.value,e.style]},[v("div",{class:"v-responsive__sizer",style:r.value},null),(i=n.additional)==null?void 0:i.call(n),n.default&&v("div",{class:["v-responsive__content",e.contentClass]},[n.default()])])}),{}}});function N1(e,t){if(!kl)return;const n=t.modifiers||{},r=t.value,{handler:s,options:i}=typeof r=="object"?r:{handler:r,options:{}},a=new IntersectionObserver(function(){var d;let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],l=arguments.length>1?arguments[1]:void 0;const u=(d=e._observe)==null?void 0:d[t.instance.$.uid];if(!u)return;const c=o.some(f=>f.isIntersecting);s&&(!n.quiet||u.init)&&(!n.once||c||u.init)&&s(c,o,l),c&&n.once?Fh(e,t):u.init=!0},i);e._observe=Object(e._observe),e._observe[t.instance.$.uid]={init:!1,observer:a},a.observe(e)}function Fh(e,t){var r;const n=(r=e._observe)==null?void 0:r[t.instance.$.uid];n&&(n.observer.unobserve(e),delete e._observe[t.instance.$.uid])}const L1={mounted:N1,unmounted:Fh},Dh=L1,R1=z({alt:String,cover:Boolean,eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},srcset:String,...Mh(),...ye(),...Us()},"VImg"),Wl=se()({name:"VImg",directives:{intersect:Dh},props:R1(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,t){let{emit:n,slots:r}=t;const s=be(""),i=K(),a=be(e.eager?"loading":"idle"),o=be(),l=be(),u=S(()=>e.src&&typeof e.src=="object"?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),c=S(()=>u.value.aspect||o.value/l.value||0);he(()=>e.src,()=>{d(a.value!=="idle")}),he(c,(p,T)=>{!p&&T&&i.value&&y(i.value)}),dl(()=>d());function d(p){if(!(e.eager&&p)&&!(kl&&!p&&!e.eager)){if(a.value="loading",u.value.lazySrc){const T=new Image;T.src=u.value.lazySrc,y(T,null)}u.value.src&&ut(()=>{var T,V;if(n("loadstart",((T=i.value)==null?void 0:T.currentSrc)||u.value.src),(V=i.value)!=null&&V.complete){if(i.value.naturalWidth||m(),a.value==="error")return;c.value||y(i.value,null),f()}else c.value||y(i.value),h()})}}function f(){var p;h(),a.value="loaded",n("load",((p=i.value)==null?void 0:p.currentSrc)||u.value.src)}function m(){var p;a.value="error",n("error",((p=i.value)==null?void 0:p.currentSrc)||u.value.src)}function h(){const p=i.value;p&&(s.value=p.currentSrc||p.src)}let g=-1;function y(p){let T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;const V=()=>{clearTimeout(g);const{naturalHeight:$,naturalWidth:P}=p;$||P?(o.value=P,l.value=$):!p.complete&&a.value==="loading"&&T!=null?g=window.setTimeout(V,T):(p.currentSrc.endsWith(".svg")||p.currentSrc.startsWith("data:image/svg+xml"))&&(o.value=1,l.value=1)};V()}const E=S(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),_=()=>{var V;if(!u.value.src||a.value==="idle")return null;const p=v("img",{class:["v-img__img",E.value],src:u.value.src,srcset:u.value.srcset,alt:e.alt,sizes:e.sizes,ref:i,onLoad:f,onError:m},null),T=(V=r.sources)==null?void 0:V.call(r);return v(Pn,{transition:e.transition,appear:!0},{default:()=>[ft(T?v("picture",{class:"v-img__picture"},[T,p]):p,[[$r,a.value==="loaded"]])]})},w=()=>v(Pn,{transition:e.transition},{default:()=>[u.value.lazySrc&&a.value!=="loaded"&&v("img",{class:["v-img__img","v-img__img--preload",E.value],src:u.value.lazySrc,alt:e.alt},null)]}),O=()=>r.placeholder?v(Pn,{transition:e.transition,appear:!0},{default:()=>[(a.value==="loading"||a.value==="error"&&!r.error)&&v("div",{class:"v-img__placeholder"},[r.placeholder()])]}):null,I=()=>r.error?v(Pn,{transition:e.transition,appear:!0},{default:()=>[a.value==="error"&&v("div",{class:"v-img__error"},[r.error()])]}):null,k=()=>e.gradient?v("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,C=be(!1);{const p=he(c,T=>{T&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{C.value=!0})}),p())})}return ve(()=>{const[p]=Wi.filterProps(e);return ft(v(Wi,ue({class:["v-img",{"v-img--booting":!C.value},e.class],style:[{width:ae(e.width==="auto"?o.value:e.width)},e.style]},p,{aspectRatio:c.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>v(ke,null,[v(_,null,null),v(w,null,null),v(k,null,null),v(O,null,null),v(I,null,null)]),default:r.default}),[[dn("intersect"),{handler:d,options:e.options},null,{once:!0}]])}),{currentSrc:s,image:i,state:a,naturalWidth:o,naturalHeight:l}}}),$1=z({start:Boolean,end:Boolean,icon:Ae,image:String,...ye(),...Vt(),...Pt(),...Ns(),...Xe(),...je(),...tn({variant:"flat"})},"VAvatar"),Dr=se()({name:"VAvatar",props:$1(),setup(e,t){let{slots:n}=t;const{themeClasses:r}=Qe(e),{colorClasses:s,colorStyles:i,variantClasses:a}=fr(e),{densityClasses:o}=qt(e),{roundedClasses:l}=Mt(e),{sizeClasses:u,sizeStyles:c}=Ls(e);return ve(()=>v(e.tag,{class:["v-avatar",{"v-avatar--start":e.start,"v-avatar--end":e.end},r.value,s.value,o.value,l.value,u.value,a.value,e.class],style:[i.value,c.value,e.style]},{default:()=>{var d;return[e.image?v(Wl,{key:"image",src:e.image,alt:"",cover:!0},null):e.icon?v(et,{key:"icon",icon:e.icon},null):(d=n.default)==null?void 0:d.call(n),dr(!1,"v-avatar")]}})),{}}}),B1=z({appendAvatar:String,appendIcon:Ae,prependAvatar:String,prependIcon:Ae,subtitle:String,title:String,...ye(),...Vt()},"VCardItem"),H1=se()({name:"VCardItem",props:B1(),setup(e,t){let{slots:n}=t;return ve(()=>{var u;const r=!!(e.prependAvatar||e.prependIcon),s=!!(r||n.prepend),i=!!(e.appendAvatar||e.appendIcon),a=!!(i||n.append),o=!!(e.title||n.title),l=!!(e.subtitle||n.subtitle);return v("div",{class:["v-card-item",e.class],style:e.style},[s&&v("div",{key:"prepend",class:"v-card-item__prepend"},[n.prepend?v(Le,{key:"prepend-defaults",disabled:!r,defaults:{VAvatar:{density:e.density,icon:e.prependIcon,image:e.prependAvatar}}},n.prepend):r&&v(Dr,{key:"prepend-avatar",density:e.density,icon:e.prependIcon,image:e.prependAvatar},null)]),v("div",{class:"v-card-item__content"},[o&&v(Ph,{key:"title"},{default:()=>{var c;return[((c=n.title)==null?void 0:c.call(n))??e.title]}}),l&&v(F1,{key:"subtitle"},{default:()=>{var c;return[((c=n.subtitle)==null?void 0:c.call(n))??e.subtitle]}}),(u=n.default)==null?void 0:u.call(n)]),a&&v("div",{key:"append",class:"v-card-item__append"},[n.append?v(Le,{key:"append-defaults",disabled:!i,defaults:{VAvatar:{density:e.density,icon:e.appendIcon,image:e.appendAvatar}}},n.append):i&&v(Dr,{key:"append-avatar",density:e.density,icon:e.appendIcon,image:e.appendAvatar},null)])])}),{}}}),Nh=lr("v-card-text"),U1=z({appendAvatar:String,appendIcon:Ae,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:Ae,ripple:{type:[Boolean,Object],default:!0},subtitle:String,text:String,title:String,...ur(),...ye(),...Vt(),...zn(),...Hn(),...Il(),...Rs(),...ca(),...Pt(),...ma(),...Xe(),...je(),...tn({variant:"elevated"})},"VCard"),ka=se()({name:"VCard",directives:{Ripple:Bs},props:U1(),setup(e,t){let{attrs:n,slots:r}=t;const{themeClasses:s}=Qe(e),{borderClasses:i}=cr(e),{colorClasses:a,colorStyles:o,variantClasses:l}=fr(e),{densityClasses:u}=qt(e),{dimensionStyles:c}=jn(e),{elevationClasses:d}=Un(e),{loaderClasses:f}=Al(e),{locationStyles:m}=$s(e),{positionClasses:h}=da(e),{roundedClasses:g}=Mt(e),y=fa(e,n),E=S(()=>e.link!==!1&&y.isLink.value),_=S(()=>!e.disabled&&e.link!==!1&&(e.link||y.isClickable.value));return ve(()=>{const w=E.value?"a":e.tag,O=!!(r.title||e.title),I=!!(r.subtitle||e.subtitle),k=O||I,C=!!(r.append||e.appendAvatar||e.appendIcon),p=!!(r.prepend||e.prependAvatar||e.prependIcon),T=!!(r.image||e.image),V=k||p||C,$=!!(r.text||e.text);return ft(v(w,{class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":_.value},s.value,i.value,a.value,u.value,d.value,f.value,h.value,g.value,l.value,e.class],style:[o.value,c.value,m.value,e.style],href:y.href.value,onClick:_.value&&y.navigate,tabindex:e.disabled?-1:void 0},{default:()=>{var P;return[T&&v("div",{key:"image",class:"v-card__image"},[r.image?v(Le,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},r.image):v(Wl,{key:"image-img",cover:!0,src:e.image},null)]),v(Qf,{name:"v-card",active:!!e.loading,color:typeof e.loading=="boolean"?void 0:e.loading},{default:r.loader}),V&&v(H1,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:r.item,prepend:r.prepend,title:r.title,subtitle:r.subtitle,append:r.append}),$&&v(Nh,{key:"text"},{default:()=>{var R;return[((R=r.text)==null?void 0:R.call(r))??e.text]}}),(P=r.default)==null?void 0:P.call(r),r.actions&&v(Vh,null,{default:r.actions}),dr(_.value,"v-card")]}}),[[dn("ripple"),_.value&&e.ripple]])}),{}}});const z1=z({disabled:Boolean,group:Boolean,hideOnLeave:Boolean,leaveAbsolute:Boolean,mode:String,origin:String},"transition");function St(e,t,n){return se()({name:e,props:z1({mode:n,origin:t}),setup(r,s){let{slots:i}=s;const a={onBeforeEnter(o){r.origin&&(o.style.transformOrigin=r.origin)},onLeave(o){if(r.leaveAbsolute){const{offsetTop:l,offsetLeft:u,offsetWidth:c,offsetHeight:d}=o;o._transitionInitialStyles={position:o.style.position,top:o.style.top,left:o.style.left,width:o.style.width,height:o.style.height},o.style.position="absolute",o.style.top=`${l}px`,o.style.left=`${u}px`,o.style.width=`${c}px`,o.style.height=`${d}px`}r.hideOnLeave&&o.style.setProperty("display","none","important")},onAfterLeave(o){if(r.leaveAbsolute&&(o!=null&&o._transitionInitialStyles)){const{position:l,top:u,left:c,width:d,height:f}=o._transitionInitialStyles;delete o._transitionInitialStyles,o.style.position=l||"",o.style.top=u||"",o.style.left=c||"",o.style.width=d||"",o.style.height=f||""}}};return()=>{const o=r.group?Xy:mn;return $n(o,{name:r.disabled?"":e,css:!r.disabled,...r.group?void 0:{mode:r.mode},...r.disabled?{}:a},i.default)}}})}function Lh(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"in-out";return se()({name:e,props:{mode:{type:String,default:n},disabled:Boolean},setup(r,s){let{slots:i}=s;return()=>$n(mn,{name:r.disabled?"":e,css:!r.disabled,...r.disabled?{}:t},i.default)}})}function Rh(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";const n=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1)?"width":"height",r=It(`offset-${n}`);return{onBeforeEnter(a){a._parent=a.parentNode,a._initialStyle={transition:a.style.transition,overflow:a.style.overflow,[n]:a.style[n]}},onEnter(a){const o=a._initialStyle;a.style.setProperty("transition","none","important"),a.style.overflow="hidden";const l=`${a[r]}px`;a.style[n]="0",a.offsetHeight,a.style.transition=o.transition,e&&a._parent&&a._parent.classList.add(e),requestAnimationFrame(()=>{a.style[n]=l})},onAfterEnter:i,onEnterCancelled:i,onLeave(a){a._initialStyle={transition:"",overflow:a.style.overflow,[n]:a.style[n]},a.style.overflow="hidden",a.style[n]=`${a[r]}px`,a.offsetHeight,requestAnimationFrame(()=>a.style[n]="0")},onAfterLeave:s,onLeaveCancelled:s};function s(a){e&&a._parent&&a._parent.classList.remove(e),i(a)}function i(a){const o=a._initialStyle[n];a.style.overflow=a._initialStyle.overflow,o!=null&&(a.style[n]=o),delete a._initialStyle}}const j1=z({target:Object},"v-dialog-transition"),ql=se()({name:"VDialogTransition",props:j1(),setup(e,t){let{slots:n}=t;const r={onBeforeEnter(s){s.style.pointerEvents="none",s.style.visibility="hidden"},async onEnter(s,i){var f;await new Promise(m=>requestAnimationFrame(m)),await new Promise(m=>requestAnimationFrame(m)),s.style.visibility="";const{x:a,y:o,sx:l,sy:u,speed:c}=rd(e.target,s),d=Cr(s,[{transform:`translate(${a}px, ${o}px) scale(${l}, ${u})`,opacity:0},{}],{duration:225*c,easing:$p});(f=nd(s))==null||f.forEach(m=>{Cr(m,[{opacity:0},{opacity:0,offset:.33},{}],{duration:225*2*c,easing:Fi})}),d.finished.then(()=>i())},onAfterEnter(s){s.style.removeProperty("pointer-events")},onBeforeLeave(s){s.style.pointerEvents="none"},async onLeave(s,i){var f;await new Promise(m=>requestAnimationFrame(m));const{x:a,y:o,sx:l,sy:u,speed:c}=rd(e.target,s);Cr(s,[{},{transform:`translate(${a}px, ${o}px) scale(${l}, ${u})`,opacity:0}],{duration:125*c,easing:Bp}).finished.then(()=>i()),(f=nd(s))==null||f.forEach(m=>{Cr(m,[{},{opacity:0,offset:.2},{opacity:0}],{duration:125*2*c,easing:Fi})})},onAfterLeave(s){s.style.removeProperty("pointer-events")}};return()=>e.target?v(mn,ue({name:"dialog-transition"},r,{css:!1}),n):v(mn,{name:"dialog-transition"},n)}});function nd(e){var n;const t=(n=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list"))==null?void 0:n.children;return t&&[...t]}function rd(e,t){const n=e.getBoundingClientRect(),r=xl(t),[s,i]=getComputedStyle(t).transformOrigin.split(" ").map(E=>parseFloat(E)),[a,o]=getComputedStyle(t).getPropertyValue("--v-overlay-anchor-origin").split(" ");let l=n.left+n.width/2;a==="left"||o==="left"?l-=n.width/2:(a==="right"||o==="right")&&(l+=n.width/2);let u=n.top+n.height/2;a==="top"||o==="top"?u-=n.height/2:(a==="bottom"||o==="bottom")&&(u+=n.height/2);const c=n.width/r.width,d=n.height/r.height,f=Math.max(1,c,d),m=c/f||0,h=d/f||0,g=r.width*r.height/(window.innerWidth*window.innerHeight),y=g>.12?Math.min(1.5,(g-.12)*10+1):1;return{x:l-(s+r.left),y:u-(i+r.top),sx:m,sy:h,speed:y}}St("fab-transition","center center","out-in");St("dialog-bottom-transition");St("dialog-top-transition");St("fade-transition");St("scale-transition");St("scroll-x-transition");St("scroll-x-reverse-transition");St("scroll-y-transition");St("scroll-y-reverse-transition");St("slide-x-transition");St("slide-x-reverse-transition");const $h=St("slide-y-transition");St("slide-y-reverse-transition");const Bh=Lh("expand-transition",Rh()),Hh=Lh("expand-x-transition",Rh("",!0)),W1=z({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...zs({origin:"center center",scrollStrategy:"block",transition:{component:ql},zIndex:2400})},"VDialog"),q1=se()({name:"VDialog",props:W1(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"modelValue"),{scopeId:s}=Hs(),i=K();function a(l){var d,f;const u=l.relatedTarget,c=l.target;if(u!==c&&((d=i.value)!=null&&d.contentEl)&&((f=i.value)!=null&&f.globalTop)&&![document,i.value.contentEl].includes(c)&&!i.value.contentEl.contains(c)){const m=Vf(i.value.contentEl);if(!m.length)return;const h=m[0],g=m[m.length-1];u===h?g.focus():h.focus()}}Ge&&he(()=>r.value&&e.retainFocus,l=>{l?document.addEventListener("focusin",a):document.removeEventListener("focusin",a)},{immediate:!0}),he(r,async l=>{var u,c;await ut(),l?(u=i.value.contentEl)==null||u.focus({preventScroll:!0}):(c=i.value.activatorEl)==null||c.focus({preventScroll:!0})});const o=S(()=>ue({"aria-haspopup":"dialog","aria-expanded":String(r.value)},e.activatorProps));return ve(()=>{const[l]=Rn.filterProps(e);return v(Rn,ue({ref:i,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},l,{modelValue:r.value,"onUpdate:modelValue":u=>r.value=u,"aria-modal":"true",activatorProps:o.value,role:"dialog"},s),{activator:n.activator,default:function(){for(var u=arguments.length,c=new Array(u),d=0;d<u;d++)c[d]=arguments[d];return v(Le,{root:"VDialog"},{default:()=>{var f;return[(f=n.default)==null?void 0:f.call(n,...c)]}})}})}),Ur({},i)}});const Uh=(()=>ha.reduce((e,t)=>(e[t]={type:[Boolean,String,Number],default:!1},e),{}))(),zh=(()=>ha.reduce((e,t)=>{const n="offset"+vn(t);return e[n]={type:[String,Number],default:null},e},{}))(),jh=(()=>ha.reduce((e,t)=>{const n="order"+vn(t);return e[n]={type:[String,Number],default:null},e},{}))(),sd={col:Object.keys(Uh),offset:Object.keys(zh),order:Object.keys(jh)};function Z1(e,t,n){let r=e;if(!(n==null||n===!1)){if(t){const s=t.replace(e,"");r+=`-${s}`}return e==="col"&&(r="v-"+r),e==="col"&&(n===""||n===!0)||(r+=`-${n}`),r.toLowerCase()}}const Y1=["auto","start","end","center","baseline","stretch"],G1=z({cols:{type:[Boolean,String,Number],default:!1},...Uh,offset:{type:[String,Number],default:null},...zh,order:{type:[String,Number],default:null},...jh,alignSelf:{type:String,default:null,validator:e=>Y1.includes(e)},...ye(),...Xe()},"VCol"),$t=se()({name:"VCol",props:G1(),setup(e,t){let{slots:n}=t;const r=S(()=>{const s=[];let i;for(i in sd)sd[i].forEach(o=>{const l=e[o],u=Z1(i,o,l);u&&s.push(u)});const a=s.some(o=>o.startsWith("v-col-"));return s.push({"v-col":!a||!e.cols,[`v-col-${e.cols}`]:e.cols,[`offset-${e.offset}`]:e.offset,[`order-${e.order}`]:e.order,[`align-self-${e.alignSelf}`]:e.alignSelf}),s});return()=>{var s;return $n(e.tag,{class:[r.value,e.class],style:e.style},(s=n.default)==null?void 0:s.call(n))}}}),Zl=["start","end","center"],Wh=["space-between","space-around","space-evenly"];function Yl(e,t){return ha.reduce((n,r)=>{const s=e+vn(r);return n[s]=t(),n},{})}const K1=[...Zl,"baseline","stretch"],qh=e=>K1.includes(e),Zh=Yl("align",()=>({type:String,default:null,validator:qh})),J1=[...Zl,...Wh],Yh=e=>J1.includes(e),Gh=Yl("justify",()=>({type:String,default:null,validator:Yh})),X1=[...Zl,...Wh,"stretch"],Kh=e=>X1.includes(e),Jh=Yl("alignContent",()=>({type:String,default:null,validator:Kh})),id={align:Object.keys(Zh),justify:Object.keys(Gh),alignContent:Object.keys(Jh)},Q1={align:"align",justify:"justify",alignContent:"align-content"};function eC(e,t,n){let r=Q1[e];if(n!=null){if(t){const s=t.replace(e,"");r+=`-${s}`}return r+=`-${n}`,r.toLowerCase()}}const tC=z({dense:Boolean,noGutters:Boolean,align:{type:String,default:null,validator:qh},...Zh,justify:{type:String,default:null,validator:Yh},...Gh,alignContent:{type:String,default:null,validator:Kh},...Jh,...ye(),...Xe()},"VRow"),xr=se()({name:"VRow",props:tC(),setup(e,t){let{slots:n}=t;const r=S(()=>{const s=[];let i;for(i in id)id[i].forEach(a=>{const o=e[a],l=eC(i,a,o);l&&s.push(l)});return s.push({"v-row--no-gutters":e.noGutters,"v-row--dense":e.dense,[`align-${e.align}`]:e.align,[`justify-${e.justify}`]:e.justify,[`align-content-${e.alignContent}`]:e.alignContent}),s});return()=>{var s;return $n(e.tag,{class:["v-row",r.value,e.class],style:e.style},(s=n.default)==null?void 0:s.call(n))}}}),Xh=lr("flex-grow-1","div","VSpacer"),Qh={__name:"confirm",setup(e,{expose:t}){const n=K(),r=K(),s=K(!1),i=K(null),a=K(null),o=K(null),l=d=>{i.value=d.title,a.value=d.message,o.value=d.okButton,n.value=void 0,r.value=void 0;const f=new Promise((m,h)=>{n.value=m,r.value=h});return s.value=!0,f},u=()=>{s.value=!1,n.value&&n.value(!0)},c=()=>{s.value=!1,n.value&&n.value(!1)};return t({show:l,_confirm:u,_cancel:c}),(d,f)=>(Ye(),gt(xr,{justify:"center"},{default:re(()=>[v(q1,{modelValue:s.value,"onUpdate:modelValue":f[0]||(f[0]=m=>s.value=m),persistent:"",width:"auto",ref:"popup"},{default:re(()=>[v(ka,null,{default:re(()=>[v(Ph,{class:"text-h5"},{default:re(()=>[Ze(Se(i.value),1)]),_:1}),v(Nh,null,{default:re(()=>[Ze(Se(a.value),1)]),_:1}),v(Vh,null,{default:re(()=>[v(Xh),v(pt,{color:"error",variant:"text",onClick:c},{default:re(()=>[Ze(Se(d.cancelButton||"Cancelar"),1)]),_:1}),v(pt,{color:"primary",onClick:u},{default:re(()=>[Ze(Se(o.value||"Confirmar"),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}};const nC=lr("v-alert-title"),rC=["success","info","warning","error"],sC=z({border:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:Ae,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>rC.includes(e)},...ye(),...Vt(),...zn(),...Hn(),...Rs(),...ca(),...Pt(),...Xe(),...je(),...tn({variant:"flat"})},"VAlert"),iC=se()({name:"VAlert",props:sC(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{emit:n,slots:r}=t;const s=Be(e,"modelValue"),i=S(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),a=S(()=>({color:e.color??e.type,variant:e.variant})),{themeClasses:o}=Qe(e),{colorClasses:l,colorStyles:u,variantClasses:c}=fr(a),{densityClasses:d}=qt(e),{dimensionStyles:f}=jn(e),{elevationClasses:m}=Un(e),{locationStyles:h}=$s(e),{positionClasses:g}=da(e),{roundedClasses:y}=Mt(e),{textColorClasses:E,textColorStyles:_}=en(de(e,"borderColor")),{t:w}=ua(),O=S(()=>({"aria-label":w(e.closeLabel),onClick(I){s.value=!1,n("click:close",I)}}));return()=>{const I=!!(r.prepend||i.value),k=!!(r.title||e.title),C=!!(r.close||e.closable);return s.value&&v(e.tag,{class:["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},o.value,l.value,d.value,m.value,g.value,y.value,c.value,e.class],style:[u.value,f.value,h.value,e.style],role:"alert"},{default:()=>{var p,T;return[dr(!1,"v-alert"),e.border&&v("div",{key:"border",class:["v-alert__border",E.value],style:_.value},null),I&&v("div",{key:"prepend",class:"v-alert__prepend"},[r.prepend?v(Le,{key:"prepend-defaults",disabled:!i.value,defaults:{VIcon:{density:e.density,icon:i.value,size:e.prominent?44:28}}},r.prepend):v(et,{key:"prepend-icon",density:e.density,icon:i.value,size:e.prominent?44:28},null)]),v("div",{class:"v-alert__content"},[k&&v(nC,{key:"title"},{default:()=>{var V;return[((V=r.title)==null?void 0:V.call(r))??e.title]}}),((p=r.text)==null?void 0:p.call(r))??e.text,(T=r.default)==null?void 0:T.call(r)]),r.append&&v("div",{key:"append",class:"v-alert__append"},[r.append()]),C&&v("div",{key:"close",class:"v-alert__close"},[r.close?v(Le,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>{var V;return[(V=r.close)==null?void 0:V.call(r,{props:O.value})]}}):v(pt,ue({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},O.value),null)])]}})}}});const aC=z({text:String,clickable:Boolean,...ye(),...je()},"VLabel"),ev=se()({name:"VLabel",props:aC(),setup(e,t){let{slots:n}=t;return ve(()=>{var r;return v("label",{class:["v-label",{"v-label--clickable":e.clickable},e.class],style:e.style},[e.text,(r=n.default)==null?void 0:r.call(n)])}),{}}});const tv=Symbol.for("vuetify:selection-control-group"),nv=z({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:Ae,trueIcon:Ae,ripple:{type:Boolean,default:!0},multiple:{type:Boolean,default:null},name:String,readonly:Boolean,modelValue:null,type:String,valueComparator:{type:Function,default:Br},...ye(),...Vt(),...je()},"SelectionControlGroup"),oC=z({...nv({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup");se()({name:"VSelectionControlGroup",props:oC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"modelValue"),s=At(),i=S(()=>e.id||`v-selection-control-group-${s}`),a=S(()=>e.name||i.value),o=new Set;return wt(tv,{modelValue:r,forceUpdate:()=>{o.forEach(l=>l())},onForceUpdate:l=>{o.add(l),lt(()=>{o.delete(l)})}}),Bn({[e.defaultsTarget]:{color:de(e,"color"),disabled:de(e,"disabled"),density:de(e,"density"),error:de(e,"error"),inline:de(e,"inline"),modelValue:r,multiple:S(()=>!!e.multiple||e.multiple==null&&Array.isArray(r.value)),name:a,falseIcon:de(e,"falseIcon"),trueIcon:de(e,"trueIcon"),readonly:de(e,"readonly"),ripple:de(e,"ripple"),type:de(e,"type"),valueComparator:de(e,"valueComparator")}}),ve(()=>{var l;return v("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[(l=n.default)==null?void 0:l.call(n)])}),{}}});const rv=z({label:String,trueValue:null,falseValue:null,value:null,...ye(),...nv()},"VSelectionControl");function lC(e){const t=Re(tv,void 0),{densityClasses:n}=qt(e),r=Be(e,"modelValue"),s=S(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),i=S(()=>e.falseValue!==void 0?e.falseValue:!1),a=S(()=>!!e.multiple||e.multiple==null&&Array.isArray(r.value)),o=S({get(){const d=t?t.modelValue.value:r.value;return a.value?d.some(f=>e.valueComparator(f,s.value)):e.valueComparator(d,s.value)},set(d){if(e.readonly)return;const f=d?s.value:i.value;let m=f;a.value&&(m=d?[...Dn(r.value),f]:Dn(r.value).filter(h=>!e.valueComparator(h,s.value))),t?t.modelValue.value=m:r.value=m}}),{textColorClasses:l,textColorStyles:u}=en(S(()=>o.value&&!e.error&&!e.disabled?e.color:void 0)),c=S(()=>o.value?e.trueIcon:e.falseIcon);return{group:t,densityClasses:n,trueValue:s,falseValue:i,model:o,textColorClasses:l,textColorStyles:u,icon:c}}const uC=se()({name:"VSelectionControl",directives:{Ripple:Bs},inheritAttrs:!1,props:rv(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:r}=t;const{group:s,densityClasses:i,icon:a,model:o,textColorClasses:l,textColorStyles:u,trueValue:c}=lC(e),d=At(),f=S(()=>e.id||`input-${d}`),m=be(!1),h=be(!1),g=K();s==null||s.onForceUpdate(()=>{g.value&&(g.value.checked=o.value)});function y(w){m.value=!0,(!Eo||Eo&&w.target.matches(":focus-visible"))&&(h.value=!0)}function E(){m.value=!1,h.value=!1}function _(w){e.readonly&&s&&ut(()=>s.forceUpdate()),o.value=w.target.checked}return ve(()=>{var k,C;const w=r.label?r.label({label:e.label,props:{for:f.value}}):e.label,[O,I]=wl(n);return v("div",ue({class:["v-selection-control",{"v-selection-control--dirty":o.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":m.value,"v-selection-control--focus-visible":h.value,"v-selection-control--inline":e.inline},i.value,e.class]},O,{style:e.style}),[v("div",{class:["v-selection-control__wrapper",l.value],style:u.value},[(k=r.default)==null?void 0:k.call(r),ft(v("div",{class:["v-selection-control__input"]},[a.value&&v(et,{key:"icon",icon:a.value},null),v("input",ue({ref:g,checked:o.value,disabled:!!(e.readonly||e.disabled),id:f.value,onBlur:E,onFocus:y,onInput:_,"aria-disabled":!!(e.readonly||e.disabled),type:e.type,value:c.value,name:e.name,"aria-checked":e.type==="checkbox"?o.value:void 0},I),null),(C=r.input)==null?void 0:C.call(r,{model:o,textColorClasses:l,textColorStyles:u,props:{onFocus:y,onBlur:E,id:f.value}})]),[[dn("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),w&&v(ev,{for:f.value,clickable:!0},{default:()=>[w]})])}),{isFocused:m,input:g}}}),sv=z({indeterminate:Boolean,indeterminateIcon:{type:Ae,default:"$checkboxIndeterminate"},...rv({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),zo=se()({name:"VCheckboxBtn",props:sv(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"indeterminate"),s=Be(e,"modelValue");function i(l){r.value&&(r.value=!1)}const a=S(()=>r.value?e.indeterminateIcon:e.falseIcon),o=S(()=>r.value?e.indeterminateIcon:e.trueIcon);return ve(()=>v(uC,ue(e,{modelValue:s.value,"onUpdate:modelValue":[l=>s.value=l,i],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:a.value,trueIcon:o.value,"aria-checked":r.value?"mixed":void 0}),n)),{}}});function iv(e){const{t}=ua();function n(r){let{name:s}=r;const i={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[s],a=e[`onClick:${s}`],o=a&&i?t(`$vuetify.input.${i}`,e.label??""):void 0;return v(et,{icon:e[`${s}Icon`],"aria-label":o,onClick:a},null)}return{InputIcon:n}}const cC=z({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},...ye(),...Us({transition:{component:$h,leaveAbsolute:!0,group:!0}})},"VMessages"),dC=se()({name:"VMessages",props:cC(),setup(e,t){let{slots:n}=t;const r=S(()=>Dn(e.messages)),{textColorClasses:s,textColorStyles:i}=en(S(()=>e.color));return ve(()=>v(Pn,{transition:e.transition,tag:"div",class:["v-messages",s.value,e.class],style:[i.value,e.style],role:"alert","aria-live":"polite"},{default:()=>[e.active&&r.value.map((a,o)=>v("div",{class:"v-messages__message",key:`${o}-${r.value}`},[n.message?n.message({message:a}):a]))]})),{}}}),av=z({focused:Boolean,"onUpdate:focused":Qt()},"focus");function Gl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn();const n=Be(e,"focused"),r=S(()=>({[`${t}--focused`]:n.value}));function s(){n.value=!0}function i(){n.value=!1}return{focusClasses:r,isFocused:n,focus:s,blur:i}}const fC=Symbol.for("vuetify:form");function ov(){return Re(fC,null)}const mC=z({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...av()},"validation");function hC(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:wn(),n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:At();const r=Be(e,"modelValue"),s=S(()=>e.validationValue===void 0?r.value:e.validationValue),i=ov(),a=K([]),o=be(!0),l=S(()=>!!(Dn(r.value===""?null:r.value).length||Dn(s.value===""?null:s.value).length)),u=S(()=>!!(e.disabled??(i==null?void 0:i.isDisabled.value))),c=S(()=>!!(e.readonly??(i==null?void 0:i.isReadonly.value))),d=S(()=>e.errorMessages.length?Dn(e.errorMessages).slice(0,Math.max(0,+e.maxErrors)):a.value),f=S(()=>{let O=(e.validateOn??(i==null?void 0:i.validateOn.value))||"input";O==="lazy"&&(O="input lazy");const I=new Set((O==null?void 0:O.split(" "))??[]);return{blur:I.has("blur")||I.has("input"),input:I.has("input"),submit:I.has("submit"),lazy:I.has("lazy")}}),m=S(()=>e.error||e.errorMessages.length?!1:e.rules.length?o.value?a.value.length||f.value.lazy?null:!0:!a.value.length:!0),h=be(!1),g=S(()=>({[`${t}--error`]:m.value===!1,[`${t}--dirty`]:l.value,[`${t}--disabled`]:u.value,[`${t}--readonly`]:c.value})),y=S(()=>e.name??tt(n));dl(()=>{i==null||i.register({id:y.value,validate:w,reset:E,resetValidation:_})}),bn(()=>{i==null||i.unregister(y.value)}),pn(async()=>{f.value.lazy||await w(!0),i==null||i.update(y.value,m.value,d.value)}),Ln(()=>f.value.input,()=>{he(s,()=>{if(s.value!=null)w();else if(e.focused){const O=he(()=>e.focused,I=>{I||w(),O()})}})}),Ln(()=>f.value.blur,()=>{he(()=>e.focused,O=>{O||w()})}),he(m,()=>{i==null||i.update(y.value,m.value,d.value)});function E(){r.value=null,ut(_)}function _(){o.value=!0,f.value.lazy?a.value=[]:w(!0)}async function w(){let O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const I=[];h.value=!0;for(const k of e.rules){if(I.length>=+(e.maxErrors??1))break;const p=await(typeof k=="function"?k:()=>k)(s.value);if(p!==!0){if(p!==!1&&typeof p!="string"){console.warn(`${p} is not a valid value. Rule functions must return boolean true or a string.`);continue}I.push(p||"")}}return a.value=I,h.value=!1,o.value=O,a.value}return{errorMessages:d,isDirty:l,isDisabled:u,isReadonly:c,isPristine:o,isValid:m,isValidating:h,reset:E,resetValidation:_,validate:w,validationClasses:g}}const Kl=z({id:String,appendIcon:Ae,centerAffix:{type:Boolean,default:!0},prependIcon:Ae,hideDetails:[Boolean,String],hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":Qt(),"onClick:append":Qt(),...ye(),...Vt(),...mC()},"VInput"),qi=se()({name:"VInput",props:{...Kl()},emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:r,emit:s}=t;const{densityClasses:i}=qt(e),{rtlClasses:a}=mr(),{InputIcon:o}=iv(e),l=At(),u=S(()=>e.id||`input-${l}`),c=S(()=>`${u.value}-messages`),{errorMessages:d,isDirty:f,isDisabled:m,isReadonly:h,isPristine:g,isValid:y,isValidating:E,reset:_,resetValidation:w,validate:O,validationClasses:I}=hC(e,"v-input",u),k=S(()=>({id:u,messagesId:c,isDirty:f,isDisabled:m,isReadonly:h,isPristine:g,isValid:y,isValidating:E,reset:_,resetValidation:w,validate:O})),C=S(()=>{var p;return(p=e.errorMessages)!=null&&p.length||!g.value&&d.value.length?d.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages});return ve(()=>{var P,R,F,G;const p=!!(r.prepend||e.prependIcon),T=!!(r.append||e.appendIcon),V=C.value.length>0,$=!e.hideDetails||e.hideDetails==="auto"&&(V||!!r.details);return v("div",{class:["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix},i.value,a.value,I.value,e.class],style:e.style},[p&&v("div",{key:"prepend",class:"v-input__prepend"},[(P=r.prepend)==null?void 0:P.call(r,k.value),e.prependIcon&&v(o,{key:"prepend-icon",name:"prepend"},null)]),r.default&&v("div",{class:"v-input__control"},[(R=r.default)==null?void 0:R.call(r,k.value)]),T&&v("div",{key:"append",class:"v-input__append"},[e.appendIcon&&v(o,{key:"append-icon",name:"append"},null),(F=r.append)==null?void 0:F.call(r,k.value)]),$&&v("div",{class:"v-input__details"},[v(dC,{id:c.value,active:V,messages:C.value},{message:r.message}),(G=r.details)==null?void 0:G.call(r,k.value)])])}),{reset:_,resetValidation:w,validate:O}}}),vC=z({...Kl(),...Ds(sv(),["inline"])},"VCheckbox"),ad=se()({name:"VCheckbox",inheritAttrs:!1,props:vC(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,t){let{attrs:n,slots:r}=t;const s=Be(e,"modelValue"),{isFocused:i,focus:a,blur:o}=Gl(e),l=At(),u=S(()=>e.id||`checkbox-${l}`);return ve(()=>{const[c,d]=wl(n),[f,m]=qi.filterProps(e),[h,g]=zo.filterProps(e);return v(qi,ue({class:["v-checkbox",e.class]},c,f,{modelValue:s.value,"onUpdate:modelValue":y=>s.value=y,id:u.value,focused:i.value,style:e.style}),{...r,default:y=>{let{id:E,messagesId:_,isDisabled:w,isReadonly:O}=y;return v(zo,ue(h,{id:E.value,"aria-describedby":_.value,disabled:w.value,readonly:O.value},d,{modelValue:s.value,"onUpdate:modelValue":I=>s.value=I,onFocus:a,onBlur:o}),r)}})}),{}}});const lv=Symbol.for("vuetify:v-chip-group"),gC=z({column:Boolean,filter:Boolean,valueComparator:{type:Function,default:Br},...ye(),...Hf({selectedClass:"v-chip--selected"}),...Xe(),...je(),...tn({variant:"tonal"})},"VChipGroup");se()({name:"VChipGroup",props:gC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:r}=Qe(e),{isSelected:s,select:i,next:a,prev:o,selected:l}=jf(e,lv);return Bn({VChip:{color:de(e,"color"),disabled:de(e,"disabled"),filter:de(e,"filter"),variant:de(e,"variant")}}),ve(()=>v(e.tag,{class:["v-chip-group",{"v-chip-group--column":e.column},r.value,e.class],style:e.style},{default:()=>{var u;return[(u=n.default)==null?void 0:u.call(n,{isSelected:s,select:i,next:a,prev:o,selected:l.value})]}})),{}}});const yC=z({activeClass:String,appendAvatar:String,appendIcon:Ae,closable:Boolean,closeIcon:{type:Ae,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:Ae,ripple:{type:[Boolean,Object],default:!0},text:String,modelValue:{type:Boolean,default:!0},onClick:Qt(),onClickOnce:Qt(),...ur(),...ye(),...Vt(),...Hn(),...Uf(),...Pt(),...ma(),...Ns(),...Xe({tag:"span"}),...je(),...tn({variant:"tonal"})},"VChip"),pC=se()({name:"VChip",directives:{Ripple:Bs},props:yC(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,t){let{attrs:n,emit:r,slots:s}=t;const{t:i}=ua(),{borderClasses:a}=cr(e),{colorClasses:o,colorStyles:l,variantClasses:u}=fr(e),{densityClasses:c}=qt(e),{elevationClasses:d}=Un(e),{roundedClasses:f}=Mt(e),{sizeClasses:m}=Ls(e),{themeClasses:h}=Qe(e),g=Be(e,"modelValue"),y=zf(e,lv,!1),E=fa(e,n),_=S(()=>e.link!==!1&&E.isLink.value),w=S(()=>!e.disabled&&e.link!==!1&&(!!y||e.link||E.isClickable.value)),O=S(()=>({"aria-label":i(e.closeLabel),onClick(C){g.value=!1,r("click:close",C)}}));function I(C){var p;r("click",C),w.value&&((p=E.navigate)==null||p.call(E,C),y==null||y.toggle())}function k(C){(C.key==="Enter"||C.key===" ")&&(C.preventDefault(),I(C))}return()=>{const C=E.isLink.value?"a":e.tag,p=!!(e.appendIcon||e.appendAvatar),T=!!(p||s.append),V=!!(s.close||e.closable),$=!!(s.filter||e.filter)&&y,P=!!(e.prependIcon||e.prependAvatar),R=!!(P||s.prepend),F=!y||y.isSelected.value;return g.value&&ft(v(C,{class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":w.value,"v-chip--filter":$,"v-chip--pill":e.pill},h.value,a.value,F?o.value:void 0,c.value,d.value,f.value,m.value,u.value,y==null?void 0:y.selectedClass.value,e.class],style:[F?l.value:void 0,e.style],disabled:e.disabled||void 0,draggable:e.draggable,href:E.href.value,tabindex:w.value?0:void 0,onClick:I,onKeydown:w.value&&!_.value&&k},{default:()=>{var G;return[dr(w.value,"v-chip"),$&&v(Hh,{key:"filter"},{default:()=>[ft(v("div",{class:"v-chip__filter"},[s.filter?ft(v(Le,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},null),[[dn("slot"),s.filter,"default"]]):v(et,{key:"filter-icon",icon:e.filterIcon},null)]),[[$r,y.isSelected.value]])]}),R&&v("div",{key:"prepend",class:"v-chip__prepend"},[s.prepend?v(Le,{key:"prepend-defaults",disabled:!P,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},s.prepend):v(ke,null,[e.prependIcon&&v(et,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&v(Dr,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),v("div",{class:"v-chip__content"},[((G=s.default)==null?void 0:G.call(s,{isSelected:y==null?void 0:y.isSelected.value,selectedClass:y==null?void 0:y.selectedClass.value,select:y==null?void 0:y.select,toggle:y==null?void 0:y.toggle,value:y==null?void 0:y.value.value,disabled:e.disabled}))??e.text]),T&&v("div",{key:"append",class:"v-chip__append"},[s.append?v(Le,{key:"append-defaults",disabled:!p,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},s.append):v(ke,null,[e.appendIcon&&v(et,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&v(Dr,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),V&&v("div",ue({key:"close",class:"v-chip__close"},O.value),[s.close?v(Le,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},s.close):v(et,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}}),[[dn("ripple"),w.value&&e.ripple,null]])}}});const jo=Symbol.for("vuetify:list");function uv(){const e=Re(jo,{hasPrepend:be(!1),updateHasPrepend:()=>null}),t={hasPrepend:be(!1),updateHasPrepend:n=>{n&&(t.hasPrepend.value=n)}};return wt(jo,t),e}function cv(){return Re(jo,null)}const bC={open:e=>{let{id:t,value:n,opened:r,parents:s}=e;if(n){const i=new Set;i.add(t);let a=s.get(t);for(;a!=null;)i.add(a),a=s.get(a);return i}else return r.delete(t),r},select:()=>null},dv={open:e=>{let{id:t,value:n,opened:r,parents:s}=e;if(n){let i=s.get(t);for(r.add(t);i!=null&&i!==t;)r.add(i),i=s.get(i);return r}else r.delete(t);return r},select:()=>null},wC={open:dv.open,select:e=>{let{id:t,value:n,opened:r,parents:s}=e;if(!n)return r;const i=[];let a=s.get(t);for(;a!=null;)i.push(a),a=s.get(a);return new Set(i)}},Jl=e=>{const t={select:n=>{let{id:r,value:s,selected:i}=n;if(r=ce(r),e&&!s){const a=Array.from(i.entries()).reduce((o,l)=>{let[u,c]=l;return c==="on"?[...o,u]:o},[]);if(a.length===1&&a[0]===r)return i}return i.set(r,s?"on":"off"),i},in:(n,r,s)=>{let i=new Map;for(const a of n||[])i=t.select({id:a,value:!0,selected:new Map(i),children:r,parents:s});return i},out:n=>{const r=[];for(const[s,i]of n.entries())i==="on"&&r.push(s);return r}};return t},fv=e=>{const t=Jl(e);return{select:r=>{let{selected:s,id:i,...a}=r;i=ce(i);const o=s.has(i)?new Map([[i,s.get(i)]]):new Map;return t.select({...a,id:i,selected:o})},in:(r,s,i)=>{let a=new Map;return r!=null&&r.length&&(a=t.in(r.slice(0,1),s,i)),a},out:(r,s,i)=>t.out(r,s,i)}},SC=e=>{const t=Jl(e);return{select:r=>{let{id:s,selected:i,children:a,...o}=r;return s=ce(s),a.has(s)?i:t.select({id:s,selected:i,children:a,...o})},in:t.in,out:t.out}},CC=e=>{const t=fv(e);return{select:r=>{let{id:s,selected:i,children:a,...o}=r;return s=ce(s),a.has(s)?i:t.select({id:s,selected:i,children:a,...o})},in:t.in,out:t.out}},xC=e=>{const t={select:n=>{let{id:r,value:s,selected:i,children:a,parents:o}=n;r=ce(r);const l=new Map(i),u=[r];for(;u.length;){const d=u.shift();i.set(d,s?"on":"off"),a.has(d)&&u.push(...a.get(d))}let c=o.get(r);for(;c;){const d=a.get(c),f=d.every(h=>i.get(h)==="on"),m=d.every(h=>!i.has(h)||i.get(h)==="off");i.set(c,f?"on":m?"off":"indeterminate"),c=o.get(c)}return e&&!s&&Array.from(i.entries()).reduce((f,m)=>{let[h,g]=m;return g==="on"?[...f,h]:f},[]).length===0?l:i},in:(n,r,s)=>{let i=new Map;for(const a of n||[])i=t.select({id:a,value:!0,selected:new Map(i),children:r,parents:s});return i},out:(n,r)=>{const s=[];for(const[i,a]of n.entries())a==="on"&&!r.has(i)&&s.push(i);return s}};return t},As=Symbol.for("vuetify:nested"),mv={id:be(),root:{register:()=>null,unregister:()=>null,parents:K(new Map),children:K(new Map),open:()=>null,openOnSelect:()=>null,select:()=>null,opened:K(new Set),selected:K(new Map),selectedValues:K([])}},_C=z({selectStrategy:[String,Function],openStrategy:[String,Object],opened:Array,selected:Array,mandatory:Boolean},"nested"),EC=e=>{let t=!1;const n=K(new Map),r=K(new Map),s=Be(e,"opened",e.opened,d=>new Set(d),d=>[...d.values()]),i=S(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single-leaf":return CC(e.mandatory);case"leaf":return SC(e.mandatory);case"independent":return Jl(e.mandatory);case"single-independent":return fv(e.mandatory);case"classic":default:return xC(e.mandatory)}}),a=S(()=>{if(typeof e.openStrategy=="object")return e.openStrategy;switch(e.openStrategy){case"list":return wC;case"single":return bC;case"multiple":default:return dv}}),o=Be(e,"selected",e.selected,d=>i.value.in(d,n.value,r.value),d=>i.value.out(d,n.value,r.value));bn(()=>{t=!0});function l(d){const f=[];let m=d;for(;m!=null;)f.unshift(m),m=r.value.get(m);return f}const u=it("nested"),c={id:be(),root:{opened:s,selected:o,selectedValues:S(()=>{const d=[];for(const[f,m]of o.value.entries())m==="on"&&d.push(f);return d}),register:(d,f,m)=>{f&&d!==f&&r.value.set(d,f),m&&n.value.set(d,[]),f!=null&&n.value.set(f,[...n.value.get(f)||[],d])},unregister:d=>{if(t)return;n.value.delete(d);const f=r.value.get(d);if(f){const m=n.value.get(f)??[];n.value.set(f,m.filter(h=>h!==d))}r.value.delete(d),s.value.delete(d)},open:(d,f,m)=>{u.emit("click:open",{id:d,value:f,path:l(d),event:m});const h=a.value.open({id:d,value:f,opened:new Set(s.value),children:n.value,parents:r.value,event:m});h&&(s.value=h)},openOnSelect:(d,f,m)=>{const h=a.value.select({id:d,value:f,selected:new Map(o.value),opened:new Set(s.value),children:n.value,parents:r.value,event:m});h&&(s.value=h)},select:(d,f,m)=>{u.emit("click:select",{id:d,value:f,path:l(d),event:m});const h=i.value.select({id:d,value:f,selected:new Map(o.value),children:n.value,parents:r.value,event:m});h&&(o.value=h),c.root.openOnSelect(d,f,m)},children:n,parents:r}};return wt(As,c),c.root},hv=(e,t)=>{const n=Re(As,mv),r=Symbol(At()),s=S(()=>e.value!==void 0?e.value:r),i={...n,id:s,open:(a,o)=>n.root.open(s.value,a,o),openOnSelect:(a,o)=>n.root.openOnSelect(s.value,a,o),isOpen:S(()=>n.root.opened.value.has(s.value)),parent:S(()=>n.root.parents.value.get(s.value)),select:(a,o)=>n.root.select(s.value,a,o),isSelected:S(()=>n.root.selected.value.get(ce(s.value))==="on"),isIndeterminate:S(()=>n.root.selected.value.get(s.value)==="indeterminate"),isLeaf:S(()=>!n.root.children.value.get(s.value)),isGroupActivator:n.isGroupActivator};return!n.isGroupActivator&&n.root.register(s.value,n.id.value,t),bn(()=>{!n.isGroupActivator&&n.root.unregister(s.value)}),t&&wt(As,i),i},TC=()=>{const e=Re(As,mv);wt(As,{...e,isGroupActivator:!0})};function kC(){const e=be(!1);return pn(()=>{window.requestAnimationFrame(()=>{e.value=!0})}),{ssrBootStyles:S(()=>e.value?void 0:{transition:"none !important"}),isBooted:Ps(e)}}const OC=Hr({name:"VListGroupActivator",setup(e,t){let{slots:n}=t;return TC(),()=>{var r;return(r=n.default)==null?void 0:r.call(n)}}}),IC=z({activeColor:String,baseColor:String,color:String,collapseIcon:{type:Ae,default:"$collapse"},expandIcon:{type:Ae,default:"$expand"},prependIcon:Ae,appendIcon:Ae,fluid:Boolean,subgroup:Boolean,title:String,value:null,...ye(),...Xe()},"VListGroup"),od=se()({name:"VListGroup",props:IC(),setup(e,t){let{slots:n}=t;const{isOpen:r,open:s,id:i}=hv(de(e,"value"),!0),a=S(()=>`v-list-group--id-${String(i.value)}`),o=cv(),{isBooted:l}=kC();function u(m){s(!r.value,m)}const c=S(()=>({onClick:u,class:"v-list-group__header",id:a.value})),d=S(()=>r.value?e.collapseIcon:e.expandIcon),f=S(()=>({VListItem:{active:r.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&d.value,appendIcon:e.appendIcon||!e.subgroup&&d.value,title:e.title,value:e.value}}));return ve(()=>v(e.tag,{class:["v-list-group",{"v-list-group--prepend":o==null?void 0:o.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":r.value},e.class],style:e.style},{default:()=>[n.activator&&v(Le,{defaults:f.value},{default:()=>[v(OC,null,{default:()=>[n.activator({props:c.value,isOpen:r.value})]})]}),v(Pn,{transition:{component:Bh},disabled:!l.value},{default:()=>{var m;return[ft(v("div",{class:"v-list-group__items",role:"group","aria-labelledby":a.value},[(m=n.default)==null?void 0:m.call(n)]),[[$r,r.value]])]}})]})),{}}});const AC=lr("v-list-item-subtitle"),VC=lr("v-list-item-title"),PC=z({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:Ae,baseColor:String,disabled:Boolean,lines:String,link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:Ae,ripple:{type:[Boolean,Object],default:!0},subtitle:[String,Number,Boolean],title:[String,Number,Boolean],value:null,onClick:Qt(),onClickOnce:Qt(),...ur(),...ye(),...Vt(),...zn(),...Hn(),...Pt(),...ma(),...Xe(),...je(),...tn({variant:"text"})},"VListItem"),Zi=se()({name:"VListItem",directives:{Ripple:Bs},props:PC(),emits:{click:e=>!0},setup(e,t){let{attrs:n,slots:r,emit:s}=t;const i=fa(e,n),a=S(()=>e.value===void 0?i.href.value:e.value),{select:o,isSelected:l,isIndeterminate:u,isGroupActivator:c,root:d,parent:f,openOnSelect:m}=hv(a,!1),h=cv(),g=S(()=>{var U;return e.active!==!1&&(e.active||((U=i.isActive)==null?void 0:U.value)||l.value)}),y=S(()=>e.link!==!1&&i.isLink.value),E=S(()=>!e.disabled&&e.link!==!1&&(e.link||i.isClickable.value||e.value!=null&&!!h)),_=S(()=>e.rounded||e.nav),w=S(()=>e.color??e.activeColor),O=S(()=>({color:g.value?w.value??e.baseColor:e.baseColor,variant:e.variant}));he(()=>{var U;return(U=i.isActive)==null?void 0:U.value},U=>{U&&f.value!=null&&d.open(f.value,!0),U&&m(U)},{immediate:!0});const{themeClasses:I}=Qe(e),{borderClasses:k}=cr(e),{colorClasses:C,colorStyles:p,variantClasses:T}=fr(O),{densityClasses:V}=qt(e),{dimensionStyles:$}=jn(e),{elevationClasses:P}=Un(e),{roundedClasses:R}=Mt(_),F=S(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),G=S(()=>({isActive:g.value,select:o,isSelected:l.value,isIndeterminate:u.value}));function Z(U){var W;s("click",U),!(c||!E.value)&&((W=i.navigate)==null||W.call(i,U),e.value!=null&&o(!l.value,U))}function ee(U){(U.key==="Enter"||U.key===" ")&&(U.preventDefault(),Z(U))}return ve(()=>{const U=y.value?"a":e.tag,W=r.title||e.title,fe=r.subtitle||e.subtitle,oe=!!(e.appendAvatar||e.appendIcon),Ce=!!(oe||r.append),De=!!(e.prependAvatar||e.prependIcon),He=!!(De||r.prepend);return h==null||h.updateHasPrepend(He),e.activeColor&&Cp("active-color",["color","base-color"]),ft(v(U,{class:["v-list-item",{"v-list-item--active":g.value,"v-list-item--disabled":e.disabled,"v-list-item--link":E.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!He&&(h==null?void 0:h.hasPrepend.value),[`${e.activeClass}`]:e.activeClass&&g.value},I.value,k.value,C.value,V.value,P.value,F.value,R.value,T.value,e.class],style:[p.value,$.value,e.style],href:i.href.value,tabindex:E.value?h?-2:0:void 0,onClick:Z,onKeydown:E.value&&!y.value&&ee},{default:()=>{var Ft;return[dr(E.value||g.value,"v-list-item"),He&&v("div",{key:"prepend",class:"v-list-item__prepend"},[r.prepend?v(Le,{key:"prepend-defaults",disabled:!De,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>{var ge;return[(ge=r.prepend)==null?void 0:ge.call(r,G.value)]}}):v(ke,null,[e.prependAvatar&&v(Dr,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&v(et,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),v("div",{class:"v-list-item__content","data-no-activator":""},[W&&v(VC,{key:"title"},{default:()=>{var ge;return[((ge=r.title)==null?void 0:ge.call(r,{title:e.title}))??e.title]}}),fe&&v(AC,{key:"subtitle"},{default:()=>{var ge;return[((ge=r.subtitle)==null?void 0:ge.call(r,{subtitle:e.subtitle}))??e.subtitle]}}),(Ft=r.default)==null?void 0:Ft.call(r,G.value)]),Ce&&v("div",{key:"append",class:"v-list-item__append"},[r.append?v(Le,{key:"append-defaults",disabled:!oe,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>{var ge;return[(ge=r.append)==null?void 0:ge.call(r,G.value)]}}):v(ke,null,[e.appendIcon&&v(et,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&v(Dr,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])]}}),[[dn("ripple"),E.value&&e.ripple]])}),{}}}),MC=z({color:String,inset:Boolean,sticky:Boolean,title:String,...ye(),...Xe()},"VListSubheader"),FC=se()({name:"VListSubheader",props:MC(),setup(e,t){let{slots:n}=t;const{textColorClasses:r,textColorStyles:s}=en(de(e,"color"));return ve(()=>{const i=!!(n.default||e.title);return v(e.tag,{class:["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},r.value,e.class],style:[{textColorStyles:s},e.style]},{default:()=>{var a;return[i&&v("div",{class:"v-list-subheader__text"},[((a=n.default)==null?void 0:a.call(n))??e.title])]}})}),{}}});const DC=z({color:String,inset:Boolean,length:[Number,String],thickness:[Number,String],vertical:Boolean,...ye(),...je()},"VDivider"),NC=se()({name:"VDivider",props:DC(),setup(e,t){let{attrs:n}=t;const{themeClasses:r}=Qe(e),{textColorClasses:s,textColorStyles:i}=en(de(e,"color")),a=S(()=>{const o={};return e.length&&(o[e.vertical?"maxHeight":"maxWidth"]=ae(e.length)),e.thickness&&(o[e.vertical?"borderRightWidth":"borderTopWidth"]=ae(e.thickness)),o});return ve(()=>v("hr",{class:[{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},r.value,s.value,e.class],style:[a.value,i.value,e.style],"aria-orientation":!n.role||n.role==="separator"?e.vertical?"vertical":"horizontal":void 0,role:`${n.role||"separator"}`},null)),{}}}),LC=z({items:Array},"VListChildren"),vv=se()({name:"VListChildren",props:LC(),setup(e,t){let{slots:n}=t;return uv(),()=>{var r,s;return((r=n.default)==null?void 0:r.call(n))??((s=e.items)==null?void 0:s.map(i=>{var m,h;let{children:a,props:o,type:l,raw:u}=i;if(l==="divider")return((m=n.divider)==null?void 0:m.call(n,{props:o}))??v(NC,o,null);if(l==="subheader")return((h=n.subheader)==null?void 0:h.call(n,{props:o}))??v(FC,o,null);const c={subtitle:n.subtitle?g=>{var y;return(y=n.subtitle)==null?void 0:y.call(n,{...g,item:u})}:void 0,prepend:n.prepend?g=>{var y;return(y=n.prepend)==null?void 0:y.call(n,{...g,item:u})}:void 0,append:n.append?g=>{var y;return(y=n.append)==null?void 0:y.call(n,{...g,item:u})}:void 0,title:n.title?g=>{var y;return(y=n.title)==null?void 0:y.call(n,{...g,item:u})}:void 0},[d,f]=od.filterProps(o);return a?v(od,ue({value:o==null?void 0:o.value},d),{activator:g=>{let{props:y}=g;return n.header?n.header({props:{...o,...y}}):v(Zi,ue(o,y),c)},default:()=>v(vv,{items:a},n)}):n.item?n.item({props:o}):v(Zi,o,c)}))}}}),gv=z({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean},"list-items");function yv(e,t){const n=an(t,e.itemTitle,t),r=e.returnObject?t:an(t,e.itemValue,n),s=an(t,e.itemChildren),i=e.itemProps===!0?typeof t=="object"&&t!=null&&!Array.isArray(t)?"children"in t?Fs(t,["children"])[1]:t:void 0:an(t,e.itemProps),a={title:n,value:r,...i};return{title:String(a.title??""),value:a.value,props:a,children:Array.isArray(s)?pv(e,s):void 0,raw:t}}function pv(e,t){const n=[];for(const r of t)n.push(yv(e,r));return n}function RC(e){const t=S(()=>pv(e,e.items));return $C(t,n=>yv(e,n))}function $C(e,t){function n(s){return s.filter(i=>i!==null||e.value.some(a=>a.value===null)).map(i=>e.value.find(o=>Br(i,o.value))??t(i))}function r(s){return s.map(i=>{let{value:a}=i;return a})}return{items:e,transformIn:n,transformOut:r}}function BC(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"}function HC(e,t){const n=an(t,e.itemType,"item"),r=BC(t)?t:an(t,e.itemTitle),s=an(t,e.itemValue,void 0),i=an(t,e.itemChildren),a=e.itemProps===!0?Fs(t,["children"])[1]:an(t,e.itemProps),o={title:r,value:s,...a};return{type:n,title:o.title,value:o.value,props:o,children:n==="item"&&i?bv(e,i):void 0,raw:t}}function bv(e,t){const n=[];for(const r of t)n.push(HC(e,r));return n}function UC(e){return{items:S(()=>bv(e,e.items))}}const zC=z({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,lines:{type:[Boolean,String],default:"one"},nav:Boolean,..._C({selectStrategy:"single-leaf",openStrategy:"list"}),...ur(),...ye(),...Vt(),...zn(),...Hn(),itemType:{type:String,default:"type"},...gv(),...Pt(),...Xe(),...je(),...tn({variant:"text"})},"VList"),jC=se()({name:"VList",props:zC(),emits:{"update:selected":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:select":e=>!0},setup(e,t){let{slots:n}=t;const{items:r}=UC(e),{themeClasses:s}=Qe(e),{backgroundColorClasses:i,backgroundColorStyles:a}=Pr(de(e,"bgColor")),{borderClasses:o}=cr(e),{densityClasses:l}=qt(e),{dimensionStyles:u}=jn(e),{elevationClasses:c}=Un(e),{roundedClasses:d}=Mt(e),{open:f,select:m}=EC(e),h=S(()=>e.lines?`v-list--${e.lines}-line`:void 0),g=de(e,"activeColor"),y=de(e,"baseColor"),E=de(e,"color");uv(),Bn({VListGroup:{activeColor:g,baseColor:y,color:E},VListItem:{activeClass:de(e,"activeClass"),activeColor:g,baseColor:y,color:E,density:de(e,"density"),disabled:de(e,"disabled"),lines:de(e,"lines"),nav:de(e,"nav"),variant:de(e,"variant")}});const _=be(!1),w=K();function O(T){_.value=!0}function I(T){_.value=!1}function k(T){var V;!_.value&&!(T.relatedTarget&&((V=w.value)!=null&&V.contains(T.relatedTarget)))&&p()}function C(T){if(w.value){if(T.key==="ArrowDown")p("next");else if(T.key==="ArrowUp")p("prev");else if(T.key==="Home")p("first");else if(T.key==="End")p("last");else return;T.preventDefault()}}function p(T){if(w.value)return Pi(w.value,T)}return ve(()=>v(e.tag,{ref:w,class:["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav},s.value,i.value,o.value,l.value,c.value,h.value,d.value,e.class],style:[a.value,u.value,e.style],tabindex:e.disabled||_.value?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:O,onFocusout:I,onFocus:k,onKeydown:C},{default:()=>[v(vv,{items:r.value},n)]})),{open:f,select:m,focus:p}}});const WC=z({id:String,...Ds(zs({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:ql}}),["absolute"])},"VMenu"),qC=se()({name:"VMenu",props:WC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"modelValue"),{scopeId:s}=Hs(),i=At(),a=S(()=>e.id||`v-menu-${i}`),o=K(),l=Re(Vo,null),u=be(0);wt(Vo,{register(){++u.value},unregister(){--u.value},closeParents(){setTimeout(()=>{u.value||(r.value=!1,l==null||l.closeParents())},40)}}),he(r,h=>{h?l==null||l.register():l==null||l.unregister()});function c(){l==null||l.closeParents()}function d(h){var g,y;e.disabled||h.key==="Tab"&&(r.value=!1,(y=(g=o.value)==null?void 0:g.activatorEl)==null||y.focus())}function f(h){var y;if(e.disabled)return;const g=(y=o.value)==null?void 0:y.contentEl;g&&r.value?h.key==="ArrowDown"?(h.preventDefault(),Pi(g,"next")):h.key==="ArrowUp"&&(h.preventDefault(),Pi(g,"prev")):["ArrowDown","ArrowUp"].includes(h.key)&&(r.value=!0,h.preventDefault(),setTimeout(()=>setTimeout(()=>f(h))))}const m=S(()=>ue({"aria-haspopup":"menu","aria-expanded":String(r.value),"aria-owns":a.value,onKeydown:f},e.activatorProps));return ve(()=>{const[h]=Rn.filterProps(e);return v(Rn,ue({ref:o,class:["v-menu",e.class],style:e.style},h,{modelValue:r.value,"onUpdate:modelValue":g=>r.value=g,absolute:!0,activatorProps:m.value,"onClick:outside":c,onKeydown:d},s),{activator:n.activator,default:function(){for(var g=arguments.length,y=new Array(g),E=0;E<g;E++)y[E]=arguments[E];return v(Le,{root:"VMenu"},{default:()=>{var _;return[(_=n.default)==null?void 0:_.call(n,...y)]}})}})}),Ur({id:a,ΨopenChildren:u},o)}});const ZC=z({active:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...ye(),...Us({transition:{component:$h}})},"VCounter"),YC=se()({name:"VCounter",functional:!0,props:ZC(),setup(e,t){let{slots:n}=t;const r=S(()=>e.max?`${e.value} / ${e.max}`:String(e.value));return ve(()=>v(Pn,{transition:e.transition},{default:()=>[ft(v("div",{class:["v-counter",e.class],style:e.style},[n.default?n.default({counter:r.value,max:e.max,value:e.value}):r.value]),[[$r,e.active]])]})),{}}});const GC=z({floating:Boolean,...ye()},"VFieldLabel"),mi=se()({name:"VFieldLabel",props:GC(),setup(e,t){let{slots:n}=t;return ve(()=>v(ev,{class:["v-field-label",{"v-field-label--floating":e.floating},e.class],style:e.style,"aria-hidden":e.floating||void 0},n)),{}}}),KC=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],wv=z({appendInnerIcon:Ae,bgColor:String,clearable:Boolean,clearIcon:{type:Ae,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},error:Boolean,flat:Boolean,label:String,persistentClear:Boolean,prependInnerIcon:Ae,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>KC.includes(e)},"onClick:clear":Qt(),"onClick:appendInner":Qt(),"onClick:prependInner":Qt(),...ye(),...Il(),...Pt(),...je()},"VField"),Sv=se()({name:"VField",inheritAttrs:!1,props:{id:String,...av(),...wv()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:s}=t;const{themeClasses:i}=Qe(e),{loaderClasses:a}=Al(e),{focusClasses:o,isFocused:l,focus:u,blur:c}=Gl(e),{InputIcon:d}=iv(e),{roundedClasses:f}=Mt(e),{rtlClasses:m}=mr(),h=S(()=>e.dirty||e.active),g=S(()=>!e.singleLine&&!!(e.label||s.label)),y=At(),E=S(()=>e.id||`input-${y}`),_=S(()=>`${E.value}-messages`),w=K(),O=K(),I=K(),k=S(()=>["plain","underlined"].includes(e.variant)),{backgroundColorClasses:C,backgroundColorStyles:p}=Pr(de(e,"bgColor")),{textColorClasses:T,textColorStyles:V}=en(S(()=>e.error||e.disabled?void 0:h.value&&l.value?e.color:e.baseColor));he(h,R=>{if(g.value){const F=w.value.$el,G=O.value.$el;requestAnimationFrame(()=>{const Z=xl(F),ee=G.getBoundingClientRect(),U=ee.x-Z.x,W=ee.y-Z.y-(Z.height/2-ee.height/2),fe=ee.width/.75,oe=Math.abs(fe-Z.width)>1?{maxWidth:ae(fe)}:void 0,Ce=getComputedStyle(F),De=getComputedStyle(G),He=parseFloat(Ce.transitionDuration)*1e3||150,Ft=parseFloat(De.getPropertyValue("--v-field-label-scale")),ge=De.getPropertyValue("color");F.style.visibility="visible",G.style.visibility="hidden",Cr(F,{transform:`translate(${U}px, ${W}px) scale(${Ft})`,color:ge,...oe},{duration:He,easing:Fi,direction:R?"normal":"reverse"}).finished.then(()=>{F.style.removeProperty("visibility"),G.style.removeProperty("visibility")})})}},{flush:"post"});const $=S(()=>({isActive:h,isFocused:l,controlRef:I,blur:c,focus:u}));function P(R){R.target!==document.activeElement&&R.preventDefault()}return ve(()=>{var U,W,fe;const R=e.variant==="outlined",F=s["prepend-inner"]||e.prependInnerIcon,G=!!(e.clearable||s.clear),Z=!!(s["append-inner"]||e.appendInnerIcon||G),ee=s.label?s.label({...$.value,label:e.label,props:{for:E.value}}):e.label;return v("div",ue({class:["v-field",{"v-field--active":h.value,"v-field--appended":Z,"v-field--center-affix":e.centerAffix??!k.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":F,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!ee,[`v-field--variant-${e.variant}`]:!0},i.value,C.value,o.value,a.value,f.value,m.value,e.class],style:[p.value,V.value,e.style],onClick:P},n),[v("div",{class:"v-field__overlay"},null),v(Qf,{name:"v-field",active:!!e.loading,color:e.error?"error":typeof e.loading=="string"?e.loading:e.color},{default:s.loader}),F&&v("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&v(d,{key:"prepend-icon",name:"prependInner"},null),(U=s["prepend-inner"])==null?void 0:U.call(s,$.value)]),v("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&g.value&&v(mi,{key:"floating-label",ref:O,class:[T.value],floating:!0,for:E.value},{default:()=>[ee]}),v(mi,{ref:w,for:E.value},{default:()=>[ee]}),(W=s.default)==null?void 0:W.call(s,{...$.value,props:{id:E.value,class:"v-field__input","aria-describedby":_.value},focus:u,blur:c})]),G&&v(Hh,{key:"clear"},{default:()=>[ft(v("div",{class:"v-field__clearable",onMousedown:oe=>{oe.preventDefault(),oe.stopPropagation()}},[s.clear?s.clear():v(d,{name:"clear"},null)]),[[$r,e.dirty]])]}),Z&&v("div",{key:"append",class:"v-field__append-inner"},[(fe=s["append-inner"])==null?void 0:fe.call(s,$.value),e.appendInnerIcon&&v(d,{key:"append-icon",name:"appendInner"},null)]),v("div",{class:["v-field__outline",T.value]},[R&&v(ke,null,[v("div",{class:"v-field__outline__start"},null),g.value&&v("div",{class:"v-field__outline__notch"},[v(mi,{ref:O,floating:!0,for:E.value},{default:()=>[ee]})]),v("div",{class:"v-field__outline__end"},null)]),k.value&&g.value&&v(mi,{ref:O,floating:!0,for:E.value},{default:()=>[ee]})])])}),{controlRef:I}}});function JC(e){const t=Object.keys(Sv.props).filter(n=>!Cl(n)&&n!=="class"&&n!=="style");return Fs(e,t)}const XC=["color","file","time","date","datetime-local","week","month"],Cv=z({autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,suffix:String,type:{type:String,default:"text"},modelModifiers:Object,...Kl(),...wv()},"VTextField"),_r=se()({name:"VTextField",directives:{Intersect:Dh},inheritAttrs:!1,props:Cv(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:s}=t;const i=Be(e,"modelValue"),{isFocused:a,focus:o,blur:l}=Gl(e),u=S(()=>typeof e.counterValue=="function"?e.counterValue(i.value):(i.value??"").toString().length),c=S(()=>{if(n.maxlength)return n.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter}),d=S(()=>["plain","underlined"].includes(e.variant));function f(k,C){var p,T;!e.autofocus||!k||(T=(p=C[0].target)==null?void 0:p.focus)==null||T.call(p)}const m=K(),h=K(),g=K(),y=S(()=>XC.includes(e.type)||e.persistentPlaceholder||a.value||e.active);function E(){var k;g.value!==document.activeElement&&((k=g.value)==null||k.focus()),a.value||o()}function _(k){r("mousedown:control",k),k.target!==g.value&&(E(),k.preventDefault())}function w(k){E(),r("click:control",k)}function O(k){k.stopPropagation(),E(),ut(()=>{i.value=null,pp(e["onClick:clear"],k)})}function I(k){var p;const C=k.target;if(i.value=C.value,(p=e.modelModifiers)!=null&&p.trim&&["text","search","password","tel","url"].includes(e.type)){const T=[C.selectionStart,C.selectionEnd];ut(()=>{C.selectionStart=T[0],C.selectionEnd=T[1]})}}return ve(()=>{const k=!!(s.counter||e.counter||e.counterValue),C=!!(k||s.details),[p,T]=wl(n),[{modelValue:V,...$}]=qi.filterProps(e),[P]=JC(e);return v(qi,ue({ref:m,modelValue:i.value,"onUpdate:modelValue":R=>i.value=R,class:["v-text-field",{"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-text-field--plain-underlined":["plain","underlined"].includes(e.variant)},e.class],style:e.style},p,$,{centerAffix:!d.value,focused:a.value}),{...s,default:R=>{let{id:F,isDisabled:G,isDirty:Z,isReadonly:ee,isValid:U}=R;return v(Sv,ue({ref:h,onMousedown:_,onClick:w,"onClick:clear":O,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:"textbox"},P,{id:F.value,active:y.value||Z.value,dirty:Z.value||e.dirty,disabled:G.value,focused:a.value,error:U.value===!1}),{...s,default:W=>{let{props:{class:fe,...oe}}=W;const Ce=ft(v("input",ue({ref:g,value:i.value,onInput:I,autofocus:e.autofocus,readonly:ee.value,disabled:G.value,name:e.name,placeholder:e.placeholder,size:1,type:e.type,onFocus:E,onBlur:l},oe,T),null),[[dn("intersect"),{handler:f},null,{once:!0}]]);return v(ke,null,[e.prefix&&v("span",{class:"v-text-field__prefix"},[v("span",{class:"v-text-field__prefix__text"},[e.prefix])]),v("div",{class:fe,"data-no-activator":""},[s.default?v(ke,null,[s.default(),Ce]):fn(Ce)]),e.suffix&&v("span",{class:"v-text-field__suffix"},[v("span",{class:"v-text-field__suffix__text"},[e.suffix])])])}})},details:C?R=>{var F;return v(ke,null,[(F=s.details)==null?void 0:F.call(s,R),k&&v(ke,null,[v("span",null,null),v(YC,{active:e.persistentCounter||a.value,value:u.value,max:c.value},s.counter)])])}:void 0})}),Ur({},m,h,g)}});const QC=z({renderless:Boolean,...ye()},"VVirtualScrollItem"),ex=se()({name:"VVirtualScrollItem",inheritAttrs:!1,props:QC(),emits:{"update:height":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:s}=t;const{resizeRef:i,contentRect:a}=la(void 0,"border");he(()=>{var o;return(o=a.value)==null?void 0:o.height},o=>{o!=null&&r("update:height",o)}),ve(()=>{var o,l;return e.renderless?v(ke,null,[(o=s.default)==null?void 0:o.call(s,{itemRef:i})]):v("div",ue({ref:i,class:["v-virtual-scroll__item",e.class],style:e.style},n),[(l=s.default)==null?void 0:l.call(s)])})}}),ld=-1,ud=1,tx=z({itemHeight:{type:[Number,String],default:48}},"virtual");function nx(e,t,n){const r=be(0),s=be(e.itemHeight),i=S({get:()=>parseInt(s.value??0,10),set(C){s.value=C}}),a=K(),{resizeRef:o,contentRect:l}=la();gn(()=>{o.value=a.value});const u=cm(),c=new Map;let d=Array.from({length:t.value.length});const f=S(()=>{const C=(!l.value||a.value===document.documentElement?u.height.value:l.value.height)-((n==null?void 0:n.value)??0);return Math.ceil(C/i.value*1.7+1)});function m(C,p){i.value=Math.max(i.value,p),d[C]=p,c.set(t.value[C],p)}function h(C){return d.slice(0,C).reduce((p,T)=>p+(T||i.value),0)}function g(C){const p=t.value.length;let T=0,V=0;for(;V<C&&T<p;)V+=d[T++]||i.value;return T-1}let y=0;function E(){if(!a.value||!l.value)return;const C=l.value.height-56,p=a.value.scrollTop,T=p<y?ld:ud,V=g(p+C/2),$=Math.round(f.value/3),P=V-$,R=r.value+$*2-1;T===ld&&V<=R?r.value=_s(P,0,t.value.length):T===ud&&V>=R&&(r.value=_s(P,0,t.value.length-f.value)),y=p}function _(C){if(!a.value)return;const p=h(C);a.value.scrollTop=p}const w=S(()=>Math.min(t.value.length,r.value+f.value)),O=S(()=>t.value.slice(r.value,w.value).map((C,p)=>({raw:C,index:p+r.value}))),I=S(()=>h(r.value)),k=S(()=>h(t.value.length)-h(w.value));return he(()=>t.value.length,()=>{d=bl(t.value.length).map(()=>i.value),c.forEach((C,p)=>{const T=t.value.indexOf(p);T===-1?c.delete(p):d[T]=C})}),{containerRef:a,computedItems:O,itemHeight:i,paddingTop:I,paddingBottom:k,scrollToIndex:_,handleScroll:E,handleItemResize:m}}const rx=z({items:{type:Array,default:()=>[]},renderless:Boolean,...tx(),...ye(),...zn()},"VVirtualScroll"),sx=se()({name:"VVirtualScroll",props:rx(),setup(e,t){let{slots:n}=t;const r=it("VVirtualScroll"),{dimensionStyles:s}=jn(e),{containerRef:i,handleScroll:a,handleItemResize:o,scrollToIndex:l,paddingTop:u,paddingBottom:c,computedItems:d}=nx(e,de(e,"items"));return Ln(()=>e.renderless,()=>{pn(()=>{var f;i.value=$f(r.vnode.el,!0),(f=i.value)==null||f.addEventListener("scroll",a)}),lt(()=>{var f;(f=i.value)==null||f.removeEventListener("scroll",a)})}),ve(()=>{const f=d.value.map(m=>v(ex,{key:m.index,renderless:e.renderless,"onUpdate:height":h=>o(m.index,h)},{default:h=>{var g;return(g=n.default)==null?void 0:g.call(n,{item:m.raw,index:m.index,...h})}}));return e.renderless?v(ke,null,[v("div",{class:"v-virtual-scroll__spacer",style:{paddingTop:ae(u.value)}},null),f,v("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:ae(c.value)}},null)]):v("div",{ref:i,class:["v-virtual-scroll",e.class],onScroll:a,style:[s.value,e.style]},[v("div",{class:"v-virtual-scroll__container",style:{paddingTop:ae(u.value),paddingBottom:ae(c.value)}},[f])])}),{scrollToIndex:l}}});function ix(e,t){const n=be(!1);let r;function s(o){cancelAnimationFrame(r),n.value=!0,r=requestAnimationFrame(()=>{r=requestAnimationFrame(()=>{n.value=!1})})}async function i(){await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>{if(n.value){const l=he(n,()=>{l(),o()})}else o()})}async function a(o){var c,d;if(o.key==="Tab"&&((c=t.value)==null||c.focus()),!["PageDown","PageUp","Home","End"].includes(o.key))return;const l=(d=e.value)==null?void 0:d.$el;if(!l)return;(o.key==="Home"||o.key==="End")&&l.scrollTo({top:o.key==="Home"?0:l.scrollHeight,behavior:"smooth"}),await i();const u=l.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)");if(o.key==="PageDown"||o.key==="Home"){const f=l.getBoundingClientRect().top;for(const m of u)if(m.getBoundingClientRect().top>=f){m.focus();break}}else{const f=l.getBoundingClientRect().bottom;for(const m of[...u].reverse())if(m.getBoundingClientRect().bottom<=f){m.focus();break}}}return{onListScroll:s,onListKeydown:a}}const ax=z({chips:Boolean,closableChips:Boolean,eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,menu:Boolean,menuIcon:{type:Ae,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,valueComparator:{type:Function,default:Br},...gv({itemChildren:!1})},"Select"),ox=z({...ax(),...Ds(Cv({modelValue:null}),["validationValue","dirty","appendInnerIcon"]),...Us({transition:{component:ql}})},"VSelect"),cd=se()({name:"VSelect",props:ox(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,t){let{slots:n}=t;const{t:r}=ua(),s=K(),i=K(),a=Be(e,"menu"),o=S({get:()=>a.value,set:F=>{var G;a.value&&!F&&((G=i.value)!=null&&G.ΨopenChildren)||(a.value=F)}}),{items:l,transformIn:u,transformOut:c}=RC(e),d=Be(e,"modelValue",[],F=>u(F===null?[null]:Dn(F)),F=>{const G=c(F);return e.multiple?G:G[0]??null}),f=ov(),m=S(()=>d.value.map(F=>l.value.find(G=>e.valueComparator(G.value,F.value))||F)),h=S(()=>m.value.map(F=>F.props.value)),g=be(!1);let y="",E;const _=S(()=>e.hideSelected?l.value.filter(F=>!m.value.some(G=>G===F)):l.value),w=S(()=>e.hideNoData&&!l.value.length||e.readonly||(f==null?void 0:f.isReadonly.value)),O=K(),{onListScroll:I,onListKeydown:k}=ix(O,s);function C(F){e.openOnClear&&(o.value=!0)}function p(){w.value||(o.value=!o.value)}function T(F){var W,fe;if(e.readonly||f!=null&&f.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(F.key)&&F.preventDefault(),["Enter","ArrowDown"," "].includes(F.key)&&(o.value=!0),["Escape","Tab"].includes(F.key)&&(o.value=!1),F.key==="Home"?(W=O.value)==null||W.focus("first"):F.key==="End"&&((fe=O.value)==null||fe.focus("last"));const G=1e3;function Z(oe){const Ce=oe.key.length===1,De=!oe.ctrlKey&&!oe.metaKey&&!oe.altKey;return Ce&&De}if(e.multiple||!Z(F))return;const ee=performance.now();ee-E>G&&(y=""),y+=F.key.toLowerCase(),E=ee;const U=l.value.find(oe=>oe.title.toLowerCase().startsWith(y));U!==void 0&&(d.value=[U])}function V(F){if(e.multiple){const G=h.value.findIndex(Z=>e.valueComparator(Z,F.value));if(G===-1)d.value=[...d.value,F];else{const Z=[...d.value];Z.splice(G,1),d.value=Z}}else d.value=[F],o.value=!1}function $(F){var G;(G=O.value)!=null&&G.$el.contains(F.relatedTarget)||(o.value=!1)}function P(){var F;g.value&&((F=s.value)==null||F.focus())}function R(F){g.value=!0}return ve(()=>{const F=!!(e.chips||n.chip),G=!!(!e.hideNoData||_.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),Z=d.value.length>0,[ee]=_r.filterProps(e),U=Z||!g.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return v(_r,ue({ref:s},ee,{modelValue:d.value.map(W=>W.props.value).join(", "),"onUpdate:modelValue":W=>{W==null&&(d.value=[])},focused:g.value,"onUpdate:focused":W=>g.value=W,validationValue:d.externalValue,dirty:Z,class:["v-select",{"v-select--active-menu":o.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":d.value.length,"v-select--selection-slot":!!n.selection},e.class],style:e.style,readonly:!0,placeholder:U,"onClick:clear":C,"onMousedown:control":p,onBlur:$,onKeydown:T}),{...n,default:()=>v(ke,null,[v(qC,ue({ref:i,modelValue:o.value,"onUpdate:modelValue":W=>o.value=W,activator:"parent",contentClass:"v-select__content",disabled:w.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterLeave:P},e.menuProps),{default:()=>[G&&v(jC,{ref:O,selected:h.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:W=>W.preventDefault(),onKeydown:k,onFocusin:R,onScrollPassive:I,tabindex:"-1"},{default:()=>{var W,fe,oe;return[(W=n["prepend-item"])==null?void 0:W.call(n),!_.value.length&&!e.hideNoData&&(((fe=n["no-data"])==null?void 0:fe.call(n))??v(Zi,{title:r(e.noDataText)},null)),v(sx,{renderless:!0,items:_.value},{default:Ce=>{var Dt;let{item:De,index:He,itemRef:Ft}=Ce;const ge=ue(De.props,{ref:Ft,key:He,onClick:()=>V(De)});return((Dt=n.item)==null?void 0:Dt.call(n,{item:De,index:He,props:ge}))??v(Zi,ge,{prepend:Ks=>{let{isSelected:Sn}=Ks;return v(ke,null,[e.multiple&&!e.hideSelected?v(zo,{key:De.value,modelValue:Sn,ripple:!1,tabindex:"-1"},null):void 0,De.props.prependIcon&&v(et,{icon:De.props.prependIcon},null)])}})}}),(oe=n["append-item"])==null?void 0:oe.call(n)]}})]}),m.value.map((W,fe)=>{var De;function oe(He){He.stopPropagation(),He.preventDefault(),V(W)}const Ce={"onClick:close":oe,onMousedown(He){He.preventDefault(),He.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0};return v("div",{key:W.value,class:"v-select__selection"},[F?n.chip?v(Le,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:W.title}}},{default:()=>{var He;return[(He=n.chip)==null?void 0:He.call(n,{item:W,index:fe,props:Ce})]}}):v(pC,ue({key:"chip",closable:e.closableChips,size:"small",text:W.title},Ce),null):((De=n.selection)==null?void 0:De.call(n,{item:W,index:fe}))??v("span",{class:"v-select__selection-text"},[W.title,e.multiple&&fe<m.value.length-1&&v("span",{class:"v-select__selection-comma"},[Ze(",")])])])})]),"append-inner":function(){var Ce;for(var W=arguments.length,fe=new Array(W),oe=0;oe<W;oe++)fe[oe]=arguments[oe];return v(ke,null,[(Ce=n["append-inner"])==null?void 0:Ce.call(n,...fe),e.menuIcon?v(et,{class:"v-select__menu-icon",icon:e.menuIcon},null):void 0])}})}),Ur({isFocused:g,menu:o,select:V},s)}});const lx=z({fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,...ye(),...Vt(),...Xe(),...je()},"VTable"),xv=se()({name:"VTable",props:lx(),setup(e,t){let{slots:n}=t;const{themeClasses:r}=Qe(e),{densityClasses:s}=qt(e);return ve(()=>v(e.tag,{class:["v-table",{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!n.top,"v-table--has-bottom":!!n.bottom,"v-table--hover":e.hover},r.value,s.value,e.class],style:e.style},{default:()=>{var i,a,o;return[(i=n.top)==null?void 0:i.call(n),n.default?v("div",{class:"v-table__wrapper",style:{height:ae(e.height)}},[v("table",null,[n.default()])]):(a=n.wrapper)==null?void 0:a.call(n),(o=n.bottom)==null?void 0:o.call(n)]}})),{}}});const ux=z({id:String,text:String,...Ds(zs({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:!1}),["absolute","persistent"])},"VTooltip"),hs=se()({name:"VTooltip",props:ux(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Be(e,"modelValue"),{scopeId:s}=Hs(),i=At(),a=S(()=>e.id||`v-tooltip-${i}`),o=K(),l=S(()=>e.location.split(" ").length>1?e.location:e.location+" center"),u=S(()=>e.origin==="auto"||e.origin==="overlap"||e.origin.split(" ").length>1||e.location.split(" ").length>1?e.origin:e.origin+" center"),c=S(()=>e.transition?e.transition:r.value?"scale-transition":"fade-transition"),d=S(()=>ue({"aria-describedby":a.value},e.activatorProps));return ve(()=>{const[f]=Rn.filterProps(e);return v(Rn,ue({ref:o,class:["v-tooltip",e.class],style:e.style,id:a.value},f,{modelValue:r.value,"onUpdate:modelValue":m=>r.value=m,transition:c.value,absolute:!0,location:l.value,origin:u.value,persistent:!0,role:"tooltip",activatorProps:d.value,_disableGlobalStack:!0},s),{activator:n.activator,default:function(){var y;for(var m=arguments.length,h=new Array(m),g=0;g<m;g++)h[g]=arguments[g];return((y=n.default)==null?void 0:y.call(n,...h))??e.text}})}),Ur({},o)}}),cx={class:"p-3"},dx=J("thead",null,[J("tr",{class:"custom-thead custom-font-size"},[J("th",{class:"text-left"},"-"),J("th",{class:"text-left"},"N° BHE"),J("th",{class:"text-left"},"N° NC"),J("th",{class:"text-left"},"N° OT"),J("th",{class:"text-left"},"FECHA"),J("th",{class:"text-left"},"GLOSA"),J("th",{class:"text-left"},"HONORARIOS"),J("th",{class:"text-left"},"RET 2° CAT."),J("th",{class:"text-left"},"TOTAL"),J("th",{class:"text-left"},"USUARIO"),J("th",{class:"text-left"},"CLIENTE"),J("th",{class:"text-left"},"RUT"),J("th",{class:"text-left"},"F. PAGO"),J("th",{class:"text-left"},"#")])],-1),fx={key:0,style:{"min-width":"250px"}},mx=J("br",null,null,-1),hx=J("br",null,null,-1),vx={key:1},gx={__name:"Reporte",setup(e){const{success:t,error:n,warning:r,hide:s}=pl(),i=K(null),a=K([]),o=K([]),l=K([]),u=K(null),c=K(!1),d=nt({fecha_i:Q.now().toFormat("yyyy-MM-dd"),fecha_f:Q.now().toFormat("yyyy-MM-dd"),num_bhe:null,rut_receptor:null,usuario_search:null,cliente:null,anuladas:!1,pendiente_pago:!1,ot:null,nc:null,pdf:!0}),f=async()=>{c.value=!0;const k={accion:"reporteBHE",form:{...d,fecha_i:O(d.fecha_i,"back"),fecha_f:O(d.fecha_f,"back")}};var C=await Jt.post("/caja_v2/api/",k).catch(T=>!1);if(C){var p=C.data;console.log(p),p.estado?(u.value=p.pdf_reporte,a.value=p.data):(a.value=[],r({msg:p.mensaje}))}else r({msg:"Ocurrio un error, Intentelo nuevamente"});c.value=!1},m=async()=>{const I={accion:"ListClientes",form:d.value};var k=await Jt.post("/caja_v2/api/",I).catch(p=>!1);if(k){var C=k.data;C.estado?o.value=C.data:r({msg:C.mensaje})}else r({msg:"Ocurrio un error, Intentelo nuevamente"})},h=async()=>{const I={accion:"ListUsuarios"};var k=await Jt.post("/caja_v2/api/",I).catch(p=>!1);if(k){var C=k.data;C.estado?l.value=C.data:r({msg:C.mensaje})}else r({msg:"Ocurrio un error, Intentelo nuevamente"})},g=async I=>{try{if(await i.value.show({title:"Eliminar BHE",message:"Estás seguro de eliminar definitivamente esta Boleta de Honorarios (BHE) ?",okButton:"Eliminar"})){const T={accion:"anularBHE",boleta:I};var k=await Jt.post("/caja_v2/api/",T).catch(V=>!1);if(k){var C=k.data;C.estado?t({msg:C.mensaje}):r({msg:C.mensaje}),f()}else r({msg:"Ocurrio un error, Intentelo nuevamente"})}}catch(p){console.error("Error:",p)}},y=async I=>{const k={accion:"traeBHE",bhe:I};var C=await Jt.post("/ot/api/",k).catch(T=>!1);if(C){var p=C.data;p.estado?_(p.data.pdf64):r({msg:p.mensaje})}else r({msg:"Ocurrio un error, Intentelo nuevamente"})},E=()=>{const I=u.value;_(I)},_=I=>{var k=window.open();k.document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64,"+I+"'></iframe>"),k.document.close()},w=()=>{d.fecha_i=Q.now().toFormat("yyyy-MM-dd"),d.fecha_f=Q.now().toFormat("yyyy-MM-dd"),d.num_bhe=null,d.rut_receptor=null,d.usuario_search=null,d.cliente=null,d.anuladas=!1,d.pendiente_pago=!1,d.ot=null,d.nc=null};console.log("hoy =>",Q.now().toFormat("yyyy-MM-dd"));const O=(I,k)=>{if(k=="back")var C=Q.fromFormat(I,"yyyy-MM-dd").toFormat("dd-MM-yyyy");if(k=="front")var C=Q.fromFormat(I,"yyyy-MM-dd").toFormat("yyyy-MM-dd");return C??null};return m(),h(),(I,k)=>(Ye(),gt(Wi,{class:"fill-height"},{default:re(()=>[v(ka,{class:"mx-auto pa-3",flat:""},{default:re(()=>[v(xr,null,{default:re(()=>[v($t,{cols:"12"},{default:re(()=>[v(xr,null,{default:re(()=>[v($t,{cols:"12",md:"2"},{default:re(()=>[v(_r,{modelValue:d.fecha_i,"onUpdate:modelValue":k[0]||(k[0]=C=>d.fecha_i=C),"hide-details":"",variant:"outlined",label:"DESDE",required:"",type:"date",class:"custom-field-input"},null,8,["modelValue"])]),_:1}),v($t,{cols:"12",md:"2"},{default:re(()=>[v(_r,{modelValue:d.fecha_f,"onUpdate:modelValue":k[1]||(k[1]=C=>d.fecha_f=C),label:"HASTA","hide-details":"",variant:"outlined",required:"",type:"date"},null,8,["modelValue"])]),_:1}),v($t,{cols:"12",md:"2"},{default:re(()=>[v(_r,{modelValue:d.num_bhe,"onUpdate:modelValue":k[2]||(k[2]=C=>d.num_bhe=C),label:"N° BHE","hide-details":"",variant:"outlined",required:""},null,8,["modelValue"])]),_:1}),v($t,{cols:"12",md:"2"},{default:re(()=>[v(_r,{modelValue:d.rut_receptor,"onUpdate:modelValue":k[3]||(k[3]=C=>d.rut_receptor=C),onInput:k[4]||(k[4]=C=>d.rut_receptor=tt(P1)(C.target.value)),label:"RUT","hide-details":"",variant:"outlined",required:""},null,8,["modelValue"])]),_:1}),v($t,{cols:"12",md:"2"},{default:re(()=>[v(cd,{modelValue:d.usuario_search,"onUpdate:modelValue":k[5]||(k[5]=C=>d.usuario_search=C),label:"USUARIO","hide-details":"",variant:"outlined","item-value":"nombre","item-title":"nombre",items:l.value},null,8,["modelValue","items"])]),_:1}),v($t,{cols:"12",md:"2"},{default:re(()=>[v(cd,{modelValue:d.cliente,"onUpdate:modelValue":k[6]||(k[6]=C=>d.cliente=C),label:"CLIENTE","hide-details":"",variant:"outlined","item-value":"nombre","item-title":"nombre",items:o.value},null,8,["modelValue","items"])]),_:1})]),_:1}),v(xr,{class:"d-flex justify-end mr-2"},{default:re(()=>[v($t,{cols:"auto py-0"},{default:re(()=>[v(ad,{label:"ANULADAS",class:"mt-n1 pt-n1",modelValue:d.anuladas,"onUpdate:modelValue":k[7]||(k[7]=C=>d.anuladas=C),density:"compact","hide-details":""},null,8,["modelValue"])]),_:1}),v($t,{cols:"auto py-0"},{default:re(()=>[v(ad,{label:"PENDIENTE PAGO",class:"mt-n1 pt-n1",modelValue:d.pendiente_pago,"onUpdate:modelValue":k[8]||(k[8]=C=>d.pendiente_pago=C),density:"compact","hide-details":""},null,8,["modelValue"])]),_:1}),v(hs,{text:"Limpiar filtros",location:"top"},{activator:re(({props:C})=>[v(pt,ue({size:"small","prepend-icon":"mdi-delete",variant:"text",color:"primary",onClick:k[9]||(k[9]=p=>w())},C,{class:"mx-2"}),{default:re(()=>[Ze("limpiar filtros")]),_:2},1040)]),_:1}),v(pt,{size:"small","prepend-icon":"mdi-magnify",color:"primary",onClick:k[10]||(k[10]=C=>f()),class:"mx-2",loading:c.value},{default:re(()=>[Ze("Buscar")]),_:1},8,["loading"])]),_:1}),v(xr,null,{default:re(()=>[v(iC,{type:"success",class:"mx-3",closable:"",title:"Importante",density:"compact",text:"Por disposición del Servicio de Impuestos Internos (SII), la anulación de boletas de honorarios electrónicas (BHE) debe ser realziada desde su portal en https://www.sii.cl "})]),_:1})]),_:1}),v($t,{cols:"12"},{default:re(()=>[v(pt,{"prepend-icon":"mdi-download",disabled:!u.value,size:"small",onClick:E,class:"my-2"},{default:re(()=>[Ze("descargar pdf")]),_:1},8,["disabled"]),J("h5",cx,Se(a.value.length??0)+" registros encontrados",1),v(xv,{density:"compact"},{default:re(()=>[dx,J("tbody",null,[(Ye(!0),tr(ke,null,rf(a.value,(C,p)=>(Ye(),tr("tr",{key:C.id,class:"custom-font-size"},[J("td",null,[v(hs,{text:"Ver BHE",location:"top"},{activator:re(({props:T})=>[v(pt,ue({size:"",variant:"text",color:"error",onClick:V=>y(C.boleta)},T),{default:re(()=>[v(et,null,{default:re(()=>[Ze("mdi-file-pdf-box")]),_:1})]),_:2},1040,["onClick"])]),_:2},1024)]),C.anulada=="1"?(Ye(),tr("td",fx,[J("div",null,[Ze(" Nro Boleta:"+Se(C.boleta),1),mx,Ze(" Fecha anulación: "+Se(I.$filters.fecha(C.fecha_anulacion))+" ",1),hx,Ze(" Usuario anulación: "+Se(C.usuario_anulacion),1)])])):(Ye(),tr("td",vx,Se(C.boleta),1)),J("td",null,Se(C.nc),1),J("td",null,Se(C.ot),1),J("td",null,Se(C.fecha),1),J("td",null,Se(C.glosa),1),J("td",null,Se(I.$filters.moneda(C.total)),1),J("td",null,Se(C.id),1),J("td",null,Se(I.$filters.moneda(C.total)),1),J("td",null,Se(C.usuario),1),J("td",null,Se(C.nombre_cliente),1),J("td",null,Se(C.rut_receptor),1),J("td",null,Se(C.forma_pago),1),J("td",null,[C.anulada!="1"?(Ye(),gt(hs,{key:0,text:"Eliminar BHE",location:"top"},{activator:re(({props:T})=>[v(pt,ue({size:"x-small",color:"error",icon:"mdi-delete-outline",onClick:V=>g(C.boleta)},T),null,16,["onClick"])]),_:2},1024)):nr("",!0)])]))),128))])]),_:1})]),_:1})]),_:1}),v(Qh,{ref_key:"confirmDialogueRef",ref:i},null,512)]),_:1})]),_:1}))}};const yx=J("thead",null,[J("tr",{class:"custom-thead custom-font-size"},[J("th",{class:"text-left"},"-"),J("th",{class:"text-left"},"N° BHE"),J("th",{class:"text-left"},"N° NC"),J("th",{class:"text-left"},"N° OT"),J("th",{class:"text-left"},"FECHA"),J("th",{class:"text-left"},"GLOSA"),J("th",{class:"text-left"},"HONORARIOS"),J("th",{class:"text-left"},"RET 2° CAT."),J("th",{class:"text-left"},"TOTAL"),J("th",{class:"text-left"},"USUARIO"),J("th",{class:"text-left"},"CLIENTE"),J("th",{class:"text-left"},"RUT"),J("th",{class:"text-left"},"#")])],-1),px={__name:"ReporteDiario",setup(e){const{success:t,error:n,warning:r,hide:s}=pl(),i=K(null),a=K([]);K([]),K([]),K(null);const o=K(!1),l=K(0),u=K(0),c=nt({fecha_i:Q.now().toFormat("yyyy-MM-dd"),fecha_f:Q.now().toFormat("yyyy-MM-dd"),num_bhe:null,rut_receptor:null,usuario_search:null,cliente:null,anuladas:!1,ot:null,nc:null,pdf:!1}),d=async()=>{o.value=!0;const E={accion:"reporteBHE",form:{...c,fecha_i:g(c.fecha_i,"back"),fecha_f:g(c.fecha_f,"back")}};var _=await Jt.post("/caja_v2/api/",E).catch(O=>!1);if(_){var w=_.data;if(console.log(w),w.estado){const O=w.data.reduce((I,k)=>I+parseInt(k.total),0);l.value=O,u.value=w.data.length,a.value=w.data}else a.value=[],r({msg:w.mensaje})}else r({msg:"Ocurrio un error, Intentelo nuevamente"});o.value=!1},f=async y=>{try{if(await i.value.show({title:"Eliminar BHE",message:"Estás seguro de eliminar definitivamente esta Boleta de Honorarios (BHE) ?",okButton:"Eliminar"})){const O={accion:"anularBHE",boleta:y};var E=await Jt.post("/caja_v2/api/",O).catch(I=>!1);if(E){var _=E.data;_.estado?t({msg:_.mensaje}):r({msg:_.mensaje}),d()}else r({msg:"Ocurrio un error, Intentelo nuevamente"})}}catch(w){console.error("Error:",w)}},m=async y=>{const E={accion:"traeBHE",bhe:y};var _=await Jt.post("/ot/api/",E).catch(O=>!1);if(_){var w=_.data;w.estado?h(w.data.pdf64):r({msg:w.mensaje})}else r({msg:"Ocurrio un error, Intentelo nuevamente"})},h=y=>{var E=window.open();E.document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64,"+y+"'></iframe>"),E.document.close()},g=(y,E)=>{if(E=="back")var _=Q.fromFormat(y,"yyyy-MM-dd").toFormat("dd-MM-yyyy");if(E=="front")var _=Q.fromFormat(y,"yyyy-MM-dd").toFormat("yyyy-MM-dd");return _??null};return setInterval(()=>{d()},3e4),d(),(y,E)=>(Ye(),gt(Wi,{class:"fill-height"},{default:re(()=>[v(ka,{class:"mx-auto pa-3",flat:""},{default:re(()=>[v(xr,null,{default:re(()=>[v($t,{cols:"12"},{default:re(()=>[Ze(" Total Registros :"+Se(u.value)+" - Total Valor: $"+Se(y.$filters.moneda(l.value))+" ",1),v(xv,{density:"compact"},{default:re(()=>[yx,J("tbody",null,[(Ye(!0),tr(ke,null,rf(a.value,(_,w)=>(Ye(),tr("tr",{key:_.id,class:"custom-font-size"},[J("td",null,[v(hs,{text:"Ver BHE",location:"top"},{activator:re(({props:O})=>[v(pt,ue({size:"",variant:"text",color:"error",onClick:I=>m(_.boleta)},O),{default:re(()=>[v(et,null,{default:re(()=>[Ze("mdi-file-pdf-box")]),_:1})]),_:2},1040,["onClick"])]),_:2},1024)]),J("td",null,Se(_.boleta),1),J("td",null,Se(_.nc),1),J("td",null,Se(_.ot),1),J("td",null,Se(_.fecha),1),J("td",null,Se(_.glosa),1),J("td",null,Se(y.$filters.moneda(_.total)),1),J("td",null,Se(_.id),1),J("td",null,Se(y.$filters.moneda(_.total)),1),J("td",null,Se(_.usuario),1),J("td",null,Se(_.nombre_cliente),1),J("td",null,Se(_.rut_receptor),1),J("td",null,[v(hs,{text:"Eliminar BHE",location:"top"},{activator:re(({props:O})=>[v(pt,ue({size:"x-small",color:"error",icon:"mdi-delete-outline",onClick:I=>f(_.boleta)},O),null,16,["onClick"])]),_:2},1024)])]))),128))])]),_:1})]),_:1})]),_:1}),v(Qh,{ref_key:"confirmDialogueRef",ref:i},null,512)]),_:1})]),_:1}))}};const bx=z({text:String,...ye(),...Xe()},"VToolbarTitle"),_v=se()({name:"VToolbarTitle",props:bx(),setup(e,t){let{slots:n}=t;return ve(()=>{const r=!!(n.default||n.text||e.text);return v(e.tag,{class:["v-toolbar-title",e.class],style:e.style},{default:()=>{var s;return[r&&v("div",{class:"v-toolbar-title__placeholder"},[n.text?n.text():e.text,(s=n.default)==null?void 0:s.call(n)])]}})}),{}}}),wx=[null,"prominent","default","comfortable","compact"],Sx=z({absolute:Boolean,collapse:Boolean,color:String,density:{type:String,default:"default",validator:e=>wx.includes(e)},extended:Boolean,extensionHeight:{type:[Number,String],default:48},flat:Boolean,floating:Boolean,height:{type:[Number,String],default:64},image:String,title:String,...ur(),...ye(),...Hn(),...Pt(),...Xe({tag:"header"}),...je()},"VToolbar"),Cx=se()({name:"VToolbar",props:Sx(),setup(e,t){var m;let{slots:n}=t;const{backgroundColorClasses:r,backgroundColorStyles:s}=Pr(de(e,"color")),{borderClasses:i}=cr(e),{elevationClasses:a}=Un(e),{roundedClasses:o}=Mt(e),{themeClasses:l}=Qe(e),{rtlClasses:u}=mr(),c=be(!!(e.extended||(m=n.extension)!=null&&m.call(n))),d=S(()=>parseInt(Number(e.height)+(e.density==="prominent"?Number(e.height):0)-(e.density==="comfortable"?8:0)-(e.density==="compact"?16:0),10)),f=S(()=>c.value?parseInt(Number(e.extensionHeight)+(e.density==="prominent"?Number(e.extensionHeight):0)-(e.density==="comfortable"?4:0)-(e.density==="compact"?8:0),10):0);return Bn({VBtn:{variant:"text"}}),ve(()=>{var E;const h=!!(e.title||n.title),g=!!(n.image||e.image),y=(E=n.extension)==null?void 0:E.call(n);return c.value=!!(e.extended||y),v(e.tag,{class:["v-toolbar",{"v-toolbar--absolute":e.absolute,"v-toolbar--collapse":e.collapse,"v-toolbar--flat":e.flat,"v-toolbar--floating":e.floating,[`v-toolbar--density-${e.density}`]:!0},r.value,i.value,a.value,o.value,l.value,u.value,e.class],style:[s.value,e.style]},{default:()=>[g&&v("div",{key:"image",class:"v-toolbar__image"},[n.image?v(Le,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},n.image):v(Wl,{key:"image-img",cover:!0,src:e.image},null)]),v(Le,{defaults:{VTabs:{height:ae(d.value)}}},{default:()=>{var _,w,O;return[v("div",{class:"v-toolbar__content",style:{height:ae(d.value)}},[n.prepend&&v("div",{class:"v-toolbar__prepend"},[(_=n.prepend)==null?void 0:_.call(n)]),h&&v(_v,{key:"title",text:e.title},{text:n.title}),(w=n.default)==null?void 0:w.call(n),n.append&&v("div",{class:"v-toolbar__append"},[(O=n.append)==null?void 0:O.call(n)])])]}}),v(Le,{defaults:{VTabs:{height:ae(f.value)}}},{default:()=>[v(Bh,null,{default:()=>[c.value&&v("div",{class:"v-toolbar__extension",style:{height:ae(f.value)}},[y])]})]})]})}),{contentHeight:d,extensionHeight:f}}}),xx={__name:"Layout",setup(e){const t=K("reporte"),n=r=>{t.value=r};return(r,s)=>(Ye(),gt(ka,{color:"grey-lighten-4",flat:"",rounded:"0"},{default:re(()=>[v(Cx,{density:"compact"},{default:re(()=>[v(_v,null,{default:re(()=>[Ze("Reportes de Caja")]),_:1}),v(Xh),t.value=="reporte"?(Ye(),gt(pt,{key:0,"prepend-icon":"mdi-file-chart-outline",color:"primary",variant:"text",onClick:s[0]||(s[0]=i=>n("reporte_diario"))},{default:re(()=>[Ze("Informe diario BHE ")]),_:1})):nr("",!0),t.value=="reporte_diario"?(Ye(),gt(pt,{key:1,"prepend-icon":"mdi-file-chart-outline",color:"primary",variant:"text",onClick:s[1]||(s[1]=i=>n("reporte"))},{default:re(()=>[Ze("Consultar BHE emitidas ")]),_:1})):nr("",!0)]),_:1}),t.value=="reporte"?(Ye(),gt(gx,{key:0})):nr("",!0),t.value=="reporte_diario"?(Ye(),gt(px,{key:1})):nr("",!0)]),_:1}))}};const dd=Symbol.for("vuetify:layout"),_x=Symbol.for("vuetify:layout-item"),fd=1e3,Ex=z({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},"layout"),Tx=(e,t,n,r)=>{let s={top:0,left:0,right:0,bottom:0};const i=[{id:"",layer:{...s}}];for(const a of e){const o=t.get(a),l=n.get(a),u=r.get(a);if(!o||!l||!u)continue;const c={...s,[o.value]:parseInt(s[o.value],10)+(u.value?parseInt(l.value,10):0)};i.push({id:a,layer:c}),s=c}return i};function kx(e){const t=Re(dd,null),n=S(()=>t?t.rootZIndex.value-100:fd),r=K([]),s=nt(new Map),i=nt(new Map),a=nt(new Map),o=nt(new Map),l=nt(new Map),{resizeRef:u,contentRect:c}=la(),d=S(()=>{const k=new Map,C=e.overlaps??[];for(const p of C.filter(T=>T.includes(":"))){const[T,V]=p.split(":");if(!r.value.includes(T)||!r.value.includes(V))continue;const $=s.get(T),P=s.get(V),R=i.get(T),F=i.get(V);!$||!P||!R||!F||(k.set(V,{position:$.value,amount:parseInt(R.value,10)}),k.set(T,{position:P.value,amount:-parseInt(F.value,10)}))}return k}),f=S(()=>{const k=[...new Set([...a.values()].map(p=>p.value))].sort((p,T)=>p-T),C=[];for(const p of k){const T=r.value.filter(V=>{var $;return(($=a.get(V))==null?void 0:$.value)===p});C.push(...T)}return Tx(C,s,i,o)}),m=S(()=>!Array.from(l.values()).some(k=>k.value)),h=S(()=>f.value[f.value.length-1].layer),g=S(()=>({"--v-layout-left":ae(h.value.left),"--v-layout-right":ae(h.value.right),"--v-layout-top":ae(h.value.top),"--v-layout-bottom":ae(h.value.bottom),...m.value?void 0:{transition:"none"}})),y=S(()=>f.value.slice(1).map((k,C)=>{let{id:p}=k;const{layer:T}=f.value[C],V=i.get(p),$=s.get(p);return{id:p,...T,size:Number(V.value),position:$.value}})),E=k=>y.value.find(C=>C.id===k),_=it("createLayout"),w=be(!1);pn(()=>{w.value=!0}),wt(dd,{register:(k,C)=>{let{id:p,order:T,position:V,layoutSize:$,elementSize:P,active:R,disableTransitions:F,absolute:G}=C;a.set(p,T),s.set(p,V),i.set(p,$),o.set(p,R),F&&l.set(p,F);const ee=ds(_x,_==null?void 0:_.vnode).indexOf(k);ee>-1?r.value.splice(ee,0,p):r.value.push(p);const U=S(()=>y.value.findIndex(Ce=>Ce.id===p)),W=S(()=>n.value+f.value.length*2-U.value*2),fe=S(()=>{const Ce=V.value==="left"||V.value==="right",De=V.value==="right",He=V.value==="bottom",Ft={[V.value]:0,zIndex:W.value,transform:`translate${Ce?"X":"Y"}(${(R.value?0:-110)*(De||He?-1:1)}%)`,position:G.value||n.value!==fd?"absolute":"fixed",...m.value?void 0:{transition:"none"}};if(!w.value)return Ft;const ge=y.value[U.value];if(!ge)throw new Error(`[Vuetify] Could not find layout item "${p}"`);const Dt=d.value.get(p);return Dt&&(ge[Dt.position]+=Dt.amount),{...Ft,height:Ce?`calc(100% - ${ge.top}px - ${ge.bottom}px)`:P.value?`${P.value}px`:void 0,left:De?void 0:`${ge.left}px`,right:De?`${ge.right}px`:void 0,top:V.value!=="bottom"?`${ge.top}px`:void 0,bottom:V.value!=="top"?`${ge.bottom}px`:void 0,width:Ce?P.value?`${P.value}px`:void 0:`calc(100% - ${ge.left}px - ${ge.right}px)`}}),oe=S(()=>({zIndex:W.value-1}));return{layoutItemStyles:fe,layoutItemScrimStyles:oe,zIndex:W}},unregister:k=>{a.delete(k),s.delete(k),i.delete(k),o.delete(k),l.delete(k),r.value=r.value.filter(C=>C!==k)},mainRect:h,mainStyles:g,getLayoutItem:E,items:y,layoutRect:c,rootZIndex:n});const O=S(()=>["v-layout",{"v-layout--full-height":e.fullHeight}]),I=S(()=>({zIndex:n.value,position:t?"relative":void 0,overflow:t?"hidden":void 0}));return{layoutClasses:O,layoutStyles:I,getLayoutItem:E,items:y,layoutRect:c,layoutRef:u}}const Ox=z({...ye(),...Ex({fullHeight:!0}),...je()},"VApp"),Ix=se()({name:"VApp",props:Ox(),setup(e,t){let{slots:n}=t;const r=Qe(e),{layoutClasses:s,layoutStyles:i,getLayoutItem:a,items:o,layoutRef:l}=kx(e),{rtlClasses:u}=mr();return ve(()=>{var c;return v("div",{ref:l,class:["v-application",r.themeClasses.value,s.value,u.value,e.class],style:[i.value,e.style]},[v("div",{class:"v-application__wrap"},[(c=n.default)==null?void 0:c.call(n)])])}),{getLayoutItem:a,items:o,theme:r}}}),Ax={__name:"App",setup(e){return(t,n)=>(Ye(),gt(Ix,{id:"inspire"},{default:re(()=>[v(xx),v(u0)]),_:1}))}},Vx="modulepreload",Px=function(e,t){return new URL(e,t).href},md={},Mx=function(t,n,r){if(!n||n.length===0)return t();const s=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=Px(i,r),i in md)return;md[i]=!0;const a=i.endsWith(".css"),o=a?'[rel="stylesheet"]':"";if(!!r)for(let c=s.length-1;c>=0;c--){const d=s[c];if(d.href===i&&(!a||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${o}`))return;const u=document.createElement("link");if(u.rel=a?"stylesheet":Vx,a||(u.as="script",u.crossOrigin=""),u.href=i,document.head.appendChild(u),a)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i})};async function Fx(){(await Mx(()=>import("./webfontloader-523643f5.js").then(t=>t.w),[],import.meta.url)).load({google:{families:["Roboto:100,300,400,500,700,900&display=swap"]}})}const Wo={"001":1,AD:1,AE:6,AF:6,AG:0,AI:1,AL:1,AM:1,AN:1,AR:1,AS:0,AT:1,AU:1,AX:1,AZ:1,BA:1,BD:0,BE:1,BG:1,BH:6,BM:1,BN:1,BR:0,BS:0,BT:0,BW:0,BY:1,BZ:0,CA:0,CH:1,CL:1,CM:1,CN:1,CO:0,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DM:0,DO:0,DZ:6,EC:1,EE:1,EG:6,ES:1,ET:0,FI:1,FJ:1,FO:1,FR:1,GB:1,"GB-alt-variant":0,GE:1,GF:1,GP:1,GR:1,GT:0,GU:0,HK:0,HN:0,HR:1,HU:1,ID:0,IE:1,IL:0,IN:0,IQ:6,IR:6,IS:1,IT:1,JM:0,JO:6,JP:0,KE:0,KG:1,KH:0,KR:0,KW:6,KZ:1,LA:0,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MH:0,MK:1,MM:0,MN:1,MO:0,MQ:1,MT:0,MV:5,MX:0,MY:1,MZ:0,NI:0,NL:1,NO:1,NP:0,NZ:1,OM:6,PA:0,PE:0,PH:0,PK:0,PL:1,PR:0,PT:0,PY:0,QA:6,RE:1,RO:1,RS:1,RU:1,SA:0,SD:6,SE:1,SG:0,SI:1,SK:1,SM:1,SV:0,SY:6,TH:0,TJ:1,TM:1,TR:1,TT:0,TW:0,UA:1,UM:0,US:0,UY:1,UZ:1,VA:1,VE:0,VI:0,VN:1,WS:0,XK:1,YE:0,ZA:0,ZW:0};function Dx(e,t){const n=[];let r=[];const s=Ev(e),i=Tv(e),a=s.getDay()-Wo[t.slice(-2).toUpperCase()],o=i.getDay()-Wo[t.slice(-2).toUpperCase()];for(let l=0;l<a;l++){const u=new Date(s);u.setDate(u.getDate()-(a-l)),r.push(u)}for(let l=1;l<=i.getDate();l++){const u=new Date(e.getFullYear(),e.getMonth(),l);r.push(u),r.length===7&&(n.push(r),r=[])}for(let l=1;l<7-o;l++){const u=new Date(i);u.setDate(u.getDate()+l),r.push(u)}return n.push(r),n}function Ev(e){return new Date(e.getFullYear(),e.getMonth(),1)}function Tv(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}function Nx(e){const t=e.split("-").map(i=>i.padStart(2,"0")).join("-"),n=new Date().getTimezoneOffset()/-60,r=n<0?"-":"+",s=Math.abs(n).toString().padStart(2,"0");return`${t}T00:00:00.000${r}${s}:00`}const Lx=/([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))/;function Rx(e){if(e==null)return new Date;if(e instanceof Date)return e;if(typeof e=="string"){let t;if(Lx.test(e)?t=Date.parse(Nx(e)):t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const hd=new Date(2e3,0,2);function $x(e){const t=Wo[e.slice(-2).toUpperCase()];return bl(7).map(n=>{const r=new Date(hd);return r.setDate(hd.getDate()+t+n),new Intl.DateTimeFormat(e,{weekday:"short"}).format(r)})}function Bx(e,t,n){const r=new Date(e);let s={};switch(t){case"fullDateWithWeekday":s={weekday:"long",day:"numeric",month:"long",year:"numeric"};break;case"normalDateWithWeekday":s={weekday:"short",day:"numeric",month:"short"};break;case"keyboardDate":s={};break;case"monthAndDate":s={month:"long",day:"numeric"};break;case"monthAndYear":s={month:"long",year:"numeric"};break;case"dayOfMonth":s={day:"numeric"};break;default:s={timeZone:"UTC",timeZoneName:"short"}}return new Intl.DateTimeFormat(n,s).format(r)}function Hx(e,t){const n=new Date(e);return n.setDate(n.getDate()+t),n}function Ux(e,t){const n=new Date(e);return n.setMonth(n.getMonth()+t),n}function zx(e){return e.getFullYear()}function jx(e){return e.getMonth()}function Wx(e){return new Date(e.getFullYear(),0,1)}function qx(e){return new Date(e.getFullYear(),11,31)}function Zx(e,t){return qo(e,t[0])&&Gx(e,t[1])}function Yx(e){const t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}function qo(e,t){return e.getTime()>t.getTime()}function Gx(e,t){return e.getTime()<t.getTime()}function vd(e,t){return e.getTime()===t.getTime()}function Kx(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function Jx(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function Xx(e,t,n){const r=new Date(e),s=new Date(t);return n==="month"?r.getMonth()-s.getMonth()+(r.getFullYear()-s.getFullYear())*12:Math.floor((r.getTime()-s.getTime())/(1e3*60*60*24))}function Qx(e,t){const n=new Date(e);return n.setFullYear(t),n}class e_{constructor(t){this.locale=t.locale}date(t){return Rx(t)}toJsDate(t){return t}addDays(t,n){return Hx(t,n)}addMonths(t,n){return Ux(t,n)}getWeekArray(t){return Dx(t,this.locale)}startOfMonth(t){return Ev(t)}endOfMonth(t){return Tv(t)}format(t,n){return Bx(t,n,this.locale)}isEqual(t,n){return vd(t,n)}isValid(t){return Yx(t)}isWithinRange(t,n){return Zx(t,n)}isAfter(t,n){return qo(t,n)}isBefore(t,n){return!qo(t,n)&&!vd(t,n)}isSameDay(t,n){return Kx(t,n)}isSameMonth(t,n){return Jx(t,n)}setYear(t,n){return Qx(t,n)}getDiff(t,n,r){return Xx(t,n,r)}getWeekdays(){return $x(this.locale)}getYear(t){return zx(t)}getMonth(t){return jx(t)}startOfYear(t){return Wx(t)}endOfYear(t){return qx(t)}}const gd=Symbol.for("vuetify:date-adapter");function t_(e){return Et({adapter:e_,locale:{af:"af-ZA",bg:"bg-BG",ca:"ca-ES",ckb:"",cs:"",de:"de-DE",el:"el-GR",en:"en-US",et:"et-EE",fa:"fa-IR",fi:"fi-FI",hr:"hr-HR",hu:"hu-HU",he:"he-IL",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lv:"lv-LV",lt:"lt-LT",nl:"nl-NL",no:"nn-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",srCyrl:"sr-SP",srLatn:"sr-SP",sv:"sv-SE",th:"th-TH",tr:"tr-TR",az:"az-AZ",uk:"uk-UA",vi:"vi-VN",zhHans:"zh-CN",zhHant:"zh-TW"}},e)}function kv(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{blueprint:t,...n}=e,r=Et(t,n),{aliases:s={},components:i={},directives:a={}}=r,o=Np(r.defaults),l=Jb(r.display,r.ssr),u=eb(r.theme),c=ub(r.icons),d=bb(r.locale),f=t_(r.date);return{install:h=>{for(const g in a)h.directive(g,a[g]);for(const g in i)h.component(g,i[g]);for(const g in s)h.component(g,Hr({...s[g],name:g,aliasName:s[g].name}));if(u.install(h),h.provide(Ts,o),h.provide(Po,l),h.provide(Ni,u),h.provide(To,c),h.provide(Li,d),h.provide(gd,f),Ge&&r.ssr)if(h.$nuxt)h.$nuxt.hook("app:suspense:resolve",()=>{l.update()});else{const{mount:g}=h;h.mount=function(){const y=g(...arguments);return ut(()=>l.update()),h.mount=g,y}}At.reset(),h.mixin({computed:{$vuetify(){return nt({defaults:Sr.call(this,Ts),display:Sr.call(this,Po),theme:Sr.call(this,Ni),icons:Sr.call(this,To),locale:Sr.call(this,Li),date:Sr.call(this,gd)})}}})},defaults:o,display:l,theme:u,icons:c,locale:d,date:f}}const n_="3.3.9";kv.version=n_;function Sr(e){var r,s;const t=this.$,n=((r=t.parent)==null?void 0:r.provides)??((s=t.vnode.appContext)==null?void 0:s.provides);if(n&&e in n)return n[e]}const r_=kv({theme:{themes:{light:{colors:{primary:"#1867C0",secondary:"#5CBBF6"}}}},display:{mobileBreakpoint:"sm",thresholds:{xs:0,sm:340,md:540,lg:800,xl:1280}}}),s_=lp();function i_(e){Fx(),e.use(r_).use(s_)}const Xl=ip(Ax);Ue.defaultLocale="es-ES";Xl.config.globalProperties.$filters={mayuscula(e){return e&&e.length>0&&typeof e=="string"?e.toUpperCase():e},moneda(e){return e==null?0:Intl.NumberFormat("de-DE").format(e)??0},fechaLarga(){if(!value)return"";var e=Q.fromFormat(value,"dd-MM-yyyy HH:mm:ss").toFormat("d LLL yyyy HH:mm");return e=="Invalid DateTime"?value:e},fecha(e){if(!e)return"";var t=Q.fromFormat(e,"dd-MM-yyyy HH:mm:ss").toFormat("d LLL yyyy HH:mm");if(t=="Invalid DateTime")var t=Q.fromFormat(e,"yyyy-MM-dd HH:mm:ss").toFormat("d LLL yyyy HH:mm");if(t=="Invalid DateTime")var t=Q.fromFormat(e,"yyyy-MM-dd ").toFormat("d LLL yyyy");if(t=="Invalid DateTime")var t=Q.fromISO(e).toFormat("d LLL yyyy");return t=="Invalid DateTime"?e:t}};i_(Xl);Xl.mount("#app");

CHANGELOG
=========


* v3.0.1 (2017-07-23)

  * Resolved regression introduced in once listeners in v3.0.0 [#49](https://github.com/igorw/evenement/pull/49)

* v3.0.0 (2017-07-23)

  * Passing null as event name throw exception [#46](https://github.com/igorw/evenement/pull/46), and [#47](https://github.com/igorw/evenement/pull/47)
  * Performance improvements [#39](https://github.com/igorw/evenement/pull/39), and [#45](https://github.com/igorw/evenement/pull/45)
  * Remove once listeners [#44](https://github.com/igorw/evenement/pull/44), [#45](https://github.com/igorw/evenement/pull/45)

* v2.1.0 (2017-07-17)

  * Chaining for "on" method [#30](https://github.com/igorw/evenement/pull/30)
  * Unit tests (on Travis) improvements [#33](https://github.com/igorw/evenement/pull/33), [#36](https://github.com/igorw/evenement/pull/36), and [#37](https://github.com/igorw/evenement/pull/37)
  * Benchmarks added [#35](https://github.com/igorw/evenement/pull/35), and [#40](https://github.com/igorw/evenement/pull/40)
  * Minor performance improvements [#42](https://github.com/igorw/evenement/pull/42), and [#38](https://github.com/igorw/evenement/pull/38)

* v2.0.0 (2012-11-02)

  * Require PHP >=5.4.0
  * Added EventEmitterTrait
  * Removed EventEmitter2

* v1.1.0 (2017-07-17)

  * Chaining for "on" method [#29](https://github.com/igorw/evenement/pull/29)
  * Minor performance improvements [#43](https://github.com/igorw/evenement/pull/43)

* v1.0.0 (2012-05-30)

  * Inital stable release

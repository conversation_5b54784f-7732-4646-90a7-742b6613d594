(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(r){if(r.ep)return;r.ep=!0;const a=n(r);fetch(r.href,a)}})();function Cl(e,t){const n=Object.create(null),s=e.split(",");for(let r=0;r<s.length;r++)n[s[r]]=!0;return t?r=>!!n[r.toLowerCase()]:r=>!!n[r]}const Ue={},Rs=[],Kt=()=>{},Yv=()=>!1,Jv=/^on[^a-z]/,Si=e=>Jv.test(e),xl=e=>e.startsWith("onUpdate:"),Qe=Object.assign,_l=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Xv=Object.prototype.hasOwnProperty,Oe=(e,t)=>Xv.call(e,t),le=Array.isArray,$s=e=>Zr(e)==="[object Map]",Ci=e=>Zr(e)==="[object Set]",Au=e=>Zr(e)==="[object Date]",be=e=>typeof e=="function",Ge=e=>typeof e=="string",Or=e=>typeof e=="symbol",Le=e=>e!==null&&typeof e=="object",Zd=e=>Le(e)&&be(e.then)&&be(e.catch),Gd=Object.prototype.toString,Zr=e=>Gd.call(e),Qv=e=>Zr(e).slice(8,-1),Kd=e=>Zr(e)==="[object Object]",El=e=>Ge(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ma=Cl(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),xi=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},eg=/-(\w)/g,Ht=xi(e=>e.replace(eg,(t,n)=>n?n.toUpperCase():"")),tg=/\B([A-Z])/g,Ss=xi(e=>e.replace(tg,"-$1").toLowerCase()),xn=xi(e=>e.charAt(0).toUpperCase()+e.slice(1)),so=xi(e=>e?`on${xn(e)}`:""),Ir=(e,t)=>!Object.is(e,t),Da=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Za=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Ga=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ng=e=>{const t=Ge(e)?Number(e):NaN;return isNaN(t)?e:t};let Vu;const Po=()=>Vu||(Vu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Gr(e){if(le(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=Ge(s)?ig(s):Gr(s);if(r)for(const a in r)t[a]=r[a]}return t}else{if(Ge(e))return e;if(Le(e))return e}}const sg=/;(?![^(]*\))/g,rg=/:([^]+)/,ag=/\/\*[^]*?\*\//g;function ig(e){const t={};return e.replace(ag,"").split(sg).forEach(n=>{if(n){const s=n.split(rg);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ar(e){let t="";if(Ge(e))t=e;else if(le(e))for(let n=0;n<e.length;n++){const s=Ar(e[n]);s&&(t+=s+" ")}else if(Le(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const og="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",lg=Cl(og);function Yd(e){return!!e||e===""}function ug(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=_i(e[s],t[s]);return n}function _i(e,t){if(e===t)return!0;let n=Au(e),s=Au(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Or(e),s=Or(t),n||s)return e===t;if(n=le(e),s=le(t),n||s)return n&&s?ug(e,t):!1;if(n=Le(e),s=Le(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,a=Object.keys(t).length;if(r!==a)return!1;for(const i in e){const o=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(o&&!l||!o&&l||!_i(e[i],t[i]))return!1}}return String(e)===String(t)}function cg(e,t){return e.findIndex(n=>_i(n,t))}const ie=e=>Ge(e)?e:e==null?"":le(e)||Le(e)&&(e.toString===Gd||!be(e.toString))?JSON.stringify(e,Jd,2):String(e),Jd=(e,t)=>t&&t.__v_isRef?Jd(e,t.value):$s(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r])=>(n[`${s} =>`]=r,n),{})}:Ci(t)?{[`Set(${t.size})`]:[...t.values()]}:Le(t)&&!le(t)&&!Kd(t)?String(t):t;let Tt;class Xd{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Tt,!t&&Tt&&(this.index=(Tt.scopes||(Tt.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=Tt;try{return Tt=this,t()}finally{Tt=n}}}on(){Tt=this}off(){Tt=this.parent}stop(t){if(this._active){let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.scopes)for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}function Kr(e){return new Xd(e)}function dg(e,t=Tt){t&&t.active&&t.effects.push(e)}function Qd(){return Tt}function pt(e){Tt&&Tt.cleanups.push(e)}const Tl=e=>{const t=new Set(e);return t.w=0,t.n=0,t},ef=e=>(e.w&qn)>0,tf=e=>(e.n&qn)>0,fg=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=qn},mg=e=>{const{deps:t}=e;if(t.length){let n=0;for(let s=0;s<t.length;s++){const r=t[s];ef(r)&&!tf(r)?r.delete(e):t[n++]=r,r.w&=~qn,r.n&=~qn}t.length=n}},Ka=new WeakMap;let hr=0,qn=1;const Mo=30;let qt;const gs=Symbol(""),Do=Symbol("");class kl{constructor(t,n=null,s){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,dg(this,s)}run(){if(!this.active)return this.fn();let t=qt,n=zn;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=qt,qt=this,zn=!0,qn=1<<++hr,hr<=Mo?fg(this):Pu(this),this.fn()}finally{hr<=Mo&&mg(this),qn=1<<--hr,qt=this.parent,zn=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){qt===this?this.deferStop=!0:this.active&&(Pu(this),this.onStop&&this.onStop(),this.active=!1)}}function Pu(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let zn=!0;const nf=[];function Gs(){nf.push(zn),zn=!1}function Ks(){const e=nf.pop();zn=e===void 0?!0:e}function xt(e,t,n){if(zn&&qt){let s=Ka.get(e);s||Ka.set(e,s=new Map);let r=s.get(n);r||s.set(n,r=Tl()),sf(r)}}function sf(e,t){let n=!1;hr<=Mo?tf(e)||(e.n|=qn,n=!ef(e)):n=!e.has(qt),n&&(e.add(qt),qt.deps.push(e))}function pn(e,t,n,s,r,a){const i=Ka.get(e);if(!i)return;let o=[];if(t==="clear")o=[...i.values()];else if(n==="length"&&le(e)){const l=Number(s);i.forEach((u,c)=>{(c==="length"||c>=l)&&o.push(u)})}else switch(n!==void 0&&o.push(i.get(n)),t){case"add":le(e)?El(n)&&o.push(i.get("length")):(o.push(i.get(gs)),$s(e)&&o.push(i.get(Do)));break;case"delete":le(e)||(o.push(i.get(gs)),$s(e)&&o.push(i.get(Do)));break;case"set":$s(e)&&o.push(i.get(gs));break}if(o.length===1)o[0]&&No(o[0]);else{const l=[];for(const u of o)u&&l.push(...u);No(Tl(l))}}function No(e,t){const n=le(e)?e:[...e];for(const s of n)s.computed&&Mu(s);for(const s of n)s.computed||Mu(s)}function Mu(e,t){(e!==qt||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}function hg(e,t){var n;return(n=Ka.get(e))==null?void 0:n.get(t)}const vg=Cl("__proto__,__v_isRef,__isVue"),rf=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Or)),gg=Ol(),yg=Ol(!1,!0),pg=Ol(!0),Du=bg();function bg(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const s=ye(this);for(let a=0,i=this.length;a<i;a++)xt(s,"get",a+"");const r=s[t](...n);return r===-1||r===!1?s[t](...n.map(ye)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Gs();const s=ye(this)[t].apply(this,n);return Ks(),s}}),e}function wg(e){const t=ye(this);return xt(t,"has",e),t.hasOwnProperty(e)}function Ol(e=!1,t=!1){return function(s,r,a){if(r==="__v_isReactive")return!e;if(r==="__v_isReadonly")return e;if(r==="__v_isShallow")return t;if(r==="__v_raw"&&a===(e?t?Fg:cf:t?uf:lf).get(s))return s;const i=le(s);if(!e){if(i&&Oe(Du,r))return Reflect.get(Du,r,a);if(r==="hasOwnProperty")return wg}const o=Reflect.get(s,r,a);return(Or(r)?rf.has(r):vg(r))||(e||xt(s,"get",r),t)?o:Me(o)?i&&El(r)?o:o.value:Le(o)?e?Yr(o):ct(o):o}}const Sg=af(),Cg=af(!0);function af(e=!1){return function(n,s,r,a){let i=n[s];if(zs(i)&&Me(i)&&!Me(r))return!1;if(!e&&(!Ya(r)&&!zs(r)&&(i=ye(i),r=ye(r)),!le(n)&&Me(i)&&!Me(r)))return i.value=r,!0;const o=le(n)&&El(s)?Number(s)<n.length:Oe(n,s),l=Reflect.set(n,s,r,a);return n===ye(a)&&(o?Ir(r,i)&&pn(n,"set",s,r):pn(n,"add",s,r)),l}}function xg(e,t){const n=Oe(e,t);e[t];const s=Reflect.deleteProperty(e,t);return s&&n&&pn(e,"delete",t,void 0),s}function _g(e,t){const n=Reflect.has(e,t);return(!Or(t)||!rf.has(t))&&xt(e,"has",t),n}function Eg(e){return xt(e,"iterate",le(e)?"length":gs),Reflect.ownKeys(e)}const of={get:gg,set:Sg,deleteProperty:xg,has:_g,ownKeys:Eg},Tg={get:pg,set(e,t){return!0},deleteProperty(e,t){return!0}},kg=Qe({},of,{get:yg,set:Cg}),Il=e=>e,Ei=e=>Reflect.getPrototypeOf(e);function va(e,t,n=!1,s=!1){e=e.__v_raw;const r=ye(e),a=ye(t);n||(t!==a&&xt(r,"get",t),xt(r,"get",a));const{has:i}=Ei(r),o=s?Il:n?Pl:Vr;if(i.call(r,t))return o(e.get(t));if(i.call(r,a))return o(e.get(a));e!==r&&e.get(t)}function ga(e,t=!1){const n=this.__v_raw,s=ye(n),r=ye(e);return t||(e!==r&&xt(s,"has",e),xt(s,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function ya(e,t=!1){return e=e.__v_raw,!t&&xt(ye(e),"iterate",gs),Reflect.get(e,"size",e)}function Nu(e){e=ye(e);const t=ye(this);return Ei(t).has.call(t,e)||(t.add(e),pn(t,"add",e,e)),this}function Fu(e,t){t=ye(t);const n=ye(this),{has:s,get:r}=Ei(n);let a=s.call(n,e);a||(e=ye(e),a=s.call(n,e));const i=r.call(n,e);return n.set(e,t),a?Ir(t,i)&&pn(n,"set",e,t):pn(n,"add",e,t),this}function Lu(e){const t=ye(this),{has:n,get:s}=Ei(t);let r=n.call(t,e);r||(e=ye(e),r=n.call(t,e)),s&&s.call(t,e);const a=t.delete(e);return r&&pn(t,"delete",e,void 0),a}function Ru(){const e=ye(this),t=e.size!==0,n=e.clear();return t&&pn(e,"clear",void 0,void 0),n}function pa(e,t){return function(s,r){const a=this,i=a.__v_raw,o=ye(i),l=t?Il:e?Pl:Vr;return!e&&xt(o,"iterate",gs),i.forEach((u,c)=>s.call(r,l(u),l(c),a))}}function ba(e,t,n){return function(...s){const r=this.__v_raw,a=ye(r),i=$s(a),o=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,u=r[e](...s),c=n?Il:t?Pl:Vr;return!t&&xt(a,"iterate",l?Do:gs),{next(){const{value:d,done:f}=u.next();return f?{value:d,done:f}:{value:o?[c(d[0]),c(d[1])]:c(d),done:f}},[Symbol.iterator](){return this}}}}function Vn(e){return function(...t){return e==="delete"?!1:this}}function Og(){const e={get(a){return va(this,a)},get size(){return ya(this)},has:ga,add:Nu,set:Fu,delete:Lu,clear:Ru,forEach:pa(!1,!1)},t={get(a){return va(this,a,!1,!0)},get size(){return ya(this)},has:ga,add:Nu,set:Fu,delete:Lu,clear:Ru,forEach:pa(!1,!0)},n={get(a){return va(this,a,!0)},get size(){return ya(this,!0)},has(a){return ga.call(this,a,!0)},add:Vn("add"),set:Vn("set"),delete:Vn("delete"),clear:Vn("clear"),forEach:pa(!0,!1)},s={get(a){return va(this,a,!0,!0)},get size(){return ya(this,!0)},has(a){return ga.call(this,a,!0)},add:Vn("add"),set:Vn("set"),delete:Vn("delete"),clear:Vn("clear"),forEach:pa(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(a=>{e[a]=ba(a,!1,!1),n[a]=ba(a,!0,!1),t[a]=ba(a,!1,!0),s[a]=ba(a,!0,!0)}),[e,n,t,s]}const[Ig,Ag,Vg,Pg]=Og();function Al(e,t){const n=t?e?Pg:Vg:e?Ag:Ig;return(s,r,a)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Oe(n,r)&&r in s?n:s,r,a)}const Mg={get:Al(!1,!1)},Dg={get:Al(!1,!0)},Ng={get:Al(!0,!1)},lf=new WeakMap,uf=new WeakMap,cf=new WeakMap,Fg=new WeakMap;function Lg(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Rg(e){return e.__v_skip||!Object.isExtensible(e)?0:Lg(Qv(e))}function ct(e){return zs(e)?e:Vl(e,!1,of,Mg,lf)}function $g(e){return Vl(e,!1,kg,Dg,uf)}function Yr(e){return Vl(e,!0,Tg,Ng,cf)}function Vl(e,t,n,s,r){if(!Le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=r.get(e);if(a)return a;const i=Rg(e);if(i===0)return e;const o=new Proxy(e,i===2?s:n);return r.set(e,o),o}function gn(e){return zs(e)?gn(e.__v_raw):!!(e&&e.__v_isReactive)}function zs(e){return!!(e&&e.__v_isReadonly)}function Ya(e){return!!(e&&e.__v_isShallow)}function df(e){return gn(e)||zs(e)}function ye(e){const t=e&&e.__v_raw;return t?ye(t):e}function Ti(e){return Za(e,"__v_skip",!0),e}const Vr=e=>Le(e)?ct(e):e,Pl=e=>Le(e)?Yr(e):e;function ff(e){zn&&qt&&(e=ye(e),sf(e.dep||(e.dep=Tl())))}function mf(e,t){e=ye(e);const n=e.dep;n&&No(n)}function Me(e){return!!(e&&e.__v_isRef===!0)}function U(e){return hf(e,!1)}function Ce(e){return hf(e,!0)}function hf(e,t){return Me(e)?e:new Bg(e,t)}class Bg{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:ye(t),this._value=n?t:Vr(t)}get value(){return ff(this),this._value}set value(t){const n=this.__v_isShallow||Ya(t)||zs(t);t=n?t:ye(t),Ir(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:Vr(t),mf(this))}}function rt(e){return Me(e)?e.value:e}const Hg={get:(e,t,n)=>rt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Me(r)&&!Me(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function vf(e){return gn(e)?e:new Proxy(e,Hg)}function ki(e){const t=le(e)?new Array(e.length):{};for(const n in e)t[n]=gf(e,n);return t}class Ug{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return hg(ye(this._object),this._key)}}class zg{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function he(e,t,n){return Me(e)?e:be(e)?new zg(e):Le(e)&&arguments.length>1?gf(e,t,n):U(e)}function gf(e,t,n){const s=e[t];return Me(s)?s:new Ug(e,t,n)}class jg{constructor(t,n,s,r){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new kl(t,()=>{this._dirty||(this._dirty=!0,mf(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=s}get value(){const t=ye(this);return ff(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function Wg(e,t,n=!1){let s,r;const a=be(e);return a?(s=e,r=Kt):(s=e.get,r=e.set),new jg(s,r,a||!r,n)}function jn(e,t,n,s){let r;try{r=s?e(...s):e()}catch(a){Oi(a,t,n)}return r}function $t(e,t,n,s){if(be(e)){const a=jn(e,t,n,s);return a&&Zd(a)&&a.catch(i=>{Oi(i,t,n)}),a}const r=[];for(let a=0;a<e.length;a++)r.push($t(e[a],t,n,s));return r}function Oi(e,t,n,s=!0){const r=t?t.vnode:null;if(t){let a=t.parent;const i=t.proxy,o=n;for(;a;){const u=a.ec;if(u){for(let c=0;c<u.length;c++)if(u[c](e,i,o)===!1)return}a=a.parent}const l=t.appContext.config.errorHandler;if(l){jn(l,null,10,[e,i,o]);return}}qg(e,n,r,s)}function qg(e,t,n,s=!0){console.error(e)}let Pr=!1,Fo=!1;const ft=[];let sn=0;const Bs=[];let mn=null,ls=0;const yf=Promise.resolve();let Ml=null;function bt(e){const t=Ml||yf;return e?t.then(this?e.bind(this):e):t}function Zg(e){let t=sn+1,n=ft.length;for(;t<n;){const s=t+n>>>1;Mr(ft[s])<e?t=s+1:n=s}return t}function Dl(e){(!ft.length||!ft.includes(e,Pr&&e.allowRecurse?sn+1:sn))&&(e.id==null?ft.push(e):ft.splice(Zg(e.id),0,e),pf())}function pf(){!Pr&&!Fo&&(Fo=!0,Ml=yf.then(wf))}function Gg(e){const t=ft.indexOf(e);t>sn&&ft.splice(t,1)}function Kg(e){le(e)?Bs.push(...e):(!mn||!mn.includes(e,e.allowRecurse?ls+1:ls))&&Bs.push(e),pf()}function $u(e,t=Pr?sn+1:0){for(;t<ft.length;t++){const n=ft[t];n&&n.pre&&(ft.splice(t,1),t--,n())}}function bf(e){if(Bs.length){const t=[...new Set(Bs)];if(Bs.length=0,mn){mn.push(...t);return}for(mn=t,mn.sort((n,s)=>Mr(n)-Mr(s)),ls=0;ls<mn.length;ls++)mn[ls]();mn=null,ls=0}}const Mr=e=>e.id==null?1/0:e.id,Yg=(e,t)=>{const n=Mr(e)-Mr(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function wf(e){Fo=!1,Pr=!0,ft.sort(Yg);const t=Kt;try{for(sn=0;sn<ft.length;sn++){const n=ft[sn];n&&n.active!==!1&&jn(n,null,14)}}finally{sn=0,ft.length=0,bf(),Pr=!1,Ml=null,(ft.length||Bs.length)&&wf()}}function Jg(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Ue;let r=n;const a=t.startsWith("update:"),i=a&&t.slice(7);if(i&&i in s){const c=`${i==="modelValue"?"model":i}Modifiers`,{number:d,trim:f}=s[c]||Ue;f&&(r=n.map(h=>Ge(h)?h.trim():h)),d&&(r=n.map(Ga))}let o,l=s[o=so(t)]||s[o=so(Ht(t))];!l&&a&&(l=s[o=so(Ss(t))]),l&&$t(l,e,6,r);const u=s[o+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[o])return;e.emitted[o]=!0,$t(u,e,6,r)}}function Sf(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const a=e.emits;let i={},o=!1;if(!be(e)){const l=u=>{const c=Sf(u,t,!0);c&&(o=!0,Qe(i,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!a&&!o?(Le(e)&&s.set(e,null),null):(le(a)?a.forEach(l=>i[l]=null):Qe(i,a),Le(e)&&s.set(e,i),i)}function Ii(e,t){return!e||!Si(t)?!1:(t=t.slice(2).replace(/Once$/,""),Oe(e,t[0].toLowerCase()+t.slice(1))||Oe(e,Ss(t))||Oe(e,t))}let Ct=null,Ai=null;function Ja(e){const t=Ct;return Ct=e,Ai=e&&e.type.__scopeId||null,t}function Xg(e){Ai=e}function Qg(){Ai=null}function F(e,t=Ct,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Xu(-1);const a=Ja(t);let i;try{i=e(...r)}finally{Ja(a),s._d&&Xu(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function ro(e){const{type:t,vnode:n,proxy:s,withProxy:r,props:a,propsOptions:[i],slots:o,attrs:l,emit:u,render:c,renderCache:d,data:f,setupState:h,ctx:v,inheritAttrs:g}=e;let b,_;const E=Ja(e);try{if(n.shapeFlag&4){const k=r||s;b=nn(c.call(k,k,d,a,h,f,v)),_=l}else{const k=t;b=nn(k.length>1?k(a,{attrs:l,slots:o,emit:u}):k(a,null)),_=t.props?l:ey(l)}}catch(k){Sr.length=0,Oi(k,e,1),b=m(Yt)}let w=b;if(_&&g!==!1){const k=Object.keys(_),{shapeFlag:P}=w;k.length&&P&7&&(i&&k.some(xl)&&(_=ty(_,i)),w=wn(w,_))}return n.dirs&&(w=wn(w),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&(w.transition=n.transition),b=w,Ja(E),b}const ey=e=>{let t;for(const n in e)(n==="class"||n==="style"||Si(n))&&((t||(t={}))[n]=e[n]);return t},ty=(e,t)=>{const n={};for(const s in e)(!xl(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function ny(e,t,n){const{props:s,children:r,component:a}=e,{props:i,children:o,patchFlag:l}=t,u=a.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?Bu(s,i,u):!!i;if(l&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const f=c[d];if(i[f]!==s[f]&&!Ii(u,f))return!0}}}else return(r||o)&&(!o||!o.$stable)?!0:s===i?!1:s?i?Bu(s,i,u):!0:!!i;return!1}function Bu(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const a=s[r];if(t[a]!==e[a]&&!Ii(n,a))return!0}return!1}function sy({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const ry=e=>e.__isSuspense;function ay(e,t){t&&t.pendingBranch?le(e)?t.effects.push(...e):t.effects.push(e):Kg(e)}function _n(e,t){return Nl(e,null,t)}const wa={};function we(e,t,n){return Nl(e,t,n)}function Nl(e,t,{immediate:n,deep:s,flush:r,onTrack:a,onTrigger:i}=Ue){var o;const l=Qd()===((o=at)==null?void 0:o.scope)?at:null;let u,c=!1,d=!1;if(Me(e)?(u=()=>e.value,c=Ya(e)):gn(e)?(u=()=>e,s=!0):le(e)?(d=!0,c=e.some(k=>gn(k)||Ya(k)),u=()=>e.map(k=>{if(Me(k))return k.value;if(gn(k))return fs(k);if(be(k))return jn(k,l,2)})):be(e)?t?u=()=>jn(e,l,2):u=()=>{if(!(l&&l.isUnmounted))return f&&f(),$t(e,l,3,[h])}:u=Kt,t&&s){const k=u;u=()=>fs(k())}let f,h=k=>{f=E.onStop=()=>{jn(k,l,4)}},v;if(Rr)if(h=Kt,t?n&&$t(t,l,3,[u(),d?[]:void 0,h]):u(),r==="sync"){const k=Xy();v=k.__watcherHandles||(k.__watcherHandles=[])}else return Kt;let g=d?new Array(e.length).fill(wa):wa;const b=()=>{if(E.active)if(t){const k=E.run();(s||c||(d?k.some((P,O)=>Ir(P,g[O])):Ir(k,g)))&&(f&&f(),$t(t,l,3,[k,g===wa?void 0:d&&g[0]===wa?[]:g,h]),g=k)}else E.run()};b.allowRecurse=!!t;let _;r==="sync"?_=b:r==="post"?_=()=>wt(b,l&&l.suspense):(b.pre=!0,l&&(b.id=l.uid),_=()=>Dl(b));const E=new kl(u,_);t?n?b():g=E.run():r==="post"?wt(E.run.bind(E),l&&l.suspense):E.run();const w=()=>{E.stop(),l&&l.scope&&_l(l.scope.effects,E)};return v&&v.push(w),w}function iy(e,t,n){const s=this.proxy,r=Ge(e)?e.includes(".")?Cf(s,e):()=>s[e]:e.bind(s,s);let a;be(t)?a=t:(a=t.handler,n=t);const i=at;js(this);const o=Nl(r,a.bind(s),n);return i?js(i):ys(),o}function Cf(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}function fs(e,t){if(!Le(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),Me(e))fs(e.value,t);else if(le(e))for(let n=0;n<e.length;n++)fs(e[n],t);else if(Ci(e)||$s(e))e.forEach(n=>{fs(n,t)});else if(Kd(e))for(const n in e)fs(e[n],t);return e}function Xe(e,t){const n=Ct;if(n===null)return e;const s=Di(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let a=0;a<t.length;a++){let[i,o,l,u=Ue]=t[a];i&&(be(i)&&(i={mounted:i,updated:i}),i.deep&&fs(o),r.push({dir:i,instance:s,value:o,oldValue:void 0,arg:l,modifiers:u}))}return e}function ns(e,t,n,s){const r=e.dirs,a=t&&t.dirs;for(let i=0;i<r.length;i++){const o=r[i];a&&(o.oldValue=a[i].value);let l=o.dir[s];l&&(Gs(),$t(l,n,8,[e.el,o,e,t]),Ks())}}function xf(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Tn(()=>{e.isMounted=!0}),ln(()=>{e.isUnmounting=!0}),e}const Dt=[Function,Array],_f={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Dt,onEnter:Dt,onAfterEnter:Dt,onEnterCancelled:Dt,onBeforeLeave:Dt,onLeave:Dt,onAfterLeave:Dt,onLeaveCancelled:Dt,onBeforeAppear:Dt,onAppear:Dt,onAfterAppear:Dt,onAppearCancelled:Dt},oy={name:"BaseTransition",props:_f,setup(e,{slots:t}){const n=Ul(),s=xf();let r;return()=>{const a=t.default&&Fl(t.default(),!0);if(!a||!a.length)return;let i=a[0];if(a.length>1){for(const g of a)if(g.type!==Yt){i=g;break}}const o=ye(e),{mode:l}=o;if(s.isLeaving)return ao(i);const u=Hu(i);if(!u)return ao(i);const c=Dr(u,o,s,n);Nr(u,c);const d=n.subTree,f=d&&Hu(d);let h=!1;const{getTransitionKey:v}=u.type;if(v){const g=v();r===void 0?r=g:g!==r&&(r=g,h=!0)}if(f&&f.type!==Yt&&(!us(u,f)||h)){const g=Dr(f,o,s,n);if(Nr(f,g),l==="out-in")return s.isLeaving=!0,g.afterLeave=()=>{s.isLeaving=!1,n.update.active!==!1&&n.update()},ao(i);l==="in-out"&&u.type!==Yt&&(g.delayLeave=(b,_,E)=>{const w=Ef(s,f);w[String(f.key)]=f,b._leaveCb=()=>{_(),b._leaveCb=void 0,delete c.delayedLeave},c.delayedLeave=E})}return i}}},ly=oy;function Ef(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Dr(e,t,n,s){const{appear:r,mode:a,persisted:i=!1,onBeforeEnter:o,onEnter:l,onAfterEnter:u,onEnterCancelled:c,onBeforeLeave:d,onLeave:f,onAfterLeave:h,onLeaveCancelled:v,onBeforeAppear:g,onAppear:b,onAfterAppear:_,onAppearCancelled:E}=t,w=String(e.key),k=Ef(n,e),P=(y,S)=>{y&&$t(y,s,9,S)},O=(y,S)=>{const A=S[1];P(y,S),le(y)?y.every(z=>z.length<=1)&&A():y.length<=1&&A()},T={mode:a,persisted:i,beforeEnter(y){let S=o;if(!n.isMounted)if(r)S=g||o;else return;y._leaveCb&&y._leaveCb(!0);const A=k[w];A&&us(e,A)&&A.el._leaveCb&&A.el._leaveCb(),P(S,[y])},enter(y){let S=l,A=u,z=c;if(!n.isMounted)if(r)S=b||l,A=_||u,z=E||c;else return;let M=!1;const B=y._enterCb=N=>{M||(M=!0,N?P(z,[y]):P(A,[y]),T.delayedLeave&&T.delayedLeave(),y._enterCb=void 0)};S?O(S,[y,B]):B()},leave(y,S){const A=String(e.key);if(y._enterCb&&y._enterCb(!0),n.isUnmounting)return S();P(d,[y]);let z=!1;const M=y._leaveCb=B=>{z||(z=!0,S(),B?P(v,[y]):P(h,[y]),y._leaveCb=void 0,k[A]===e&&delete k[A])};k[A]=e,f?O(f,[y,M]):M()},clone(y){return Dr(y,t,n,s)}};return T}function ao(e){if(Vi(e))return e=wn(e),e.children=null,e}function Hu(e){return Vi(e)?e.children?e.children[0]:void 0:e}function Nr(e,t){e.shapeFlag&6&&e.component?Nr(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Fl(e,t=!1,n){let s=[],r=0;for(let a=0;a<e.length;a++){let i=e[a];const o=n==null?i.key:String(n)+String(i.key!=null?i.key:a);i.type===_e?(i.patchFlag&128&&r++,s=s.concat(Fl(i.children,t,o))):(t||i.type!==Yt)&&s.push(o!=null?wn(i,{key:o}):i)}if(r>1)for(let a=0;a<s.length;a++)s[a].patchFlag=-2;return s}function uy(e,t){return be(e)?(()=>Qe({name:e.name},t,{setup:e}))():e}const Na=e=>!!e.type.__asyncLoader,Vi=e=>e.type.__isKeepAlive;function Tf(e,t){Of(e,"a",t)}function kf(e,t){Of(e,"da",t)}function Of(e,t,n=at){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Pi(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Vi(r.parent.vnode)&&cy(s,t,n,r),r=r.parent}}function cy(e,t,n,s){const r=Pi(t,e,s,!0);Af(()=>{_l(s[t],r)},n)}function Pi(e,t,n=at,s=!1){if(n){const r=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=(...i)=>{if(n.isUnmounted)return;Gs(),js(n);const o=$t(t,n,e,i);return ys(),Ks(),o});return s?r.unshift(a):r.push(a),a}}const En=e=>(t,n=at)=>(!Rr||e==="sp")&&Pi(e,(...s)=>t(...s),n),Ll=En("bm"),Tn=En("m"),dy=En("bu"),If=En("u"),ln=En("bum"),Af=En("um"),fy=En("sp"),my=En("rtg"),hy=En("rtc");function vy(e,t=at){Pi("ec",e,t)}const Vf="components",gy="directives",Pf=Symbol.for("v-ndc");function yy(e){return Ge(e)?Mf(Vf,e,!1)||e:e||Pf}function bn(e){return Mf(gy,e)}function Mf(e,t,n=!0,s=!1){const r=Ct||at;if(r){const a=r.type;if(e===Vf){const o=Ky(a,!1);if(o&&(o===t||o===Ht(t)||o===xn(Ht(t))))return a}const i=Uu(r[e]||a[e],t)||Uu(r.appContext[e],t);return!i&&s?a:i}}function Uu(e,t){return e&&(e[t]||e[Ht(t)]||e[xn(Ht(t))])}function Ns(e,t,n,s){let r;const a=n&&n[s];if(le(e)||Ge(e)){r=new Array(e.length);for(let i=0,o=e.length;i<o;i++)r[i]=t(e[i],i,void 0,a&&a[i])}else if(typeof e=="number"){r=new Array(e);for(let i=0;i<e;i++)r[i]=t(i+1,i,void 0,a&&a[i])}else if(Le(e))if(e[Symbol.iterator])r=Array.from(e,(i,o)=>t(i,o,void 0,a&&a[o]));else{const i=Object.keys(e);r=new Array(i.length);for(let o=0,l=i.length;o<l;o++){const u=i[o];r[o]=t(e[u],u,o,a&&a[o])}}else r=[];return n&&(n[s]=r),r}const Lo=e=>e?jf(e)?Di(e)||e.proxy:Lo(e.parent):null,br=Qe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Lo(e.parent),$root:e=>Lo(e.root),$emit:e=>e.emit,$options:e=>Rl(e),$forceUpdate:e=>e.f||(e.f=()=>Dl(e.update)),$nextTick:e=>e.n||(e.n=bt.bind(e.proxy)),$watch:e=>iy.bind(e)}),io=(e,t)=>e!==Ue&&!e.__isScriptSetup&&Oe(e,t),py={get({_:e},t){const{ctx:n,setupState:s,data:r,props:a,accessCache:i,type:o,appContext:l}=e;let u;if(t[0]!=="$"){const h=i[t];if(h!==void 0)switch(h){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return a[t]}else{if(io(s,t))return i[t]=1,s[t];if(r!==Ue&&Oe(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&Oe(u,t))return i[t]=3,a[t];if(n!==Ue&&Oe(n,t))return i[t]=4,n[t];Ro&&(i[t]=0)}}const c=br[t];let d,f;if(c)return t==="$attrs"&&xt(e,"get",t),c(e);if((d=o.__cssModules)&&(d=d[t]))return d;if(n!==Ue&&Oe(n,t))return i[t]=4,n[t];if(f=l.config.globalProperties,Oe(f,t))return f[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:a}=e;return io(r,t)?(r[t]=n,!0):s!==Ue&&Oe(s,t)?(s[t]=n,!0):Oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(a[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:a}},i){let o;return!!n[i]||e!==Ue&&Oe(e,i)||io(t,i)||(o=a[0])&&Oe(o,i)||Oe(s,i)||Oe(br,i)||Oe(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function zu(e){return le(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ro=!0;function by(e){const t=Rl(e),n=e.proxy,s=e.ctx;Ro=!1,t.beforeCreate&&ju(t.beforeCreate,e,"bc");const{data:r,computed:a,methods:i,watch:o,provide:l,inject:u,created:c,beforeMount:d,mounted:f,beforeUpdate:h,updated:v,activated:g,deactivated:b,beforeDestroy:_,beforeUnmount:E,destroyed:w,unmounted:k,render:P,renderTracked:O,renderTriggered:T,errorCaptured:y,serverPrefetch:S,expose:A,inheritAttrs:z,components:M,directives:B,filters:N}=t;if(u&&wy(u,s,null),i)for(const se in i){const Y=i[se];be(Y)&&(s[se]=Y.bind(n))}if(r){const se=r.call(n,n);Le(se)&&(e.data=ct(se))}if(Ro=!0,a)for(const se in a){const Y=a[se],J=be(Y)?Y.bind(n,n):be(Y.get)?Y.get.bind(n,n):Kt,me=!be(Y)&&be(Y.set)?Y.set.bind(n):Kt,fe=C({get:J,set:me});Object.defineProperty(s,se,{enumerable:!0,configurable:!0,get:()=>fe.value,set:Ee=>fe.value=Ee})}if(o)for(const se in o)Df(o[se],s,n,se);if(l){const se=be(l)?l.call(n):l;Reflect.ownKeys(se).forEach(Y=>{_t(Y,se[Y])})}c&&ju(c,e,"c");function ee(se,Y){le(Y)?Y.forEach(J=>se(J.bind(n))):Y&&se(Y.bind(n))}if(ee(Ll,d),ee(Tn,f),ee(dy,h),ee(If,v),ee(Tf,g),ee(kf,b),ee(vy,y),ee(hy,O),ee(my,T),ee(ln,E),ee(Af,k),ee(fy,S),le(A))if(A.length){const se=e.exposed||(e.exposed={});A.forEach(Y=>{Object.defineProperty(se,Y,{get:()=>n[Y],set:J=>n[Y]=J})})}else e.exposed||(e.exposed={});P&&e.render===Kt&&(e.render=P),z!=null&&(e.inheritAttrs=z),M&&(e.components=M),B&&(e.directives=B)}function wy(e,t,n=Kt){le(e)&&(e=$o(e));for(const s in e){const r=e[s];let a;Le(r)?"default"in r?a=je(r.from||s,r.default,!0):a=je(r.from||s):a=je(r),Me(a)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>a.value,set:i=>a.value=i}):t[s]=a}}function ju(e,t,n){$t(le(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Df(e,t,n,s){const r=s.includes(".")?Cf(n,s):()=>n[s];if(Ge(e)){const a=t[e];be(a)&&we(r,a)}else if(be(e))we(r,e.bind(n));else if(Le(e))if(le(e))e.forEach(a=>Df(a,t,n,s));else{const a=be(e.handler)?e.handler.bind(n):t[e.handler];be(a)&&we(r,a,e)}}function Rl(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:a,config:{optionMergeStrategies:i}}=e.appContext,o=a.get(t);let l;return o?l=o:!r.length&&!n&&!s?l=t:(l={},r.length&&r.forEach(u=>Xa(l,u,i,!0)),Xa(l,t,i)),Le(t)&&a.set(t,l),l}function Xa(e,t,n,s=!1){const{mixins:r,extends:a}=t;a&&Xa(e,a,n,!0),r&&r.forEach(i=>Xa(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const o=Sy[i]||n&&n[i];e[i]=o?o(e[i],t[i]):t[i]}return e}const Sy={data:Wu,props:qu,emits:qu,methods:vr,computed:vr,beforeCreate:vt,created:vt,beforeMount:vt,mounted:vt,beforeUpdate:vt,updated:vt,beforeDestroy:vt,beforeUnmount:vt,destroyed:vt,unmounted:vt,activated:vt,deactivated:vt,errorCaptured:vt,serverPrefetch:vt,components:vr,directives:vr,watch:xy,provide:Wu,inject:Cy};function Wu(e,t){return t?e?function(){return Qe(be(e)?e.call(this,this):e,be(t)?t.call(this,this):t)}:t:e}function Cy(e,t){return vr($o(e),$o(t))}function $o(e){if(le(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function vt(e,t){return e?[...new Set([].concat(e,t))]:t}function vr(e,t){return e?Qe(Object.create(null),e,t):t}function qu(e,t){return e?le(e)&&le(t)?[...new Set([...e,...t])]:Qe(Object.create(null),zu(e),zu(t??{})):t}function xy(e,t){if(!e)return t;if(!t)return e;const n=Qe(Object.create(null),e);for(const s in t)n[s]=vt(e[s],t[s]);return n}function Nf(){return{app:null,config:{isNativeTag:Yv,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let _y=0;function Ey(e,t){return function(s,r=null){be(s)||(s=Qe({},s)),r!=null&&!Le(r)&&(r=null);const a=Nf(),i=new Set;let o=!1;const l=a.app={_uid:_y++,_component:s,_props:r,_container:null,_context:a,_instance:null,version:Qy,get config(){return a.config},set config(u){},use(u,...c){return i.has(u)||(u&&be(u.install)?(i.add(u),u.install(l,...c)):be(u)&&(i.add(u),u(l,...c))),l},mixin(u){return a.mixins.includes(u)||a.mixins.push(u),l},component(u,c){return c?(a.components[u]=c,l):a.components[u]},directive(u,c){return c?(a.directives[u]=c,l):a.directives[u]},mount(u,c,d){if(!o){const f=m(s,r);return f.appContext=a,c&&t?t(f,u):e(f,u,d),o=!0,l._container=u,u.__vue_app__=l,Di(f.component)||f.component.proxy}},unmount(){o&&(e(null,l._container),delete l._container.__vue_app__)},provide(u,c){return a.provides[u]=c,l},runWithContext(u){Fr=l;try{return u()}finally{Fr=null}}};return l}}let Fr=null;function _t(e,t){if(at){let n=at.provides;const s=at.parent&&at.parent.provides;s===n&&(n=at.provides=Object.create(s)),n[e]=t}}function je(e,t,n=!1){const s=at||Ct;if(s||Fr){const r=s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:Fr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&be(t)?t.call(s&&s.proxy):t}}function Ty(){return!!(at||Ct||Fr)}function ky(e,t,n,s=!1){const r={},a={};Za(a,Mi,1),e.propsDefaults=Object.create(null),Ff(e,t,r,a);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:$g(r):e.type.props?e.props=r:e.props=a,e.attrs=a}function Oy(e,t,n,s){const{props:r,attrs:a,vnode:{patchFlag:i}}=e,o=ye(r),[l]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let f=c[d];if(Ii(e.emitsOptions,f))continue;const h=t[f];if(l)if(Oe(a,f))h!==a[f]&&(a[f]=h,u=!0);else{const v=Ht(f);r[v]=Bo(l,o,v,h,e,!1)}else h!==a[f]&&(a[f]=h,u=!0)}}}else{Ff(e,t,r,a)&&(u=!0);let c;for(const d in o)(!t||!Oe(t,d)&&((c=Ss(d))===d||!Oe(t,c)))&&(l?n&&(n[d]!==void 0||n[c]!==void 0)&&(r[d]=Bo(l,o,d,void 0,e,!0)):delete r[d]);if(a!==o)for(const d in a)(!t||!Oe(t,d))&&(delete a[d],u=!0)}u&&pn(e,"set","$attrs")}function Ff(e,t,n,s){const[r,a]=e.propsOptions;let i=!1,o;if(t)for(let l in t){if(Ma(l))continue;const u=t[l];let c;r&&Oe(r,c=Ht(l))?!a||!a.includes(c)?n[c]=u:(o||(o={}))[c]=u:Ii(e.emitsOptions,l)||(!(l in s)||u!==s[l])&&(s[l]=u,i=!0)}if(a){const l=ye(n),u=o||Ue;for(let c=0;c<a.length;c++){const d=a[c];n[d]=Bo(r,l,d,u[d],e,!Oe(u,d))}}return i}function Bo(e,t,n,s,r,a){const i=e[n];if(i!=null){const o=Oe(i,"default");if(o&&s===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&be(l)){const{propsDefaults:u}=r;n in u?s=u[n]:(js(r),s=u[n]=l.call(null,t),ys())}else s=l}i[0]&&(a&&!o?s=!1:i[1]&&(s===""||s===Ss(n))&&(s=!0))}return s}function Lf(e,t,n=!1){const s=t.propsCache,r=s.get(e);if(r)return r;const a=e.props,i={},o=[];let l=!1;if(!be(e)){const c=d=>{l=!0;const[f,h]=Lf(d,t,!0);Qe(i,f),h&&o.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!a&&!l)return Le(e)&&s.set(e,Rs),Rs;if(le(a))for(let c=0;c<a.length;c++){const d=Ht(a[c]);Zu(d)&&(i[d]=Ue)}else if(a)for(const c in a){const d=Ht(c);if(Zu(d)){const f=a[c],h=i[d]=le(f)||be(f)?{type:f}:Qe({},f);if(h){const v=Yu(Boolean,h.type),g=Yu(String,h.type);h[0]=v>-1,h[1]=g<0||v<g,(v>-1||Oe(h,"default"))&&o.push(d)}}}const u=[i,o];return Le(e)&&s.set(e,u),u}function Zu(e){return e[0]!=="$"}function Gu(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function Ku(e,t){return Gu(e)===Gu(t)}function Yu(e,t){return le(t)?t.findIndex(n=>Ku(n,e)):be(t)&&Ku(t,e)?0:-1}const Rf=e=>e[0]==="_"||e==="$stable",$l=e=>le(e)?e.map(nn):[nn(e)],Iy=(e,t,n)=>{if(t._n)return t;const s=F((...r)=>$l(t(...r)),n);return s._c=!1,s},$f=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Rf(r))continue;const a=e[r];if(be(a))t[r]=Iy(r,a,s);else if(a!=null){const i=$l(a);t[r]=()=>i}}},Bf=(e,t)=>{const n=$l(t);e.slots.default=()=>n},Ay=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=ye(t),Za(t,"_",n)):$f(t,e.slots={})}else e.slots={},t&&Bf(e,t);Za(e.slots,Mi,1)},Vy=(e,t,n)=>{const{vnode:s,slots:r}=e;let a=!0,i=Ue;if(s.shapeFlag&32){const o=t._;o?n&&o===1?a=!1:(Qe(r,t),!n&&o===1&&delete r._):(a=!t.$stable,$f(t,r)),i=t}else t&&(Bf(e,t),i={default:1});if(a)for(const o in r)!Rf(o)&&!(o in i)&&delete r[o]};function Ho(e,t,n,s,r=!1){if(le(e)){e.forEach((f,h)=>Ho(f,t&&(le(t)?t[h]:t),n,s,r));return}if(Na(s)&&!r)return;const a=s.shapeFlag&4?Di(s.component)||s.component.proxy:s.el,i=r?null:a,{i:o,r:l}=e,u=t&&t.r,c=o.refs===Ue?o.refs={}:o.refs,d=o.setupState;if(u!=null&&u!==l&&(Ge(u)?(c[u]=null,Oe(d,u)&&(d[u]=null)):Me(u)&&(u.value=null)),be(l))jn(l,o,12,[i,c]);else{const f=Ge(l),h=Me(l);if(f||h){const v=()=>{if(e.f){const g=f?Oe(d,l)?d[l]:c[l]:l.value;r?le(g)&&_l(g,a):le(g)?g.includes(a)||g.push(a):f?(c[l]=[a],Oe(d,l)&&(d[l]=c[l])):(l.value=[a],e.k&&(c[e.k]=l.value))}else f?(c[l]=i,Oe(d,l)&&(d[l]=i)):h&&(l.value=i,e.k&&(c[e.k]=i))};i?(v.id=-1,wt(v,n)):v()}}}const wt=ay;function Py(e){return My(e)}function My(e,t){const n=Po();n.__VUE__=!0;const{insert:s,remove:r,patchProp:a,createElement:i,createText:o,createComment:l,setText:u,setElementText:c,parentNode:d,nextSibling:f,setScopeId:h=Kt,insertStaticContent:v}=e,g=(p,x,V,R=null,L=null,G=null,q=!1,j=null,X=!!x.dynamicChildren)=>{if(p===x)return;p&&!us(p,x)&&(R=Z(p),Ee(p,L,G,!0),p=null),x.patchFlag===-2&&(X=!1,x.dynamicChildren=null);const{type:H,ref:ue,shapeFlag:ne}=x;switch(H){case Jr:b(p,x,V,R);break;case Yt:_(p,x,V,R);break;case oo:p==null&&E(x,V,R,q);break;case _e:M(p,x,V,R,L,G,q,j,X);break;default:ne&1?P(p,x,V,R,L,G,q,j,X):ne&6?B(p,x,V,R,L,G,q,j,X):(ne&64||ne&128)&&H.process(p,x,V,R,L,G,q,j,X,ae)}ue!=null&&L&&Ho(ue,p&&p.ref,G,x||p,!x)},b=(p,x,V,R)=>{if(p==null)s(x.el=o(x.children),V,R);else{const L=x.el=p.el;x.children!==p.children&&u(L,x.children)}},_=(p,x,V,R)=>{p==null?s(x.el=l(x.children||""),V,R):x.el=p.el},E=(p,x,V,R)=>{[p.el,p.anchor]=v(p.children,x,V,R,p.el,p.anchor)},w=({el:p,anchor:x},V,R)=>{let L;for(;p&&p!==x;)L=f(p),s(p,V,R),p=L;s(x,V,R)},k=({el:p,anchor:x})=>{let V;for(;p&&p!==x;)V=f(p),r(p),p=V;r(x)},P=(p,x,V,R,L,G,q,j,X)=>{q=q||x.type==="svg",p==null?O(x,V,R,L,G,q,j,X):S(p,x,L,G,q,j,X)},O=(p,x,V,R,L,G,q,j)=>{let X,H;const{type:ue,props:ne,shapeFlag:ce,transition:ve,dirs:xe}=p;if(X=p.el=i(p.type,G,ne&&ne.is,ne),ce&8?c(X,p.children):ce&16&&y(p.children,X,null,R,L,G&&ue!=="foreignObject",q,j),xe&&ns(p,null,R,"created"),T(X,p,p.scopeId,q,R),ne){for(const De in ne)De!=="value"&&!Ma(De)&&a(X,De,null,ne[De],G,p.children,R,L,W);"value"in ne&&a(X,"value",null,ne.value),(H=ne.onVnodeBeforeMount)&&en(H,R,p)}xe&&ns(p,null,R,"beforeMount");const $e=(!L||L&&!L.pendingBranch)&&ve&&!ve.persisted;$e&&ve.beforeEnter(X),s(X,x,V),((H=ne&&ne.onVnodeMounted)||$e||xe)&&wt(()=>{H&&en(H,R,p),$e&&ve.enter(X),xe&&ns(p,null,R,"mounted")},L)},T=(p,x,V,R,L)=>{if(V&&h(p,V),R)for(let G=0;G<R.length;G++)h(p,R[G]);if(L){let G=L.subTree;if(x===G){const q=L.vnode;T(p,q,q.scopeId,q.slotScopeIds,L.parent)}}},y=(p,x,V,R,L,G,q,j,X=0)=>{for(let H=X;H<p.length;H++){const ue=p[H]=j?Rn(p[H]):nn(p[H]);g(null,ue,x,V,R,L,G,q,j)}},S=(p,x,V,R,L,G,q)=>{const j=x.el=p.el;let{patchFlag:X,dynamicChildren:H,dirs:ue}=x;X|=p.patchFlag&16;const ne=p.props||Ue,ce=x.props||Ue;let ve;V&&ss(V,!1),(ve=ce.onVnodeBeforeUpdate)&&en(ve,V,x,p),ue&&ns(x,p,V,"beforeUpdate"),V&&ss(V,!0);const xe=L&&x.type!=="foreignObject";if(H?A(p.dynamicChildren,H,j,V,R,xe,G):q||Y(p,x,j,null,V,R,xe,G,!1),X>0){if(X&16)z(j,x,ne,ce,V,R,L);else if(X&2&&ne.class!==ce.class&&a(j,"class",null,ce.class,L),X&4&&a(j,"style",ne.style,ce.style,L),X&8){const $e=x.dynamicProps;for(let De=0;De<$e.length;De++){const tt=$e[De],zt=ne[tt],Ts=ce[tt];(Ts!==zt||tt==="value")&&a(j,tt,zt,Ts,L,p.children,V,R,W)}}X&1&&p.children!==x.children&&c(j,x.children)}else!q&&H==null&&z(j,x,ne,ce,V,R,L);((ve=ce.onVnodeUpdated)||ue)&&wt(()=>{ve&&en(ve,V,x,p),ue&&ns(x,p,V,"updated")},R)},A=(p,x,V,R,L,G,q)=>{for(let j=0;j<x.length;j++){const X=p[j],H=x[j],ue=X.el&&(X.type===_e||!us(X,H)||X.shapeFlag&70)?d(X.el):V;g(X,H,ue,null,R,L,G,q,!0)}},z=(p,x,V,R,L,G,q)=>{if(V!==R){if(V!==Ue)for(const j in V)!Ma(j)&&!(j in R)&&a(p,j,V[j],null,q,x.children,L,G,W);for(const j in R){if(Ma(j))continue;const X=R[j],H=V[j];X!==H&&j!=="value"&&a(p,j,H,X,q,x.children,L,G,W)}"value"in R&&a(p,"value",V.value,R.value)}},M=(p,x,V,R,L,G,q,j,X)=>{const H=x.el=p?p.el:o(""),ue=x.anchor=p?p.anchor:o("");let{patchFlag:ne,dynamicChildren:ce,slotScopeIds:ve}=x;ve&&(j=j?j.concat(ve):ve),p==null?(s(H,V,R),s(ue,V,R),y(x.children,V,ue,L,G,q,j,X)):ne>0&&ne&64&&ce&&p.dynamicChildren?(A(p.dynamicChildren,ce,V,L,G,q,j),(x.key!=null||L&&x===L.subTree)&&Bl(p,x,!0)):Y(p,x,V,ue,L,G,q,j,X)},B=(p,x,V,R,L,G,q,j,X)=>{x.slotScopeIds=j,p==null?x.shapeFlag&512?L.ctx.activate(x,V,R,q,X):N(x,V,R,L,G,q,X):te(p,x,X)},N=(p,x,V,R,L,G,q)=>{const j=p.component=jy(p,R,L);if(Vi(p)&&(j.ctx.renderer=ae),Wy(j),j.asyncDep){if(L&&L.registerDep(j,ee),!p.el){const X=j.subTree=m(Yt);_(null,X,x,V)}return}ee(j,p,x,V,L,G,q)},te=(p,x,V)=>{const R=x.component=p.component;if(ny(p,x,V))if(R.asyncDep&&!R.asyncResolved){se(R,x,V);return}else R.next=x,Gg(R.update),R.update();else x.el=p.el,R.vnode=x},ee=(p,x,V,R,L,G,q)=>{const j=()=>{if(p.isMounted){let{next:ue,bu:ne,u:ce,parent:ve,vnode:xe}=p,$e=ue,De;ss(p,!1),ue?(ue.el=xe.el,se(p,ue,q)):ue=xe,ne&&Da(ne),(De=ue.props&&ue.props.onVnodeBeforeUpdate)&&en(De,ve,ue,xe),ss(p,!0);const tt=ro(p),zt=p.subTree;p.subTree=tt,g(zt,tt,d(zt.el),Z(zt),p,L,G),ue.el=tt.el,$e===null&&sy(p,tt.el),ce&&wt(ce,L),(De=ue.props&&ue.props.onVnodeUpdated)&&wt(()=>en(De,ve,ue,xe),L)}else{let ue;const{el:ne,props:ce}=x,{bm:ve,m:xe,parent:$e}=p,De=Na(x);if(ss(p,!1),ve&&Da(ve),!De&&(ue=ce&&ce.onVnodeBeforeMount)&&en(ue,$e,x),ss(p,!0),ne&&An){const tt=()=>{p.subTree=ro(p),An(ne,p.subTree,p,L,null)};De?x.type.__asyncLoader().then(()=>!p.isUnmounted&&tt()):tt()}else{const tt=p.subTree=ro(p);g(null,tt,V,R,p,L,G),x.el=tt.el}if(xe&&wt(xe,L),!De&&(ue=ce&&ce.onVnodeMounted)){const tt=x;wt(()=>en(ue,$e,tt),L)}(x.shapeFlag&256||$e&&Na($e.vnode)&&$e.vnode.shapeFlag&256)&&p.a&&wt(p.a,L),p.isMounted=!0,x=V=R=null}},X=p.effect=new kl(j,()=>Dl(H),p.scope),H=p.update=()=>X.run();H.id=p.uid,ss(p,!0),H()},se=(p,x,V)=>{x.component=p;const R=p.vnode.props;p.vnode=x,p.next=null,Oy(p,x.props,R,V),Vy(p,x.children,V),Gs(),$u(),Ks()},Y=(p,x,V,R,L,G,q,j,X=!1)=>{const H=p&&p.children,ue=p?p.shapeFlag:0,ne=x.children,{patchFlag:ce,shapeFlag:ve}=x;if(ce>0){if(ce&128){me(H,ne,V,R,L,G,q,j,X);return}else if(ce&256){J(H,ne,V,R,L,G,q,j,X);return}}ve&8?(ue&16&&W(H,L,G),ne!==H&&c(V,ne)):ue&16?ve&16?me(H,ne,V,R,L,G,q,j,X):W(H,L,G,!0):(ue&8&&c(V,""),ve&16&&y(ne,V,R,L,G,q,j,X))},J=(p,x,V,R,L,G,q,j,X)=>{p=p||Rs,x=x||Rs;const H=p.length,ue=x.length,ne=Math.min(H,ue);let ce;for(ce=0;ce<ne;ce++){const ve=x[ce]=X?Rn(x[ce]):nn(x[ce]);g(p[ce],ve,V,null,L,G,q,j,X)}H>ue?W(p,L,G,!0,!1,ne):y(x,V,R,L,G,q,j,X,ne)},me=(p,x,V,R,L,G,q,j,X)=>{let H=0;const ue=x.length;let ne=p.length-1,ce=ue-1;for(;H<=ne&&H<=ce;){const ve=p[H],xe=x[H]=X?Rn(x[H]):nn(x[H]);if(us(ve,xe))g(ve,xe,V,null,L,G,q,j,X);else break;H++}for(;H<=ne&&H<=ce;){const ve=p[ne],xe=x[ce]=X?Rn(x[ce]):nn(x[ce]);if(us(ve,xe))g(ve,xe,V,null,L,G,q,j,X);else break;ne--,ce--}if(H>ne){if(H<=ce){const ve=ce+1,xe=ve<ue?x[ve].el:R;for(;H<=ce;)g(null,x[H]=X?Rn(x[H]):nn(x[H]),V,xe,L,G,q,j,X),H++}}else if(H>ce)for(;H<=ne;)Ee(p[H],L,G,!0),H++;else{const ve=H,xe=H,$e=new Map;for(H=xe;H<=ce;H++){const Et=x[H]=X?Rn(x[H]):nn(x[H]);Et.key!=null&&$e.set(Et.key,H)}let De,tt=0;const zt=ce-xe+1;let Ts=!1,ku=0;const ar=new Array(zt);for(H=0;H<zt;H++)ar[H]=0;for(H=ve;H<=ne;H++){const Et=p[H];if(tt>=zt){Ee(Et,L,G,!0);continue}let Qt;if(Et.key!=null)Qt=$e.get(Et.key);else for(De=xe;De<=ce;De++)if(ar[De-xe]===0&&us(Et,x[De])){Qt=De;break}Qt===void 0?Ee(Et,L,G,!0):(ar[Qt-xe]=H+1,Qt>=ku?ku=Qt:Ts=!0,g(Et,x[Qt],V,null,L,G,q,j,X),tt++)}const Ou=Ts?Dy(ar):Rs;for(De=Ou.length-1,H=zt-1;H>=0;H--){const Et=xe+H,Qt=x[Et],Iu=Et+1<ue?x[Et+1].el:R;ar[H]===0?g(null,Qt,V,Iu,L,G,q,j,X):Ts&&(De<0||H!==Ou[De]?fe(Qt,V,Iu,2):De--)}}},fe=(p,x,V,R,L=null)=>{const{el:G,type:q,transition:j,children:X,shapeFlag:H}=p;if(H&6){fe(p.component.subTree,x,V,R);return}if(H&128){p.suspense.move(x,V,R);return}if(H&64){q.move(p,x,V,ae);return}if(q===_e){s(G,x,V);for(let ne=0;ne<X.length;ne++)fe(X[ne],x,V,R);s(p.anchor,x,V);return}if(q===oo){w(p,x,V);return}if(R!==2&&H&1&&j)if(R===0)j.beforeEnter(G),s(G,x,V),wt(()=>j.enter(G),L);else{const{leave:ne,delayLeave:ce,afterLeave:ve}=j,xe=()=>s(G,x,V),$e=()=>{ne(G,()=>{xe(),ve&&ve()})};ce?ce(G,xe,$e):$e()}else s(G,x,V)},Ee=(p,x,V,R=!1,L=!1)=>{const{type:G,props:q,ref:j,children:X,dynamicChildren:H,shapeFlag:ue,patchFlag:ne,dirs:ce}=p;if(j!=null&&Ho(j,null,V,p,!0),ue&256){x.ctx.deactivate(p);return}const ve=ue&1&&ce,xe=!Na(p);let $e;if(xe&&($e=q&&q.onVnodeBeforeUnmount)&&en($e,x,p),ue&6)Mt(p.component,V,R);else{if(ue&128){p.suspense.unmount(V,R);return}ve&&ns(p,null,x,"beforeUnmount"),ue&64?p.type.remove(p,x,V,L,ae,R):H&&(G!==_e||ne>0&&ne&64)?W(H,x,V,!1,!0):(G===_e&&ne&384||!L&&ue&16)&&W(X,x,V),R&&Re(p)}(xe&&($e=q&&q.onVnodeUnmounted)||ve)&&wt(()=>{$e&&en($e,x,p),ve&&ns(p,null,x,"unmounted")},V)},Re=p=>{const{type:x,el:V,anchor:R,transition:L}=p;if(x===_e){ze(V,R);return}if(x===oo){k(p);return}const G=()=>{r(V),L&&!L.persisted&&L.afterLeave&&L.afterLeave()};if(p.shapeFlag&1&&L&&!L.persisted){const{leave:q,delayLeave:j}=L,X=()=>q(V,G);j?j(p.el,G,X):X()}else G()},ze=(p,x)=>{let V;for(;p!==x;)V=f(p),r(p),p=V;r(x)},Mt=(p,x,V)=>{const{bum:R,scope:L,update:G,subTree:q,um:j}=p;R&&Da(R),L.stop(),G&&(G.active=!1,Ee(q,p,x,V)),j&&wt(j,x),wt(()=>{p.isUnmounted=!0},x),x&&x.pendingBranch&&!x.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===x.pendingId&&(x.deps--,x.deps===0&&x.resolve())},W=(p,x,V,R=!1,L=!1,G=0)=>{for(let q=G;q<p.length;q++)Ee(p[q],x,V,R,L)},Z=p=>p.shapeFlag&6?Z(p.component.subTree):p.shapeFlag&128?p.suspense.next():f(p.anchor||p.el),$=(p,x,V)=>{p==null?x._vnode&&Ee(x._vnode,null,null,!0):g(x._vnode||null,p,x,null,null,null,V),$u(),bf(),x._vnode=p},ae={p:g,um:Ee,m:fe,r:Re,mt:N,mc:y,pc:Y,pbc:A,n:Z,o:e};let dn,An;return t&&([dn,An]=t(ae)),{render:$,hydrate:dn,createApp:Ey($,dn)}}function ss({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Bl(e,t,n=!1){const s=e.children,r=t.children;if(le(s)&&le(r))for(let a=0;a<s.length;a++){const i=s[a];let o=r[a];o.shapeFlag&1&&!o.dynamicChildren&&((o.patchFlag<=0||o.patchFlag===32)&&(o=r[a]=Rn(r[a]),o.el=i.el),n||Bl(i,o)),o.type===Jr&&(o.el=i.el)}}function Dy(e){const t=e.slice(),n=[0];let s,r,a,i,o;const l=e.length;for(s=0;s<l;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(a=0,i=n.length-1;a<i;)o=a+i>>1,e[n[o]]<u?a=o+1:i=o;u<e[n[a]]&&(a>0&&(t[s]=n[a-1]),n[a]=s)}}for(a=n.length,i=n[a-1];a-- >0;)n[a]=i,i=t[i];return n}const Ny=e=>e.__isTeleport,wr=e=>e&&(e.disabled||e.disabled===""),Ju=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Uo=(e,t)=>{const n=e&&e.to;return Ge(n)?t?t(n):null:n},Fy={__isTeleport:!0,process(e,t,n,s,r,a,i,o,l,u){const{mc:c,pc:d,pbc:f,o:{insert:h,querySelector:v,createText:g,createComment:b}}=u,_=wr(t.props);let{shapeFlag:E,children:w,dynamicChildren:k}=t;if(e==null){const P=t.el=g(""),O=t.anchor=g("");h(P,n,s),h(O,n,s);const T=t.target=Uo(t.props,v),y=t.targetAnchor=g("");T&&(h(y,T),i=i||Ju(T));const S=(A,z)=>{E&16&&c(w,A,z,r,a,i,o,l)};_?S(n,O):T&&S(T,y)}else{t.el=e.el;const P=t.anchor=e.anchor,O=t.target=e.target,T=t.targetAnchor=e.targetAnchor,y=wr(e.props),S=y?n:O,A=y?P:T;if(i=i||Ju(O),k?(f(e.dynamicChildren,k,S,r,a,i,o),Bl(e,t,!0)):l||d(e,t,S,A,r,a,i,o,!1),_)y||Sa(t,n,P,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const z=t.target=Uo(t.props,v);z&&Sa(t,z,null,u,0)}else y&&Sa(t,O,T,u,1)}Hf(t)},remove(e,t,n,s,{um:r,o:{remove:a}},i){const{shapeFlag:o,children:l,anchor:u,targetAnchor:c,target:d,props:f}=e;if(d&&a(c),(i||!wr(f))&&(a(u),o&16))for(let h=0;h<l.length;h++){const v=l[h];r(v,t,n,!0,!!v.dynamicChildren)}},move:Sa,hydrate:Ly};function Sa(e,t,n,{o:{insert:s},m:r},a=2){a===0&&s(e.targetAnchor,t,n);const{el:i,anchor:o,shapeFlag:l,children:u,props:c}=e,d=a===2;if(d&&s(i,t,n),(!d||wr(c))&&l&16)for(let f=0;f<u.length;f++)r(u[f],t,n,2);d&&s(o,t,n)}function Ly(e,t,n,s,r,a,{o:{nextSibling:i,parentNode:o,querySelector:l}},u){const c=t.target=Uo(t.props,l);if(c){const d=c._lpa||c.firstChild;if(t.shapeFlag&16)if(wr(t.props))t.anchor=u(i(e),t,o(e),n,s,r,a),t.targetAnchor=d;else{t.anchor=i(e);let f=d;for(;f;)if(f=i(f),f&&f.nodeType===8&&f.data==="teleport anchor"){t.targetAnchor=f,c._lpa=t.targetAnchor&&i(t.targetAnchor);break}u(d,t,c,n,s,r,a)}Hf(t)}return t.anchor&&i(t.anchor)}const Ry=Fy;function Hf(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;for(;n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}const _e=Symbol.for("v-fgt"),Jr=Symbol.for("v-txt"),Yt=Symbol.for("v-cmt"),oo=Symbol.for("v-stc"),Sr=[];let Zt=null;function Ae(e=!1){Sr.push(Zt=e?null:[])}function $y(){Sr.pop(),Zt=Sr[Sr.length-1]||null}let Lr=1;function Xu(e){Lr+=e}function Uf(e){return e.dynamicChildren=Lr>0?Zt||Rs:null,$y(),Lr>0&&Zt&&Zt.push(e),e}function St(e,t,n,s,r,a){return Uf(I(e,t,n,s,r,a,!0))}function ut(e,t,n,s,r){return Uf(m(e,t,n,s,r,!0))}function zo(e){return e?e.__v_isVNode===!0:!1}function us(e,t){return e.type===t.type&&e.key===t.key}const Mi="__vInternal",zf=({key:e})=>e??null,Fa=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ge(e)||Me(e)||be(e)?{i:Ct,r:e,k:t,f:!!n}:e:null);function I(e,t=null,n=null,s=0,r=null,a=e===_e?0:1,i=!1,o=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&zf(t),ref:t&&Fa(t),scopeId:Ai,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ct};return o?(Hl(l,n),a&128&&e.normalize(l)):n&&(l.shapeFlag|=Ge(n)?8:16),Lr>0&&!i&&Zt&&(l.patchFlag>0||a&6)&&l.patchFlag!==32&&Zt.push(l),l}const m=By;function By(e,t=null,n=null,s=0,r=null,a=!1){if((!e||e===Pf)&&(e=Yt),zo(e)){const o=wn(e,t,!0);return n&&Hl(o,n),Lr>0&&!a&&Zt&&(o.shapeFlag&6?Zt[Zt.indexOf(e)]=o:Zt.push(o)),o.patchFlag|=-2,o}if(Yy(e)&&(e=e.__vccOpts),t){t=Hy(t);let{class:o,style:l}=t;o&&!Ge(o)&&(t.class=Ar(o)),Le(l)&&(df(l)&&!le(l)&&(l=Qe({},l)),t.style=Gr(l))}const i=Ge(e)?1:ry(e)?128:Ny(e)?64:Le(e)?4:be(e)?2:0;return I(e,t,n,s,r,i,a,!0)}function Hy(e){return e?df(e)||Mi in e?Qe({},e):e:null}function wn(e,t,n=!1){const{props:s,ref:r,patchFlag:a,children:i}=e,o=t?ge(s||{},t):s;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:o,key:o&&zf(o),ref:t&&t.ref?n&&r?le(r)?r.concat(Fa(t)):[r,Fa(t)]:Fa(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==_e?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&wn(e.ssContent),ssFallback:e.ssFallback&&wn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Pe(e=" ",t=0){return m(Jr,null,e,t)}function tn(e="",t=!1){return t?(Ae(),ut(Yt,null,e)):m(Yt,null,e)}function nn(e){return e==null||typeof e=="boolean"?m(Yt):le(e)?m(_e,null,e.slice()):typeof e=="object"?Rn(e):m(Jr,null,String(e))}function Rn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:wn(e)}function Hl(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(le(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Hl(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!(Mi in t)?t._ctx=Ct:r===3&&Ct&&(Ct.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else be(t)?(t={default:t,_ctx:Ct},n=32):(t=String(t),s&64?(n=16,t=[Pe(t)]):n=8);e.children=t,e.shapeFlag|=n}function ge(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Ar([t.class,s.class]));else if(r==="style")t.style=Gr([t.style,s.style]);else if(Si(r)){const a=t[r],i=s[r];i&&a!==i&&!(le(a)&&a.includes(i))&&(t[r]=a?[].concat(a,i):i)}else r!==""&&(t[r]=s[r])}return t}function en(e,t,n,s=null){$t(e,t,7,[n,s])}const Uy=Nf();let zy=0;function jy(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Uy,a={uid:zy++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new Xd(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Lf(s,r),emitsOptions:Sf(s,r),emit:null,emitted:null,propsDefaults:Ue,inheritAttrs:s.inheritAttrs,ctx:Ue,data:Ue,props:Ue,attrs:Ue,slots:Ue,refs:Ue,setupState:Ue,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return a.ctx={_:a},a.root=t?t.root:a,a.emit=Jg.bind(null,a),e.ce&&e.ce(a),a}let at=null;const Ul=()=>at||Ct;let zl,ks,Qu="__VUE_INSTANCE_SETTERS__";(ks=Po()[Qu])||(ks=Po()[Qu]=[]),ks.push(e=>at=e),zl=e=>{ks.length>1?ks.forEach(t=>t(e)):ks[0](e)};const js=e=>{zl(e),e.scope.on()},ys=()=>{at&&at.scope.off(),zl(null)};function jf(e){return e.vnode.shapeFlag&4}let Rr=!1;function Wy(e,t=!1){Rr=t;const{props:n,children:s}=e.vnode,r=jf(e);ky(e,n,r,t),Ay(e,s);const a=r?qy(e,t):void 0;return Rr=!1,a}function qy(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Ti(new Proxy(e.ctx,py));const{setup:s}=n;if(s){const r=e.setupContext=s.length>1?Gy(e):null;js(e),Gs();const a=jn(s,e,0,[e.props,r]);if(Ks(),ys(),Zd(a)){if(a.then(ys,ys),t)return a.then(i=>{ec(e,i,t)}).catch(i=>{Oi(i,e,0)});e.asyncDep=a}else ec(e,a,t)}else Wf(e,t)}function ec(e,t,n){be(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Le(t)&&(e.setupState=vf(t)),Wf(e,n)}let tc;function Wf(e,t,n){const s=e.type;if(!e.render){if(!t&&tc&&!s.render){const r=s.template||Rl(e).template;if(r){const{isCustomElement:a,compilerOptions:i}=e.appContext.config,{delimiters:o,compilerOptions:l}=s,u=Qe(Qe({isCustomElement:a,delimiters:o},i),l);s.render=tc(r,u)}}e.render=s.render||Kt}js(e),Gs(),by(e),Ks(),ys()}function Zy(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return xt(e,"get","$attrs"),t[n]}}))}function Gy(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return Zy(e)},slots:e.slots,emit:e.emit,expose:t}}function Di(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(vf(Ti(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in br)return br[n](e)},has(t,n){return n in t||n in br}}))}function Ky(e,t=!0){return be(e)?e.displayName||e.name:e.name||t&&e.__name}function Yy(e){return be(e)&&"__vccOpts"in e}const C=(e,t)=>Wg(e,t,Rr);function Kn(e,t,n){const s=arguments.length;return s===2?Le(t)&&!le(t)?zo(t)?m(e,null,[t]):m(e,t):m(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&zo(n)&&(n=[n]),m(e,t,n))}const Jy=Symbol.for("v-scx"),Xy=()=>je(Jy),Qy="3.3.4",ep="http://www.w3.org/2000/svg",cs=typeof document<"u"?document:null,nc=cs&&cs.createElement("template"),tp={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t?cs.createElementNS(ep,e):cs.createElement(e,n?{is:n}:void 0);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>cs.createTextNode(e),createComment:e=>cs.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>cs.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,a){const i=n?n.previousSibling:t.lastChild;if(r&&(r===a||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===a||!(r=r.nextSibling)););else{nc.innerHTML=s?`<svg>${e}</svg>`:e;const o=nc.content;if(s){const l=o.firstChild;for(;l.firstChild;)o.appendChild(l.firstChild);o.removeChild(l)}t.insertBefore(o,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function np(e,t,n){const s=e._vtc;s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function sp(e,t,n){const s=e.style,r=Ge(n);if(n&&!r){if(t&&!Ge(t))for(const a in t)n[a]==null&&jo(s,a,"");for(const a in n)jo(s,a,n[a])}else{const a=s.display;r?t!==n&&(s.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(s.display=a)}}const sc=/\s*!important$/;function jo(e,t,n){if(le(n))n.forEach(s=>jo(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=rp(e,t);sc.test(n)?e.setProperty(Ss(s),n.replace(sc,""),"important"):e[s]=n}}const rc=["Webkit","Moz","ms"],lo={};function rp(e,t){const n=lo[t];if(n)return n;let s=Ht(t);if(s!=="filter"&&s in e)return lo[t]=s;s=xn(s);for(let r=0;r<rc.length;r++){const a=rc[r]+s;if(a in e)return lo[t]=a}return t}const ac="http://www.w3.org/1999/xlink";function ap(e,t,n,s,r){if(s&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(ac,t.slice(6,t.length)):e.setAttributeNS(ac,t,n);else{const a=lg(t);n==null||a&&!Yd(n)?e.removeAttribute(t):e.setAttribute(t,a?"":n)}}function ip(e,t,n,s,r,a,i){if(t==="innerHTML"||t==="textContent"){s&&i(s,r,a),e[t]=n??"";return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){e._value=n;const u=o==="OPTION"?e.getAttribute("value"):e.value,c=n??"";u!==c&&(e.value=c),n==null&&e.removeAttribute(t);return}let l=!1;if(n===""||n==null){const u=typeof e[t];u==="boolean"?n=Yd(n):n==null&&u==="string"?(n="",l=!0):u==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(t)}function ds(e,t,n,s){e.addEventListener(t,n,s)}function op(e,t,n,s){e.removeEventListener(t,n,s)}function lp(e,t,n,s,r=null){const a=e._vei||(e._vei={}),i=a[t];if(s&&i)i.value=s;else{const[o,l]=up(t);if(s){const u=a[t]=fp(s,r);ds(e,o,u,l)}else i&&(op(e,o,i,l),a[t]=void 0)}}const ic=/(?:Once|Passive|Capture)$/;function up(e){let t;if(ic.test(e)){t={};let s;for(;s=e.match(ic);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Ss(e.slice(2)),t]}let uo=0;const cp=Promise.resolve(),dp=()=>uo||(cp.then(()=>uo=0),uo=Date.now());function fp(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;$t(mp(s,n.value),t,5,[s])};return n.value=e,n.attached=dp(),n}function mp(e,t){if(le(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const oc=/^on[a-z]/,hp=(e,t,n,s,r=!1,a,i,o,l)=>{t==="class"?np(e,s,r):t==="style"?sp(e,n,s):Si(t)?xl(t)||lp(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):vp(e,t,s,r))?ip(e,t,s,a,i,o,l):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ap(e,t,s,r))};function vp(e,t,n,s){return s?!!(t==="innerHTML"||t==="textContent"||t in e&&oc.test(t)&&be(n)):t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA"||oc.test(t)&&Ge(n)?!1:t in e}const Pn="transition",ir="animation",Sn=(e,{slots:t})=>Kn(ly,Zf(e),t);Sn.displayName="Transition";const qf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},gp=Sn.props=Qe({},_f,qf),rs=(e,t=[])=>{le(e)?e.forEach(n=>n(...t)):e&&e(...t)},lc=e=>e?le(e)?e.some(t=>t.length>1):e.length>1:!1;function Zf(e){const t={};for(const M in e)M in qf||(t[M]=e[M]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:a=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:o=`${n}-enter-to`,appearFromClass:l=a,appearActiveClass:u=i,appearToClass:c=o,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,v=yp(r),g=v&&v[0],b=v&&v[1],{onBeforeEnter:_,onEnter:E,onEnterCancelled:w,onLeave:k,onLeaveCancelled:P,onBeforeAppear:O=_,onAppear:T=E,onAppearCancelled:y=w}=t,S=(M,B,N)=>{Fn(M,B?c:o),Fn(M,B?u:i),N&&N()},A=(M,B)=>{M._isLeaving=!1,Fn(M,d),Fn(M,h),Fn(M,f),B&&B()},z=M=>(B,N)=>{const te=M?T:E,ee=()=>S(B,M,N);rs(te,[B,ee]),uc(()=>{Fn(B,M?l:a),fn(B,M?c:o),lc(te)||cc(B,s,g,ee)})};return Qe(t,{onBeforeEnter(M){rs(_,[M]),fn(M,a),fn(M,i)},onBeforeAppear(M){rs(O,[M]),fn(M,l),fn(M,u)},onEnter:z(!1),onAppear:z(!0),onLeave(M,B){M._isLeaving=!0;const N=()=>A(M,B);fn(M,d),Kf(),fn(M,f),uc(()=>{M._isLeaving&&(Fn(M,d),fn(M,h),lc(k)||cc(M,s,b,N))}),rs(k,[M,N])},onEnterCancelled(M){S(M,!1),rs(w,[M])},onAppearCancelled(M){S(M,!0),rs(y,[M])},onLeaveCancelled(M){A(M),rs(P,[M])}})}function yp(e){if(e==null)return null;if(Le(e))return[co(e.enter),co(e.leave)];{const t=co(e);return[t,t]}}function co(e){return ng(e)}function fn(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e._vtc||(e._vtc=new Set)).add(t)}function Fn(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function uc(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let pp=0;function cc(e,t,n,s){const r=e._endId=++pp,a=()=>{r===e._endId&&s()};if(n)return setTimeout(a,n);const{type:i,timeout:o,propCount:l}=Gf(e,t);if(!i)return s();const u=i+"end";let c=0;const d=()=>{e.removeEventListener(u,f),a()},f=h=>{h.target===e&&++c>=l&&d()};setTimeout(()=>{c<l&&d()},o+1),e.addEventListener(u,f)}function Gf(e,t){const n=window.getComputedStyle(e),s=v=>(n[v]||"").split(", "),r=s(`${Pn}Delay`),a=s(`${Pn}Duration`),i=dc(r,a),o=s(`${ir}Delay`),l=s(`${ir}Duration`),u=dc(o,l);let c=null,d=0,f=0;t===Pn?i>0&&(c=Pn,d=i,f=a.length):t===ir?u>0&&(c=ir,d=u,f=l.length):(d=Math.max(i,u),c=d>0?i>u?Pn:ir:null,f=c?c===Pn?a.length:l.length:0);const h=c===Pn&&/\b(transform|all)(,|$)/.test(s(`${Pn}Property`).toString());return{type:c,timeout:d,propCount:f,hasTransform:h}}function dc(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>fc(n)+fc(e[s])))}function fc(e){return Number(e.slice(0,-1).replace(",","."))*1e3}function Kf(){return document.body.offsetHeight}const Yf=new WeakMap,Jf=new WeakMap,Xf={name:"TransitionGroup",props:Qe({},gp,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Ul(),s=xf();let r,a;return If(()=>{if(!r.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!_p(r[0].el,n.vnode.el,i))return;r.forEach(Sp),r.forEach(Cp);const o=r.filter(xp);Kf(),o.forEach(l=>{const u=l.el,c=u.style;fn(u,i),c.transform=c.webkitTransform=c.transitionDuration="";const d=u._moveCb=f=>{f&&f.target!==u||(!f||/transform$/.test(f.propertyName))&&(u.removeEventListener("transitionend",d),u._moveCb=null,Fn(u,i))};u.addEventListener("transitionend",d)})}),()=>{const i=ye(e),o=Zf(i);let l=i.tag||_e;r=a,a=t.default?Fl(t.default()):[];for(let u=0;u<a.length;u++){const c=a[u];c.key!=null&&Nr(c,Dr(c,o,s,n))}if(r)for(let u=0;u<r.length;u++){const c=r[u];Nr(c,Dr(c,o,s,n)),Yf.set(c,c.el.getBoundingClientRect())}return m(l,null,a)}}},bp=e=>delete e.mode;Xf.props;const wp=Xf;function Sp(e){const t=e.el;t._moveCb&&t._moveCb(),t._enterCb&&t._enterCb()}function Cp(e){Jf.set(e,e.el.getBoundingClientRect())}function xp(e){const t=Yf.get(e),n=Jf.get(e),s=t.left-n.left,r=t.top-n.top;if(s||r){const a=e.el.style;return a.transform=a.webkitTransform=`translate(${s}px,${r}px)`,a.transitionDuration="0s",e}}function _p(e,t,n){const s=e.cloneNode();e._vtc&&e._vtc.forEach(i=>{i.split(/\s+/).forEach(o=>o&&s.classList.remove(o))}),n.split(/\s+/).forEach(i=>i&&s.classList.add(i)),s.style.display="none";const r=t.nodeType===1?t:t.parentNode;r.appendChild(s);const{hasTransform:a}=Gf(s);return r.removeChild(s),a}const Qa=e=>{const t=e.props["onUpdate:modelValue"]||!1;return le(t)?n=>Da(t,n):t};function Ep(e){e.target.composing=!0}function mc(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Os={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e._assign=Qa(r);const a=s||r.props&&r.props.type==="number";ds(e,t?"change":"input",i=>{if(i.target.composing)return;let o=e.value;n&&(o=o.trim()),a&&(o=Ga(o)),e._assign(o)}),n&&ds(e,"change",()=>{e.value=e.value.trim()}),t||(ds(e,"compositionstart",Ep),ds(e,"compositionend",mc),ds(e,"change",mc))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:s,number:r}},a){if(e._assign=Qa(a),e.composing||document.activeElement===e&&e.type!=="range"&&(n||s&&e.value.trim()===t||(r||e.type==="number")&&Ga(e.value)===t))return;const i=t??"";e.value!==i&&(e.value=i)}},Tp={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Ci(t);ds(e,"change",()=>{const a=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?Ga(ei(i)):ei(i));e._assign(e.multiple?r?new Set(a):a:a[0])}),e._assign=Qa(s)},mounted(e,{value:t}){hc(e,t)},beforeUpdate(e,t,n){e._assign=Qa(n)},updated(e,{value:t}){hc(e,t)}};function hc(e,t){const n=e.multiple;if(!(n&&!le(t)&&!Ci(t))){for(let s=0,r=e.options.length;s<r;s++){const a=e.options[s],i=ei(a);if(n)le(t)?a.selected=cg(t,i)>-1:a.selected=t.has(i);else if(_i(ei(a),t)){e.selectedIndex!==s&&(e.selectedIndex=s);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function ei(e){return"_value"in e?e._value:e.value}const kp={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Ca=(e,t)=>n=>{if(!("key"in n))return;const s=Ss(n.key);if(t.some(r=>r===s||kp[r]===s))return e(n)},Ys={beforeMount(e,{value:t},{transition:n}){e._vod=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):or(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),or(e,!0),s.enter(e)):s.leave(e,()=>{or(e,!1)}):or(e,t))},beforeUnmount(e,{value:t}){or(e,t)}};function or(e,t){e.style.display=t?e._vod:"none"}const Op=Qe({patchProp:hp},tp);let vc;function Ip(){return vc||(vc=Py(Op))}const Ap=(...e)=>{const t=Ip().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Vp(s);if(!r)return;const a=t._component;!be(a)&&!a.render&&!a.template&&(a.template=r.innerHTML),r.innerHTML="";const i=n(r,!1,r instanceof SVGElement);return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Vp(e){return Ge(e)?document.querySelector(e):e}var Pp=!1;/*!
  * pinia v2.1.4
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */let Qf;const Ni=e=>Qf=e,em=Symbol();function Wo(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Cr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Cr||(Cr={}));function Mp(){const e=Kr(!0),t=e.run(()=>U({}));let n=[],s=[];const r=Ti({install(a){Ni(r),r._a=a,a.provide(em,r),a.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(a){return!this._a&&!Pp?s.push(a):n.push(a),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const tm=()=>{};function gc(e,t,n,s=tm){e.push(t);const r=()=>{const a=e.indexOf(t);a>-1&&(e.splice(a,1),s())};return!n&&Qd()&&pt(r),r}function Is(e,...t){e.slice().forEach(n=>{n(...t)})}const Dp=e=>e();function qo(e,t){e instanceof Map&&t instanceof Map&&t.forEach((n,s)=>e.set(s,n)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];Wo(r)&&Wo(s)&&e.hasOwnProperty(n)&&!Me(s)&&!gn(s)?e[n]=qo(r,s):e[n]=s}return e}const Np=Symbol();function Fp(e){return!Wo(e)||!e.hasOwnProperty(Np)}const{assign:Ln}=Object;function Lp(e){return!!(Me(e)&&e.effect)}function Rp(e,t,n,s){const{state:r,actions:a,getters:i}=t,o=n.state.value[e];let l;function u(){o||(n.state.value[e]=r?r():{});const c=ki(n.state.value[e]);return Ln(c,a,Object.keys(i||{}).reduce((d,f)=>(d[f]=Ti(C(()=>{Ni(n);const h=n._s.get(e);return i[f].call(h,h)})),d),{}))}return l=nm(e,u,t,n,s,!0),l}function nm(e,t,n={},s,r,a){let i;const o=Ln({actions:{}},n),l={deep:!0};let u,c,d=[],f=[],h;const v=s.state.value[e];!a&&!v&&(s.state.value[e]={}),U({});let g;function b(y){let S;u=c=!1,typeof y=="function"?(y(s.state.value[e]),S={type:Cr.patchFunction,storeId:e,events:h}):(qo(s.state.value[e],y),S={type:Cr.patchObject,payload:y,storeId:e,events:h});const A=g=Symbol();bt().then(()=>{g===A&&(u=!0)}),c=!0,Is(d,S,s.state.value[e])}const _=a?function(){const{state:S}=n,A=S?S():{};this.$patch(z=>{Ln(z,A)})}:tm;function E(){i.stop(),d=[],f=[],s._s.delete(e)}function w(y,S){return function(){Ni(s);const A=Array.from(arguments),z=[],M=[];function B(ee){z.push(ee)}function N(ee){M.push(ee)}Is(f,{args:A,name:y,store:P,after:B,onError:N});let te;try{te=S.apply(this&&this.$id===e?this:P,A)}catch(ee){throw Is(M,ee),ee}return te instanceof Promise?te.then(ee=>(Is(z,ee),ee)).catch(ee=>(Is(M,ee),Promise.reject(ee))):(Is(z,te),te)}}const k={_p:s,$id:e,$onAction:gc.bind(null,f),$patch:b,$reset:_,$subscribe(y,S={}){const A=gc(d,y,S.detached,()=>z()),z=i.run(()=>we(()=>s.state.value[e],M=>{(S.flush==="sync"?c:u)&&y({storeId:e,type:Cr.direct,events:h},M)},Ln({},l,S)));return A},$dispose:E},P=ct(k);s._s.set(e,P);const O=s._a&&s._a.runWithContext||Dp,T=s._e.run(()=>(i=Kr(),O(()=>i.run(t))));for(const y in T){const S=T[y];if(Me(S)&&!Lp(S)||gn(S))a||(v&&Fp(S)&&(Me(S)?S.value=v[y]:qo(S,v[y])),s.state.value[e][y]=S);else if(typeof S=="function"){const A=w(y,S);T[y]=A,o.actions[y]=S}}return Ln(P,T),Ln(ye(P),T),Object.defineProperty(P,"$state",{get:()=>s.state.value[e],set:y=>{b(S=>{Ln(S,y)})}}),s._p.forEach(y=>{Ln(P,i.run(()=>y({store:P,app:s._a,pinia:s,options:o})))}),v&&a&&n.hydrate&&n.hydrate(P.$state,v),u=!0,c=!0,P}function $p(e,t,n){let s,r;const a=typeof t=="function";typeof e=="string"?(s=e,r=a?n:t):(r=e,s=e.id);function i(o,l){const u=Ty();return o=o||(u?je(em,null):null),o&&Ni(o),o=Qf,o._s.has(s)||(a?nm(s,t,r,o):Rp(s,r,o)),o._s.get(s)}return i.$id=s,i}function Bp(e){{e=ye(e);const t={};for(const n in e){const s=e[n];(Me(s)||gn(s))&&(t[n]=he(e,n))}return t}}const Fi=$p({id:"snackbar",state:()=>({snackbar:!1,timeout:3e3,msg:"--",color:"success",icon:null}),getters:{},actions:{async success(e){this.color="success",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-check-circle"},async error(e){this.color="error",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-close-circle"},async warning(e){console.log("pinia warnign sanckbar"),this.color="warning",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-alert-circle"},async info(e){this.color="info",this.msg=e.msg||"--",this.timeout=!!e.tiempo||5e3,this.snackbar=!0,this.icon="mdi-information"},async show(e){console.log("ACTION MOSTRAR SNACKBAR",e),this.color=e.color||"success",this.msg=e.msg||"prueba---",this.timeout=!!e.tiempo||3e3,this.snackbar=!0},async hide(){this.snackbar=!1,this.msg="",this.color="success",this.icon=""}}});function sm(e,t,n){const s=t.length-1;if(s<0)return e===void 0?n:e;for(let r=0;r<s;r++){if(e==null)return n;e=e[t[r]]}return e==null||e[t[s]]===void 0?n:e[t[s]]}function Js(e,t){if(e===t)return!0;if(e instanceof Date&&t instanceof Date&&e.getTime()!==t.getTime()||e!==Object(e)||t!==Object(t))return!1;const n=Object.keys(e);return n.length!==Object.keys(t).length?!1:n.every(s=>Js(e[s],t[s]))}function Zo(e,t,n){return e==null||!t||typeof t!="string"?n:e[t]!==void 0?e[t]:(t=t.replace(/\[(\w+)\]/g,".$1"),t=t.replace(/^\./,""),sm(e,t.split("."),n))}function hn(e,t,n){if(t==null)return e===void 0?n:e;if(e!==Object(e)){if(typeof t!="function")return n;const r=t(e,n);return typeof r>"u"?n:r}if(typeof t=="string")return Zo(e,t,n);if(Array.isArray(t))return sm(e,t,n);if(typeof t!="function")return n;const s=t(e,n);return typeof s>"u"?n:s}function jl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Array.from({length:e},(n,s)=>t+s)}function de(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"px";if(!(e==null||e===""))return isNaN(+e)?String(e):isFinite(+e)?`${Number(e)}${t}`:void 0}function Go(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Ko(e){return e&&"$el"in e?e.$el:e}const yc=Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16});function fo(e,t){return t.every(n=>e.hasOwnProperty(n))}function Xr(e,t,n){const s=Object.create(null),r=Object.create(null);for(const a in e)t.some(i=>i instanceof RegExp?i.test(a):i===a)&&!(n!=null&&n.some(i=>i===a))?s[a]=e[a]:r[a]=e[a];return[s,r]}function Qr(e,t){const n={...e};return t.forEach(s=>delete n[s]),n}function Wl(e){return Xr(e,["class","style","id",/^data-/])}function Wn(e){return e==null?[]:Array.isArray(e)?e:[e]}function $r(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;return Math.max(t,Math.min(n,e))}function pc(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0";return e+n.repeat(Math.max(0,t-e.length))}function Hp(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const n=[];let s=0;for(;s<e.length;)n.push(e.substr(s,t)),s+=t;return n}function Lt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const s={};for(const r in e)s[r]=e[r];for(const r in t){const a=e[r],i=t[r];if(Go(a)&&Go(i)){s[r]=Lt(a,i,n);continue}if(Array.isArray(a)&&Array.isArray(i)&&n){s[r]=n(a,i);continue}s[r]=i}return s}function rm(e){return e.map(t=>t.type===_e?rm(t.children):t).flat()}function ps(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";if(ps.cache.has(e))return ps.cache.get(e);const t=e.replace(/[^a-z]/gi,"-").replace(/\B([A-Z])/g,"-$1").toLowerCase();return ps.cache.set(e,t),t}ps.cache=new Map;function xr(e,t){if(!t||typeof t!="object")return[];if(Array.isArray(t))return t.map(n=>xr(e,n)).flat(1);if(Array.isArray(t.children))return t.children.map(n=>xr(e,n)).flat(1);if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component];if(t.component.subTree)return xr(e,t.component.subTree).flat(1)}return[]}function ql(e){const t=ct({}),n=C(e);return _n(()=>{for(const s in n.value)t[s]=n.value[s]},{flush:"sync"}),ki(t)}function ti(e,t){return e.includes(t)}const Up=/^on[^a-z]/,Zl=e=>Up.test(e);function am(e){return e[2].toLowerCase()+e.slice(3)}const an=()=>[Function,Array];function bc(e,t){return t="on"+xn(t),!!(e[t]||e[`${t}Once`]||e[`${t}Capture`]||e[`${t}OnceCapture`]||e[`${t}CaptureOnce`])}function zp(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];if(Array.isArray(e))for(const r of e)r(...n);else typeof e=="function"&&e(...n)}function im(e){const t=["button","[href]",'input:not([type="hidden"])',"select","textarea","[tabindex]"].map(n=>`${n}:not([tabindex="-1"]):not([disabled])`).join(", ");return[...e.querySelectorAll(t)]}function ni(e,t){var r,a,i;const n=im(e),s=n.indexOf(document.activeElement);if(!t)(e===document.activeElement||!e.contains(document.activeElement))&&((r=n[0])==null||r.focus());else if(t==="first")(a=n[0])==null||a.focus();else if(t==="last")(i=n.at(-1))==null||i.focus();else{let o,l=s;const u=t==="next"?1:-1;do l+=u,o=n[l];while((!o||o.offsetParent==null)&&l<n.length&&l>=0);o?o.focus():ni(e,t==="next"?"first":"last")}}const om=["top","bottom"],jp=["start","end","left","right"];function Yo(e,t){let[n,s]=e.split(" ");return s||(s=ti(om,n)?"start":ti(jp,n)?"top":"center"),{side:wc(n,t),align:wc(s,t)}}function wc(e,t){return e==="start"?t?"right":"left":e==="end"?t?"left":"right":e}function mo(e){return{side:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.side],align:e.align}}function ho(e){return{side:e.side,align:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.align]}}function Sc(e){return{side:e.align,align:e.side}}function Cc(e){return ti(om,e.side)?"y":"x"}class Hs{constructor(t){let{x:n,y:s,width:r,height:a}=t;this.x=n,this.y=s,this.width=r,this.height=a}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}}function xc(e,t){return{x:{before:Math.max(0,t.left-e.left),after:Math.max(0,e.right-t.right)},y:{before:Math.max(0,t.top-e.top),after:Math.max(0,e.bottom-t.bottom)}}}function Gl(e){const t=e.getBoundingClientRect(),n=getComputedStyle(e),s=n.transform;if(s){let r,a,i,o,l;if(s.startsWith("matrix3d("))r=s.slice(9,-1).split(/, /),a=+r[0],i=+r[5],o=+r[12],l=+r[13];else if(s.startsWith("matrix("))r=s.slice(7,-1).split(/, /),a=+r[0],i=+r[3],o=+r[4],l=+r[5];else return new Hs(t);const u=n.transformOrigin,c=t.x-o-(1-a)*parseFloat(u),d=t.y-l-(1-i)*parseFloat(u.slice(u.indexOf(" ")+1)),f=a?t.width/a:e.offsetWidth+1,h=i?t.height/i:e.offsetHeight+1;return new Hs({x:c,y:d,width:f,height:h})}else return new Hs(t)}function Fs(e,t,n){if(typeof e.animate>"u")return{finished:Promise.resolve()};let s;try{s=e.animate(t,n)}catch{return{finished:Promise.resolve()}}return typeof s.finished>"u"&&(s.finished=new Promise(r=>{s.onfinish=()=>{r(s)}})),s}const La=new WeakMap;function Wp(e,t){Object.keys(t).forEach(n=>{if(Zl(n)){const s=am(n),r=La.get(e);if(t[n]==null)r==null||r.forEach(a=>{const[i,o]=a;i===s&&(e.removeEventListener(s,o),r.delete(a))});else if(!r||![...r].some(a=>a[0]===s&&a[1]===t[n])){e.addEventListener(s,t[n]);const a=r||new Set;a.add([s,t[n]]),La.has(e)||La.set(e,a)}}else t[n]==null?e.removeAttribute(n):e.setAttribute(n,t[n])})}function qp(e,t){Object.keys(t).forEach(n=>{if(Zl(n)){const s=am(n),r=La.get(e);r==null||r.forEach(a=>{const[i,o]=a;i===s&&(e.removeEventListener(s,o),r.delete(a))})}else e.removeAttribute(n)})}function Zp(e,t){t=Array.isArray(t)?t.slice(0,-1).map(n=>`'${n}'`).join(", ")+` or '${t.at(-1)}'`:`'${t}'`}const si=.20689655172413793,Gp=e=>e>si**3?Math.cbrt(e):e/(3*si**2)+4/29,Kp=e=>e>si?e**3:3*si**2*(e-4/29);function lm(e){const t=Gp,n=t(e[1]);return[116*n-16,500*(t(e[0]/.95047)-n),200*(n-t(e[2]/1.08883))]}function um(e){const t=Kp,n=(e[0]+16)/116;return[t(n+e[1]/500)*.95047,t(n),t(n-e[2]/200)*1.08883]}const Yp=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],Jp=e=>e<=.0031308?e*12.92:1.055*e**(1/2.4)-.055,Xp=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],Qp=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function cm(e){const t=Array(3),n=Jp,s=Yp;for(let r=0;r<3;++r)t[r]=Math.round($r(n(s[r][0]*e[0]+s[r][1]*e[1]+s[r][2]*e[2]))*255);return{r:t[0],g:t[1],b:t[2]}}function Kl(e){let{r:t,g:n,b:s}=e;const r=[0,0,0],a=Qp,i=Xp;t=a(t/255),n=a(n/255),s=a(s/255);for(let o=0;o<3;++o)r[o]=i[o][0]*t+i[o][1]*n+i[o][2]*s;return r}function _c(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}const Ec=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,eb={rgb:(e,t,n,s)=>({r:e,g:t,b:n,a:s}),rgba:(e,t,n,s)=>({r:e,g:t,b:n,a:s}),hsl:(e,t,n,s)=>Tc({h:e,s:t,l:n,a:s}),hsla:(e,t,n,s)=>Tc({h:e,s:t,l:n,a:s}),hsv:(e,t,n,s)=>Br({h:e,s:t,v:n,a:s}),hsva:(e,t,n,s)=>Br({h:e,s:t,v:n,a:s})};function ms(e){if(typeof e=="number")return{r:(e&16711680)>>16,g:(e&65280)>>8,b:e&255};if(typeof e=="string"&&Ec.test(e)){const{groups:t}=e.match(Ec),{fn:n,values:s}=t,r=s.split(/,\s*/).map(a=>a.endsWith("%")&&["hsl","hsla","hsv","hsva"].includes(n)?parseFloat(a)/100:parseFloat(a));return eb[n](...r)}else if(typeof e=="string"){let t=e.startsWith("#")?e.slice(1):e;return[3,4].includes(t.length)?t=t.split("").map(n=>n+n).join(""):[6,8].includes(t.length),nb(t)}else if(typeof e=="object"){if(fo(e,["r","g","b"]))return e;if(fo(e,["h","s","l"]))return Br(dm(e));if(fo(e,["h","s","v"]))return Br(e)}throw new TypeError(`Invalid color: ${e==null?e:String(e)||e.constructor.name}
Expected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function Br(e){const{h:t,s:n,v:s,a:r}=e,a=o=>{const l=(o+t/60)%6;return s-s*n*Math.max(Math.min(l,4-l,1),0)},i=[a(5),a(3),a(1)].map(o=>Math.round(o*255));return{r:i[0],g:i[1],b:i[2],a:r}}function Tc(e){return Br(dm(e))}function dm(e){const{h:t,s:n,l:s,a:r}=e,a=s+n*Math.min(s,1-s),i=a===0?0:2-2*s/a;return{h:t,s:i,v:a,a:r}}function xa(e){const t=Math.round(e).toString(16);return("00".substr(0,2-t.length)+t).toUpperCase()}function tb(e){let{r:t,g:n,b:s,a:r}=e;return`#${[xa(t),xa(n),xa(s),r!==void 0?xa(Math.round(r*255)):""].join("")}`}function nb(e){e=sb(e);let[t,n,s,r]=Hp(e,2).map(a=>parseInt(a,16));return r=r===void 0?r:r/255,{r:t,g:n,b:s,a:r}}function sb(e){return e.startsWith("#")&&(e=e.slice(1)),e=e.replace(/([^0-9a-f])/gi,"F"),(e.length===3||e.length===4)&&(e=e.split("").map(t=>t+t).join("")),e.length!==6&&(e=pc(pc(e,6),8,"F")),e}function rb(e,t){const n=lm(Kl(e));return n[0]=n[0]+t*10,cm(um(n))}function ab(e,t){const n=lm(Kl(e));return n[0]=n[0]-t*10,cm(um(n))}function ib(e){const t=ms(e);return Kl(t)[1]}function K(e,t){return n=>Object.keys(e).reduce((s,r)=>{const i=typeof e[r]=="object"&&e[r]!=null&&!Array.isArray(e[r])?e[r]:{type:e[r]};return n&&r in n?s[r]={...i,default:n[r]}:s[r]=i,t&&!s[r].source&&(s[r].source=t),s},{})}const Se=K({class:[String,Array],style:{type:[String,Array,Object],default:null}},"component");function Zn(e,t){let n;function s(){n=Kr(),n.run(()=>t.length?t(()=>{n==null||n.stop(),s()}):t())}we(e,r=>{r&&!n?s():r||(n==null||n.stop(),n=void 0)},{immediate:!0}),pt(()=>{n==null||n.stop()})}const Hr=Symbol.for("vuetify:defaults");function ob(e){return U(e)}function Yl(){const e=je(Hr);if(!e)throw new Error("[Vuetify] Could not find defaults instance");return e}function Yn(e,t){const n=Yl(),s=U(e),r=C(()=>{if(rt(t==null?void 0:t.disabled))return n.value;const i=rt(t==null?void 0:t.scoped),o=rt(t==null?void 0:t.reset),l=rt(t==null?void 0:t.root);let u=Lt(s.value,{prev:n.value});if(i)return u;if(o||l){const c=Number(o||1/0);for(let d=0;d<=c&&!(!u||!("prev"in u));d++)u=u.prev;return u&&typeof l=="string"&&l in u&&(u=Lt(Lt(u,{prev:u}),u[l])),u}return u.prev?Lt(u.prev,u):u});return _t(Hr,r),r}function lb(e,t){var n,s;return typeof((n=e.props)==null?void 0:n[t])<"u"||typeof((s=e.props)==null?void 0:s[ps(t)])<"u"}function ub(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Yl();const s=dt("useDefaults");if(t=t??s.type.name??s.type.__name,!t)throw new Error("[Vuetify] Could not determine component name");const r=C(()=>{var l;return(l=n.value)==null?void 0:l[e._as??t]}),a=new Proxy(e,{get(l,u){var d,f,h,v;const c=Reflect.get(l,u);return u==="class"||u==="style"?[(d=r.value)==null?void 0:d[u],c].filter(g=>g!=null):typeof u=="string"&&!lb(s.vnode,u)?((f=r.value)==null?void 0:f[u])??((v=(h=n.value)==null?void 0:h.global)==null?void 0:v[u])??c:c}}),i=Ce();_n(()=>{if(r.value){const l=Object.entries(r.value).filter(u=>{let[c]=u;return c.startsWith(c[0].toUpperCase())});l.length&&(i.value=Object.fromEntries(l))}});function o(){Zn(i,()=>{var l;Yn(Lt(((l=hb(Hr))==null?void 0:l.value)??{},i.value))})}return{props:a,provideSubDefaults:o}}function Xs(e){if(e._setup=e._setup??e.setup,!e.name)return e;if(e._setup){e.props=K(e.props??{},e.name)();const t=Object.keys(e.props);e.filterProps=function(s){return Xr(s,t,["class","style"])},e.props._as=String,e.setup=function(s,r){const a=Yl();if(!a.value)return e._setup(s,r);const{props:i,provideSubDefaults:o}=ub(s,s._as??e.name,a),l=e._setup(i,r);return o(),l}}return e}function oe(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return t=>(e?Xs:uy)(t)}function Cs(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"div",n=arguments.length>2?arguments[2]:void 0;return oe()({name:n??xn(Ht(e.replace(/__/g,"-"))),props:{tag:{type:String,default:t},...Se()},setup(s,r){let{slots:a}=r;return()=>{var i;return Kn(s.tag,{class:[e,s.class],style:s.style},(i=a.default)==null?void 0:i.call(a))}}})}function fm(e){if(typeof e.getRootNode!="function"){for(;e.parentNode;)e=e.parentNode;return e!==document?null:document}const t=e.getRootNode();return t!==document&&t.getRootNode({composed:!0})!==document?null:t}const ri="cubic-bezier(0.4, 0, 0.2, 1)",cb="cubic-bezier(0.0, 0, 0.2, 1)",db="cubic-bezier(0.4, 0, 1, 1)";function dt(e,t){const n=Ul();if(!n)throw new Error(`[Vuetify] ${e} ${t||"must be called from inside a setup function"}`);return n}function kn(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"composables";const t=dt(e).type;return ps((t==null?void 0:t.aliasName)||(t==null?void 0:t.name))}let mm=0,Ra=new WeakMap;function It(){const e=dt("getUid");if(Ra.has(e))return Ra.get(e);{const t=mm++;return Ra.set(e,t),t}}It.reset=()=>{mm=0,Ra=new WeakMap};function hm(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(t?fb(e):Jl(e))return e;e=e.parentElement}return document.scrollingElement}function ai(e,t){const n=[];if(t&&e&&!t.contains(e))return n;for(;e&&(Jl(e)&&n.push(e),e!==t);)e=e.parentElement;return n}function Jl(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return t.overflowY==="scroll"||t.overflowY==="auto"&&e.scrollHeight>e.clientHeight}function fb(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return["scroll","auto"].includes(t.overflowY)}const st=typeof window<"u",Xl=st&&"IntersectionObserver"in window,mb=st&&("ontouchstart"in window||window.navigator.maxTouchPoints>0),Jo=st&&typeof CSS<"u"&&typeof CSS.supports<"u"&&CSS.supports("selector(:focus-visible)");function hb(e){const{provides:t}=dt("injectSelf");if(t&&e in t)return t[e]}function vb(e){for(;e;){if(window.getComputedStyle(e).position==="fixed")return!0;e=e.offsetParent}return!1}function pe(e){const t=dt("useRender");t.render=e}const Jn=K({border:[Boolean,Number,String]},"border");function Xn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();return{borderClasses:C(()=>{const s=Me(e)?e.value:e.border,r=[];if(s===!0||s==="")r.push(`${t}--border`);else if(typeof s=="string"||s===0)for(const a of String(s).split(" "))r.push(`border-${a}`);return r})}}const gb=[null,"default","comfortable","compact"],Ut=K({density:{type:String,default:"default",validator:e=>gb.includes(e)}},"density");function Xt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();return{densityClasses:C(()=>`${t}--density-${e.density}`)}}const On=K({elevation:{type:[Number,String],validator(e){const t=parseInt(e);return!isNaN(t)&&t>=0&&t<=24}}},"elevation");function In(e){return{elevationClasses:C(()=>{const n=Me(e)?e.value:e.elevation,s=[];return n==null||s.push(`elevation-${n}`),s})}}const At=K({rounded:{type:[Boolean,Number,String],default:void 0}},"rounded");function Vt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();return{roundedClasses:C(()=>{const s=Me(e)?e.value:e.rounded,r=[];if(s===!0||s==="")r.push(`${t}--rounded`);else if(typeof s=="string"||s===0)for(const a of String(s).split(" "))r.push(`rounded-${a}`);return r})}}const Ye=K({tag:{type:String,default:"div"}},"tag"),As=2.4,kc=.2126729,Oc=.7151522,Ic=.072175,yb=.55,pb=.58,bb=.57,wb=.62,_a=.03,Ac=1.45,Sb=5e-4,Cb=1.25,xb=1.25,Vc=.078,Pc=12.82051282051282,Ea=.06,Mc=.001;function Dc(e,t){const n=(e.r/255)**As,s=(e.g/255)**As,r=(e.b/255)**As,a=(t.r/255)**As,i=(t.g/255)**As,o=(t.b/255)**As;let l=n*kc+s*Oc+r*Ic,u=a*kc+i*Oc+o*Ic;if(l<=_a&&(l+=(_a-l)**Ac),u<=_a&&(u+=(_a-u)**Ac),Math.abs(u-l)<Sb)return 0;let c;if(u>l){const d=(u**yb-l**pb)*Cb;c=d<Mc?0:d<Vc?d-d*Pc*Ea:d-Ea}else{const d=(u**wb-l**bb)*xb;c=d>-Mc?0:d>-Vc?d-d*Pc*Ea:d+Ea}return c*100}const ii=Symbol.for("vuetify:theme"),We=K({theme:String},"theme"),lr={defaultTheme:"light",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#6200EE","primary-darken-1":"#3700B3",secondary:"#03DAC6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-variant":"#BDBDBD","on-surface-variant":"#424242",primary:"#BB86FC","primary-darken-1":"#3700B3",secondary:"#03DAC5","secondary-darken-1":"#03DAC5",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}}};function _b(){var n,s;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:lr;if(!e)return{...lr,isDisabled:!0};const t={};for(const[r,a]of Object.entries(e.themes??{})){const i=a.dark||r==="dark"?(n=lr.themes)==null?void 0:n.dark:(s=lr.themes)==null?void 0:s.light;t[r]=Lt(i,a)}return Lt(lr,{...e,themes:t})}function Eb(e){const t=_b(e),n=U(t.defaultTheme),s=U(t.themes),r=C(()=>{const c={};for(const[d,f]of Object.entries(s.value)){const h=c[d]={...f,colors:{...f.colors}};if(t.variations)for(const v of t.variations.colors){const g=h.colors[v];if(g)for(const b of["lighten","darken"]){const _=b==="lighten"?rb:ab;for(const E of jl(t.variations[b],1))h.colors[`${v}-${b}-${E}`]=tb(_(ms(g),E))}}for(const v of Object.keys(h.colors)){if(/^on-[a-z]/.test(v)||h.colors[`on-${v}`])continue;const g=`on-${v}`,b=ms(h.colors[v]),_=Math.abs(Dc(ms(0),b)),E=Math.abs(Dc(ms(16777215),b));h.colors[g]=E>Math.min(_,50)?"#fff":"#000"}}return c}),a=C(()=>r.value[n.value]),i=C(()=>{const c=[];a.value.dark&&as(c,":root",["color-scheme: dark"]),as(c,":root",Nc(a.value));for(const[v,g]of Object.entries(r.value))as(c,`.v-theme--${v}`,[`color-scheme: ${g.dark?"dark":"normal"}`,...Nc(g)]);const d=[],f=[],h=new Set(Object.values(r.value).flatMap(v=>Object.keys(v.colors)));for(const v of h)/^on-[a-z]/.test(v)?as(f,`.${v}`,[`color: rgb(var(--v-theme-${v})) !important`]):(as(d,`.bg-${v}`,[`--v-theme-overlay-multiplier: var(--v-theme-${v}-overlay-multiplier)`,`background-color: rgb(var(--v-theme-${v})) !important`,`color: rgb(var(--v-theme-on-${v})) !important`]),as(f,`.text-${v}`,[`color: rgb(var(--v-theme-${v})) !important`]),as(f,`.border-${v}`,[`--v-border-color: var(--v-theme-${v})`]));return c.push(...d,...f),c.map((v,g)=>g===0?v:`    ${v}`).join("")});function o(){return{style:[{children:i.value,id:"vuetify-theme-stylesheet",nonce:t.cspNonce||!1}]}}function l(c){if(t.isDisabled)return;const d=c._context.provides.usehead;if(d)if(d.push){const f=d.push(o);we(i,()=>{f.patch(o)})}else st?(d.addHeadObjs(C(o)),_n(()=>d.updateDOM())):d.addHeadObjs(o());else{let h=function(){if(typeof document<"u"&&!f){const v=document.createElement("style");v.type="text/css",v.id="vuetify-theme-stylesheet",t.cspNonce&&v.setAttribute("nonce",t.cspNonce),f=v,document.head.appendChild(f)}f&&(f.innerHTML=i.value)},f=st?document.getElementById("vuetify-theme-stylesheet"):null;we(i,h,{immediate:!0})}}const u=C(()=>t.isDisabled?void 0:`v-theme--${n.value}`);return{install:l,isDisabled:t.isDisabled,name:n,themes:s,current:a,computedThemes:r,themeClasses:u,styles:i,global:{name:n,current:a}}}function et(e){dt("provideTheme");const t=je(ii,null);if(!t)throw new Error("Could not find Vuetify theme injection");const n=C(()=>e.theme??(t==null?void 0:t.name.value)),s=C(()=>t.isDisabled?void 0:`v-theme--${n.value}`),r={...t,name:n,themeClasses:s};return _t(ii,r),r}function as(e,t,n){e.push(`${t} {
`,...n.map(s=>`  ${s};
`),`}
`)}function Nc(e){const t=e.dark?2:1,n=e.dark?1:2,s=[];for(const[r,a]of Object.entries(e.colors)){const i=ms(a);s.push(`--v-theme-${r}: ${i.r},${i.g},${i.b}`),r.startsWith("on-")||s.push(`--v-theme-${r}-overlay-multiplier: ${ib(a)>.18?t:n}`)}for(const[r,a]of Object.entries(e.variables)){const i=typeof a=="string"&&a.startsWith("#")?ms(a):void 0,o=i?`${i.r}, ${i.g}, ${i.b}`:void 0;s.push(`--v-${r}: ${o??a}`)}return s}function Ql(e){return ql(()=>{const t=[],n={};return e.value.background&&(_c(e.value.background)?n.backgroundColor=e.value.background:t.push(`bg-${e.value.background}`)),e.value.text&&(_c(e.value.text)?(n.color=e.value.text,n.caretColor=e.value.text):t.push(`text-${e.value.text}`)),{colorClasses:t,colorStyles:n}})}function on(e,t){const n=C(()=>({text:Me(e)?e.value:t?e[t]:null})),{colorClasses:s,colorStyles:r}=Ql(n);return{textColorClasses:s,textColorStyles:r}}function ws(e,t){const n=C(()=>({background:Me(e)?e.value:t?e[t]:null})),{colorClasses:s,colorStyles:r}=Ql(n);return{backgroundColorClasses:s,backgroundColorStyles:r}}const Tb=["elevated","flat","tonal","outlined","text","plain"];function xs(e,t){return m(_e,null,[e&&m("span",{key:"overlay",class:`${t}__overlay`},null),m("span",{key:"underlay",class:`${t}__underlay`},null)])}const un=K({color:String,variant:{type:String,default:"elevated",validator:e=>Tb.includes(e)}},"variant");function _s(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();const n=C(()=>{const{variant:a}=rt(e);return`${t}--variant-${a}`}),{colorClasses:s,colorStyles:r}=Ql(C(()=>{const{variant:a,color:i}=rt(e);return{[["elevated","flat"].includes(a)?"background":"text"]:i}}));return{colorClasses:s,colorStyles:r,variantClasses:n}}const vm=K({divided:Boolean,...Jn(),...Se(),...Ut(),...On(),...At(),...Ye(),...We(),...un()},"VBtnGroup"),Fc=oe()({name:"VBtnGroup",props:vm(),setup(e,t){let{slots:n}=t;const{themeClasses:s}=et(e),{densityClasses:r}=Xt(e),{borderClasses:a}=Xn(e),{elevationClasses:i}=In(e),{roundedClasses:o}=Vt(e);Yn({VBtn:{height:"auto",color:he(e,"color"),density:he(e,"density"),flat:!0,variant:he(e,"variant")}}),pe(()=>m(e.tag,{class:["v-btn-group",{"v-btn-group--divided":e.divided},s.value,a.value,r.value,i.value,o.value,e.class],style:e.style},n))}});function Ke(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:d=>d,r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:d=>d;const a=dt("useProxiedModel"),i=U(e[t]!==void 0?e[t]:n),o=ps(t),u=C(o!==t?()=>{var d,f,h,v;return e[t],!!(((d=a.vnode.props)!=null&&d.hasOwnProperty(t)||(f=a.vnode.props)!=null&&f.hasOwnProperty(o))&&((h=a.vnode.props)!=null&&h.hasOwnProperty(`onUpdate:${t}`)||(v=a.vnode.props)!=null&&v.hasOwnProperty(`onUpdate:${o}`)))}:()=>{var d,f;return e[t],!!((d=a.vnode.props)!=null&&d.hasOwnProperty(t)&&((f=a.vnode.props)!=null&&f.hasOwnProperty(`onUpdate:${t}`)))});Zn(()=>!u.value,()=>{we(()=>e[t],d=>{i.value=d})});const c=C({get(){const d=e[t];return s(u.value?d:i.value)},set(d){const f=r(d),h=ye(u.value?e[t]:i.value);h===f||s(h)===d||(i.value=f,a==null||a.emit(`update:${t}`,f))}});return Object.defineProperty(c,"externalValue",{get:()=>u.value?e[t]:i.value}),c}const eu=K({modelValue:{type:null,default:void 0},multiple:Boolean,mandatory:[Boolean,String],max:Number,selectedClass:String,disabled:Boolean},"group"),tu=K({value:null,disabled:Boolean,selectedClass:String},"group-item");function nu(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;const s=dt("useGroupItem");if(!s)throw new Error("[Vuetify] useGroupItem composable must be used inside a component setup function");const r=It();_t(Symbol.for(`${t.description}:id`),r);const a=je(t,null);if(!a){if(!n)return a;throw new Error(`[Vuetify] Could not find useGroup injection with symbol ${t.description}`)}const i=he(e,"value"),o=C(()=>a.disabled.value||e.disabled);a.register({id:r,value:i,disabled:o},s),ln(()=>{a.unregister(r)});const l=C(()=>a.isSelected(r)),u=C(()=>l.value&&[a.selectedClass.value,e.selectedClass]);return we(l,c=>{s.emit("group:selected",{value:c})}),{id:r,isSelected:l,toggle:()=>a.select(r,!l.value),select:c=>a.select(r,c),selectedClass:u,value:i,disabled:o,group:a}}function su(e,t){let n=!1;const s=ct([]),r=Ke(e,"modelValue",[],f=>f==null?[]:gm(s,Wn(f)),f=>{const h=Ob(s,f);return e.multiple?h:h[0]}),a=dt("useGroup");function i(f,h){const v=f,g=Symbol.for(`${t.description}:id`),_=xr(g,a==null?void 0:a.vnode).indexOf(h);_>-1?s.splice(_,0,v):s.push(v)}function o(f){if(n)return;l();const h=s.findIndex(v=>v.id===f);s.splice(h,1)}function l(){const f=s.find(h=>!h.disabled);f&&e.mandatory==="force"&&!r.value.length&&(r.value=[f.id])}Tn(()=>{l()}),ln(()=>{n=!0});function u(f,h){const v=s.find(g=>g.id===f);if(!(h&&(v!=null&&v.disabled)))if(e.multiple){const g=r.value.slice(),b=g.findIndex(E=>E===f),_=~b;if(h=h??!_,_&&e.mandatory&&g.length<=1||!_&&e.max!=null&&g.length+1>e.max)return;b<0&&h?g.push(f):b>=0&&!h&&g.splice(b,1),r.value=g}else{const g=r.value.includes(f);if(e.mandatory&&g)return;r.value=h??!g?[f]:[]}}function c(f){if(e.multiple,r.value.length){const h=r.value[0],v=s.findIndex(_=>_.id===h);let g=(v+f)%s.length,b=s[g];for(;b.disabled&&g!==v;)g=(g+f)%s.length,b=s[g];if(b.disabled)return;r.value=[s[g].id]}else{const h=s.find(v=>!v.disabled);h&&(r.value=[h.id])}}const d={register:i,unregister:o,selected:r,select:u,disabled:he(e,"disabled"),prev:()=>c(s.length-1),next:()=>c(1),isSelected:f=>r.value.includes(f),selectedClass:C(()=>e.selectedClass),items:C(()=>s),getItemIndex:f=>kb(s,f)};return _t(t,d),d}function kb(e,t){const n=gm(e,[t]);return n.length?e.findIndex(s=>s.id===n[0]):-1}function gm(e,t){const n=[];return t.forEach(s=>{const r=e.find(i=>Js(s,i.value)),a=e[s];(r==null?void 0:r.value)!=null?n.push(r.id):a!=null&&n.push(a.id)}),n}function Ob(e,t){const n=[];return t.forEach(s=>{const r=e.findIndex(a=>a.id===s);if(~r){const a=e[r];n.push(a.value!=null?a.value:r)}}),n}const ym=Symbol.for("vuetify:v-btn-toggle"),Ib=K({...vm(),...eu()},"VBtnToggle");oe()({name:"VBtnToggle",props:Ib(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{isSelected:s,next:r,prev:a,select:i,selected:o}=su(e,ym);return pe(()=>{const[l]=Fc.filterProps(e);return m(Fc,ge({class:["v-btn-toggle",e.class]},l,{style:e.style}),{default:()=>{var u;return[(u=n.default)==null?void 0:u.call(n,{isSelected:s,next:r,prev:a,select:i,selected:o})]}})}),{next:r,prev:a,select:i}}});const Ab=K({defaults:Object,disabled:Boolean,reset:[Number,String],root:[Boolean,String],scoped:Boolean},"VDefaultsProvider"),Ze=oe(!1)({name:"VDefaultsProvider",props:Ab(),setup(e,t){let{slots:n}=t;const{defaults:s,disabled:r,reset:a,root:i,scoped:o}=ki(e);return Yn(s,{reset:a,root:i,scoped:o,disabled:r}),()=>{var l;return(l=n.default)==null?void 0:l.call(n)}}});const Vb={collapse:"mdi-chevron-up",complete:"mdi-check",cancel:"mdi-close-circle",close:"mdi-close",delete:"mdi-close-circle",clear:"mdi-close-circle",success:"mdi-check-circle",info:"mdi-information",warning:"mdi-alert-circle",error:"mdi-close-circle",prev:"mdi-chevron-left",next:"mdi-chevron-right",checkboxOn:"mdi-checkbox-marked",checkboxOff:"mdi-checkbox-blank-outline",checkboxIndeterminate:"mdi-minus-box",delimiter:"mdi-circle",sortAsc:"mdi-arrow-up",sortDesc:"mdi-arrow-down",expand:"mdi-chevron-down",menu:"mdi-menu",subgroup:"mdi-menu-down",dropdown:"mdi-menu-down",radioOn:"mdi-radiobox-marked",radioOff:"mdi-radiobox-blank",edit:"mdi-pencil",ratingEmpty:"mdi-star-outline",ratingFull:"mdi-star",ratingHalf:"mdi-star-half-full",loading:"mdi-cached",first:"mdi-page-first",last:"mdi-page-last",unfold:"mdi-unfold-more-horizontal",file:"mdi-paperclip",plus:"mdi-plus",minus:"mdi-minus",calendar:"mdi-calendar"},Pb={component:e=>Kn(bm,{...e,class:"mdi"})},Fe=[String,Function,Object,Array],Xo=Symbol.for("vuetify:icons"),Li=K({icon:{type:Fe},tag:{type:String,required:!0}},"icon"),Lc=oe()({name:"VComponentIcon",props:Li(),setup(e,t){let{slots:n}=t;return()=>{const s=e.icon;return m(e.tag,null,{default:()=>{var r;return[e.icon?m(s,null,null):(r=n.default)==null?void 0:r.call(n)]}})}}}),pm=Xs({name:"VSvgIcon",inheritAttrs:!1,props:Li(),setup(e,t){let{attrs:n}=t;return()=>m(e.tag,ge(n,{style:null}),{default:()=>[m("svg",{class:"v-icon__svg",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true"},[Array.isArray(e.icon)?e.icon.map(s=>Array.isArray(s)?m("path",{d:s[0],"fill-opacity":s[1]},null):m("path",{d:s},null)):m("path",{d:e.icon},null)])]})}});Xs({name:"VLigatureIcon",props:Li(),setup(e){return()=>m(e.tag,null,{default:()=>[e.icon]})}});const bm=Xs({name:"VClassIcon",props:Li(),setup(e){return()=>m(e.tag,{class:e.icon},null)}}),Mb={svg:{component:pm},class:{component:bm}};function Db(e){return Lt({defaultSet:"mdi",sets:{...Mb,mdi:Pb},aliases:{...Vb,vuetify:["M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z",["M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z",.6]],"vuetify-outline":"svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z"}},e)}const Nb=e=>{const t=je(Xo);if(!t)throw new Error("Missing Vuetify Icons provide!");return{iconData:C(()=>{var l;const s=rt(e);if(!s)return{component:Lc};let r=s;if(typeof r=="string"&&(r=r.trim(),r.startsWith("$")&&(r=(l=t.aliases)==null?void 0:l[r.slice(1)])),!r)throw new Error(`Could not find aliased icon "${s}"`);if(Array.isArray(r))return{component:pm,icon:r};if(typeof r!="string")return{component:Lc,icon:r};const a=Object.keys(t.sets).find(u=>typeof r=="string"&&r.startsWith(`${u}:`)),i=a?r.slice(a.length+1):r;return{component:t.sets[a??t.defaultSet].component,icon:i}})}},Fb=["x-small","small","default","large","x-large"],ea=K({size:{type:[String,Number],default:"default"}},"size");function ta(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();return ql(()=>{let n,s;return ti(Fb,e.size)?n=`${t}--size-${e.size}`:e.size&&(s={width:de(e.size),height:de(e.size)}),{sizeClasses:n,sizeStyles:s}})}const Lb=K({color:String,start:Boolean,end:Boolean,icon:Fe,...Se(),...ea(),...Ye({tag:"i"}),...We()},"VIcon"),it=oe()({name:"VIcon",props:Lb(),setup(e,t){let{attrs:n,slots:s}=t;const r=U(),{themeClasses:a}=et(e),{iconData:i}=Nb(C(()=>r.value||e.icon)),{sizeClasses:o}=ta(e),{textColorClasses:l,textColorStyles:u}=on(he(e,"color"));return pe(()=>{var d,f;const c=(d=s.default)==null?void 0:d.call(s);return c&&(r.value=(f=rm(c).filter(h=>h.type===Jr&&h.children&&typeof h.children=="string")[0])==null?void 0:f.children),m(i.value.component,{tag:e.tag,icon:i.value.icon,class:["v-icon","notranslate",a.value,o.value,l.value,{"v-icon--clickable":!!n.onClick,"v-icon--start":e.start,"v-icon--end":e.end},e.class],style:[o.value?void 0:{fontSize:de(e.size),height:de(e.size),width:de(e.size)},u.value,e.style],role:n.onClick?"button":void 0,"aria-hidden":!n.onClick},{default:()=>[c]})}),{}}});function wm(e,t){const n=U(),s=Ce(!1);if(Xl){const r=new IntersectionObserver(a=>{e==null||e(a,r),s.value=!!a.find(i=>i.isIntersecting)},t);ln(()=>{r.disconnect()}),we(n,(a,i)=>{i&&(r.unobserve(i),s.value=!1),a&&r.observe(a)},{flush:"post"})}return{intersectionRef:n,isIntersecting:s}}function na(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"content";const n=U(),s=U();if(st){const r=new ResizeObserver(a=>{e==null||e(a,r),a.length&&(t==="content"?s.value=a[0].contentRect:s.value=a[0].target.getBoundingClientRect())});ln(()=>{r.disconnect()}),we(n,(a,i)=>{i&&(r.unobserve(Ko(i)),s.value=void 0),a&&r.observe(Ko(a))},{flush:"post"})}return{resizeRef:n,contentRect:Yr(s)}}const Rb=K({bgColor:String,color:String,indeterminate:[Boolean,String],modelValue:{type:[Number,String],default:0},rotate:{type:[Number,String],default:0},width:{type:[Number,String],default:4},...Se(),...ea(),...Ye({tag:"div"}),...We()},"VProgressCircular"),$b=oe()({name:"VProgressCircular",props:Rb(),setup(e,t){let{slots:n}=t;const s=20,r=2*Math.PI*s,a=U(),{themeClasses:i}=et(e),{sizeClasses:o,sizeStyles:l}=ta(e),{textColorClasses:u,textColorStyles:c}=on(he(e,"color")),{textColorClasses:d,textColorStyles:f}=on(he(e,"bgColor")),{intersectionRef:h,isIntersecting:v}=wm(),{resizeRef:g,contentRect:b}=na(),_=C(()=>Math.max(0,Math.min(100,parseFloat(e.modelValue)))),E=C(()=>Number(e.width)),w=C(()=>l.value?Number(e.size):b.value?b.value.width:Math.max(E.value,32)),k=C(()=>s/(1-E.value/w.value)*2),P=C(()=>E.value/w.value*k.value),O=C(()=>de((100-_.value)/100*r));return _n(()=>{h.value=a.value,g.value=a.value}),pe(()=>m(e.tag,{ref:a,class:["v-progress-circular",{"v-progress-circular--indeterminate":!!e.indeterminate,"v-progress-circular--visible":v.value,"v-progress-circular--disable-shrink":e.indeterminate==="disable-shrink"},i.value,o.value,u.value,e.class],style:[l.value,c.value,e.style],role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.indeterminate?void 0:_.value},{default:()=>[m("svg",{style:{transform:`rotate(calc(-90deg + ${Number(e.rotate)}deg))`},xmlns:"http://www.w3.org/2000/svg",viewBox:`0 0 ${k.value} ${k.value}`},[m("circle",{class:["v-progress-circular__underlay",d.value],style:f.value,fill:"transparent",cx:"50%",cy:"50%",r:s,"stroke-width":P.value,"stroke-dasharray":r,"stroke-dashoffset":0},null),m("circle",{class:"v-progress-circular__overlay",fill:"transparent",cx:"50%",cy:"50%",r:s,"stroke-width":P.value,"stroke-dasharray":r,"stroke-dashoffset":O.value},null)]),n.default&&m("div",{class:"v-progress-circular__content"},[n.default({value:_.value})])]})),{}}}),Qn=K({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},"dimension");function es(e){return{dimensionStyles:C(()=>({height:de(e.height),maxHeight:de(e.maxHeight),maxWidth:de(e.maxWidth),minHeight:de(e.minHeight),minWidth:de(e.minWidth),width:de(e.width)}))}}const Bb={badge:"Badge",close:"Close",dataIterator:{noResultsText:"No matching records found",loadingText:"Loading items..."},dataTable:{itemsPerPageText:"Rows per page:",ariaLabel:{sortDescending:"Sorted descending.",sortAscending:"Sorted ascending.",sortNone:"Not sorted.",activateNone:"Activate to remove sorting.",activateDescending:"Activate to sort descending.",activateAscending:"Activate to sort ascending."},sortBy:"Sort by"},dataFooter:{itemsPerPageText:"Items per page:",itemsPerPageAll:"All",nextPage:"Next page",prevPage:"Previous page",firstPage:"First page",lastPage:"Last page",pageText:"{0}-{1} of {2}"},dateRangeInput:{divider:"to"},datePicker:{ok:"OK",cancel:"Cancel",range:{title:"Select dates",header:"Enter dates"},title:"Select date",header:"Enter date",input:{placeholder:"Enter date"}},noDataText:"No data available",carousel:{prev:"Previous visual",next:"Next visual",ariaLabel:{delimiter:"Carousel slide {0} of {1}"}},calendar:{moreEvents:"{0} more"},input:{clear:"Clear {0}",prependAction:"{0} prepended action",appendAction:"{0} appended action"},fileInput:{counter:"{0} files",counterSize:"{0} files ({1} in total)"},timePicker:{am:"AM",pm:"PM"},pagination:{ariaLabel:{root:"Pagination Navigation",next:"Next page",previous:"Previous page",page:"Go to page {0}",currentPage:"Page {0}, Current page",first:"First page",last:"Last page"}},rating:{ariaLabel:{item:"Rating {0} of {1}"}},loading:"Loading...",infiniteScroll:{loadMore:"Load more",empty:"No more"}},Hb={af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1},Rc="$vuetify.",$c=(e,t)=>e.replace(/\{(\d+)\}/g,(n,s)=>String(t[+s])),Sm=(e,t,n)=>function(s){for(var r=arguments.length,a=new Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];if(!s.startsWith(Rc))return $c(s,a);const o=s.replace(Rc,""),l=e.value&&n.value[e.value],u=t.value&&n.value[t.value];let c=Zo(l,o,null);return c||(`${s}${e.value}`,c=Zo(u,o,null)),c||(c=s),typeof c!="string"&&(c=s),$c(c,a)};function Cm(e,t){return(n,s)=>new Intl.NumberFormat([e.value,t.value],s).format(n)}function vo(e,t,n){const s=Ke(e,t,e[t]??n.value);return s.value=e[t]??n.value,we(n,r=>{e[t]==null&&(s.value=n.value)}),s}function xm(e){return t=>{const n=vo(t,"locale",e.current),s=vo(t,"fallback",e.fallback),r=vo(t,"messages",e.messages);return{name:"vuetify",current:n,fallback:s,messages:r,t:Sm(n,s,r),n:Cm(n,s),provide:xm({current:n,fallback:s,messages:r})}}}function Ub(e){const t=Ce((e==null?void 0:e.locale)??"en"),n=Ce((e==null?void 0:e.fallback)??"en"),s=U({en:Bb,...e==null?void 0:e.messages});return{name:"vuetify",current:t,fallback:n,messages:s,t:Sm(t,n,s),n:Cm(t,n),provide:xm({current:t,fallback:n,messages:s})}}const oi=Symbol.for("vuetify:locale");function zb(e){return e.name!=null}function jb(e){const t=e!=null&&e.adapter&&zb(e==null?void 0:e.adapter)?e==null?void 0:e.adapter:Ub(e),n=Wb(t,e);return{...t,...n}}function Ri(){const e=je(oi);if(!e)throw new Error("[Vuetify] Could not find injected locale instance");return e}function Wb(e,t){const n=U((t==null?void 0:t.rtl)??Hb),s=C(()=>n.value[e.current.value]??!1);return{isRtl:s,rtl:n,rtlClasses:C(()=>`v-locale--is-${s.value?"rtl":"ltr"}`)}}function ts(){const e=je(oi);if(!e)throw new Error("[Vuetify] Could not find injected rtl instance");return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}const Bc={center:"center",top:"bottom",bottom:"top",left:"right",right:"left"},sa=K({location:String},"location");function ra(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2?arguments[2]:void 0;const{isRtl:s}=ts();return{locationStyles:C(()=>{if(!e.location)return{};const{side:a,align:i}=Yo(e.location.split(" ").length>1?e.location:`${e.location} center`,s.value);function o(u){return n?n(u):0}const l={};return a!=="center"&&(t?l[Bc[a]]=`calc(100% - ${o(a)}px)`:l[a]=0),i!=="center"?t?l[Bc[i]]=`calc(100% - ${o(i)}px)`:l[i]=0:(a==="center"?l.top=l.left="50%":l[{top:"left",bottom:"left",left:"top",right:"top"}[a]]="50%",l.transform={top:"translateX(-50%)",bottom:"translateX(-50%)",left:"translateY(-50%)",right:"translateY(-50%)",center:"translate(-50%, -50%)"}[a]),l})}}const qb=K({absolute:Boolean,active:{type:Boolean,default:!0},bgColor:String,bgOpacity:[Number,String],bufferValue:{type:[Number,String],default:0},clickable:Boolean,color:String,height:{type:[Number,String],default:4},indeterminate:Boolean,max:{type:[Number,String],default:100},modelValue:{type:[Number,String],default:0},reverse:Boolean,stream:Boolean,striped:Boolean,roundedBar:Boolean,...Se(),...sa({location:"top"}),...At(),...Ye(),...We()},"VProgressLinear"),Zb=oe()({name:"VProgressLinear",props:qb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"modelValue"),{isRtl:r,rtlClasses:a}=ts(),{themeClasses:i}=et(e),{locationStyles:o}=ra(e),{textColorClasses:l,textColorStyles:u}=on(e,"color"),{backgroundColorClasses:c,backgroundColorStyles:d}=ws(C(()=>e.bgColor||e.color)),{backgroundColorClasses:f,backgroundColorStyles:h}=ws(e,"color"),{roundedClasses:v}=Vt(e),{intersectionRef:g,isIntersecting:b}=wm(),_=C(()=>parseInt(e.max,10)),E=C(()=>parseInt(e.height,10)),w=C(()=>parseFloat(e.bufferValue)/_.value*100),k=C(()=>parseFloat(s.value)/_.value*100),P=C(()=>r.value!==e.reverse),O=C(()=>e.indeterminate?"fade-transition":"slide-x-transition"),T=C(()=>e.bgOpacity==null?e.bgOpacity:parseFloat(e.bgOpacity));function y(S){if(!g.value)return;const{left:A,right:z,width:M}=g.value.getBoundingClientRect(),B=P.value?M-S.clientX+(z-M):S.clientX-A;s.value=Math.round(B/M*_.value)}return pe(()=>m(e.tag,{ref:g,class:["v-progress-linear",{"v-progress-linear--absolute":e.absolute,"v-progress-linear--active":e.active&&b.value,"v-progress-linear--reverse":P.value,"v-progress-linear--rounded":e.rounded,"v-progress-linear--rounded-bar":e.roundedBar,"v-progress-linear--striped":e.striped},v.value,i.value,a.value,e.class],style:[{bottom:e.location==="bottom"?0:void 0,top:e.location==="top"?0:void 0,height:e.active?de(E.value):0,"--v-progress-linear-height":de(E.value),...o.value},e.style],role:"progressbar","aria-hidden":e.active?"false":"true","aria-valuemin":"0","aria-valuemax":e.max,"aria-valuenow":e.indeterminate?void 0:k.value,onClick:e.clickable&&y},{default:()=>[e.stream&&m("div",{key:"stream",class:["v-progress-linear__stream",l.value],style:{...u.value,[P.value?"left":"right"]:de(-E.value),borderTop:`${de(E.value/2)} dotted`,opacity:T.value,top:`calc(50% - ${de(E.value/4)})`,width:de(100-w.value,"%"),"--v-progress-linear-stream-to":de(E.value*(P.value?1:-1))}},null),m("div",{class:["v-progress-linear__background",c.value],style:[d.value,{opacity:T.value,width:de(e.stream?w.value:100,"%")}]},null),m(Sn,{name:O.value},{default:()=>[e.indeterminate?m("div",{class:"v-progress-linear__indeterminate"},[["long","short"].map(S=>m("div",{key:S,class:["v-progress-linear__indeterminate",S,f.value],style:h.value},null))]):m("div",{class:["v-progress-linear__determinate",f.value],style:[h.value,{width:de(k.value,"%")}]},null)]}),n.default&&m("div",{class:"v-progress-linear__content"},[n.default({value:k.value,buffer:w.value})])]})),{}}}),ru=K({loading:[Boolean,String]},"loader");function au(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();return{loaderClasses:C(()=>({[`${t}--loading`]:e.loading}))}}function _m(e,t){var s;let{slots:n}=t;return m("div",{class:`${e.name}__loader`},[((s=n.default)==null?void 0:s.call(n,{color:e.color,isActive:e.active}))||m(Zb,{active:e.active,color:e.color,height:"2",indeterminate:!0},null)])}const Gb=["static","relative","fixed","absolute","sticky"],$i=K({position:{type:String,validator:e=>Gb.includes(e)}},"position");function Bi(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();return{positionClasses:C(()=>e.position?`${t}--${e.position}`:void 0)}}function Kb(){var e,t;return(t=(e=dt("useRouter"))==null?void 0:e.proxy)==null?void 0:t.$router}function Hi(e,t){const n=yy("RouterLink"),s=C(()=>!!(e.href||e.to)),r=C(()=>(s==null?void 0:s.value)||bc(t,"click")||bc(e,"click"));if(typeof n=="string")return{isLink:s,isClickable:r,href:he(e,"href")};const a=e.to?n.useLink(e):void 0;return{isLink:s,isClickable:r,route:a==null?void 0:a.route,navigate:a==null?void 0:a.navigate,isActive:a&&C(()=>{var i,o;return e.exact?(i=a.isExactActive)==null?void 0:i.value:(o=a.isActive)==null?void 0:o.value}),href:C(()=>e.to?a==null?void 0:a.route.value.href:e.href)}}const Ui=K({href:String,replace:Boolean,to:[String,Object],exact:Boolean},"router");let go=!1;function Yb(e,t){let n=!1,s,r;st&&(bt(()=>{window.addEventListener("popstate",a),s=e==null?void 0:e.beforeEach((i,o,l)=>{go?n?t(l):l():setTimeout(()=>n?t(l):l()),go=!0}),r=e==null?void 0:e.afterEach(()=>{go=!1})}),pt(()=>{window.removeEventListener("popstate",a),s==null||s(),r==null||r()}));function a(i){var o;(o=i.state)!=null&&o.replaced||(n=!0,setTimeout(()=>n=!1))}}function Jb(e,t){we(()=>{var n;return(n=e.isActive)==null?void 0:n.value},n=>{e.isLink.value&&n&&t&&bt(()=>{t(!0)})},{immediate:!0})}const Qo=Symbol("rippleStop"),Xb=80;function Hc(e,t){e.style.transform=t,e.style.webkitTransform=t}function el(e){return e.constructor.name==="TouchEvent"}function Em(e){return e.constructor.name==="KeyboardEvent"}const Qb=function(e,t){var d;let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=0,r=0;if(!Em(e)){const f=t.getBoundingClientRect(),h=el(e)?e.touches[e.touches.length-1]:e;s=h.clientX-f.left,r=h.clientY-f.top}let a=0,i=.3;(d=t._ripple)!=null&&d.circle?(i=.15,a=t.clientWidth/2,a=n.center?a:a+Math.sqrt((s-a)**2+(r-a)**2)/4):a=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2;const o=`${(t.clientWidth-a*2)/2}px`,l=`${(t.clientHeight-a*2)/2}px`,u=n.center?o:`${s-a}px`,c=n.center?l:`${r-a}px`;return{radius:a,scale:i,x:u,y:c,centerX:o,centerY:l}},li={show(e,t){var h;let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!((h=t==null?void 0:t._ripple)!=null&&h.enabled))return;const s=document.createElement("span"),r=document.createElement("span");s.appendChild(r),s.className="v-ripple__container",n.class&&(s.className+=` ${n.class}`);const{radius:a,scale:i,x:o,y:l,centerX:u,centerY:c}=Qb(e,t,n),d=`${a*2}px`;r.className="v-ripple__animation",r.style.width=d,r.style.height=d,t.appendChild(s);const f=window.getComputedStyle(t);f&&f.position==="static"&&(t.style.position="relative",t.dataset.previousPosition="static"),r.classList.add("v-ripple__animation--enter"),r.classList.add("v-ripple__animation--visible"),Hc(r,`translate(${o}, ${l}) scale3d(${i},${i},${i})`),r.dataset.activated=String(performance.now()),setTimeout(()=>{r.classList.remove("v-ripple__animation--enter"),r.classList.add("v-ripple__animation--in"),Hc(r,`translate(${u}, ${c}) scale3d(1,1,1)`)},0)},hide(e){var a;if(!((a=e==null?void 0:e._ripple)!=null&&a.enabled))return;const t=e.getElementsByClassName("v-ripple__animation");if(t.length===0)return;const n=t[t.length-1];if(n.dataset.isHiding)return;n.dataset.isHiding="true";const s=performance.now()-Number(n.dataset.activated),r=Math.max(250-s,0);setTimeout(()=>{n.classList.remove("v-ripple__animation--in"),n.classList.add("v-ripple__animation--out"),setTimeout(()=>{var o;e.getElementsByClassName("v-ripple__animation").length===1&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),((o=n.parentNode)==null?void 0:o.parentNode)===e&&e.removeChild(n.parentNode)},300)},r)}};function Tm(e){return typeof e>"u"||!!e}function Ur(e){const t={},n=e.currentTarget;if(!(!(n!=null&&n._ripple)||n._ripple.touched||e[Qo])){if(e[Qo]=!0,el(e))n._ripple.touched=!0,n._ripple.isTouch=!0;else if(n._ripple.isTouch)return;if(t.center=n._ripple.centered||Em(e),n._ripple.class&&(t.class=n._ripple.class),el(e)){if(n._ripple.showTimerCommit)return;n._ripple.showTimerCommit=()=>{li.show(e,n,t)},n._ripple.showTimer=window.setTimeout(()=>{var s;(s=n==null?void 0:n._ripple)!=null&&s.showTimerCommit&&(n._ripple.showTimerCommit(),n._ripple.showTimerCommit=null)},Xb)}else li.show(e,n,t)}}function Uc(e){e[Qo]=!0}function kt(e){const t=e.currentTarget;if(t!=null&&t._ripple){if(window.clearTimeout(t._ripple.showTimer),e.type==="touchend"&&t._ripple.showTimerCommit){t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null,t._ripple.showTimer=window.setTimeout(()=>{kt(e)});return}window.setTimeout(()=>{t._ripple&&(t._ripple.touched=!1)}),li.hide(t)}}function km(e){const t=e.currentTarget;t!=null&&t._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}let zr=!1;function Om(e){!zr&&(e.keyCode===yc.enter||e.keyCode===yc.space)&&(zr=!0,Ur(e))}function Im(e){zr=!1,kt(e)}function Am(e){zr&&(zr=!1,kt(e))}function Vm(e,t,n){const{value:s,modifiers:r}=t,a=Tm(s);if(a||li.hide(e),e._ripple=e._ripple??{},e._ripple.enabled=a,e._ripple.centered=r.center,e._ripple.circle=r.circle,Go(s)&&s.class&&(e._ripple.class=s.class),a&&!n){if(r.stop){e.addEventListener("touchstart",Uc,{passive:!0}),e.addEventListener("mousedown",Uc);return}e.addEventListener("touchstart",Ur,{passive:!0}),e.addEventListener("touchend",kt,{passive:!0}),e.addEventListener("touchmove",km,{passive:!0}),e.addEventListener("touchcancel",kt),e.addEventListener("mousedown",Ur),e.addEventListener("mouseup",kt),e.addEventListener("mouseleave",kt),e.addEventListener("keydown",Om),e.addEventListener("keyup",Im),e.addEventListener("blur",Am),e.addEventListener("dragstart",kt,{passive:!0})}else!a&&n&&Pm(e)}function Pm(e){e.removeEventListener("mousedown",Ur),e.removeEventListener("touchstart",Ur),e.removeEventListener("touchend",kt),e.removeEventListener("touchmove",km),e.removeEventListener("touchcancel",kt),e.removeEventListener("mouseup",kt),e.removeEventListener("mouseleave",kt),e.removeEventListener("keydown",Om),e.removeEventListener("keyup",Im),e.removeEventListener("dragstart",kt),e.removeEventListener("blur",Am)}function e0(e,t){Vm(e,t,!1)}function t0(e){delete e._ripple,Pm(e)}function n0(e,t){if(t.value===t.oldValue)return;const n=Tm(t.oldValue);Vm(e,t,n)}const aa={mounted:e0,unmounted:t0,updated:n0},s0=K({active:{type:Boolean,default:void 0},symbol:{type:null,default:ym},flat:Boolean,icon:[Boolean,String,Function,Object],prependIcon:Fe,appendIcon:Fe,block:Boolean,stacked:Boolean,ripple:{type:[Boolean,Object],default:!0},text:String,...Jn(),...Se(),...Ut(),...Qn(),...On(),...tu(),...ru(),...sa(),...$i(),...At(),...Ui(),...ea(),...Ye({tag:"button"}),...We(),...un({variant:"elevated"})},"VBtn"),Be=oe()({name:"VBtn",directives:{Ripple:aa},props:s0(),emits:{"group:selected":e=>!0},setup(e,t){let{attrs:n,slots:s}=t;const{themeClasses:r}=et(e),{borderClasses:a}=Xn(e),{colorClasses:i,colorStyles:o,variantClasses:l}=_s(e),{densityClasses:u}=Xt(e),{dimensionStyles:c}=es(e),{elevationClasses:d}=In(e),{loaderClasses:f}=au(e),{locationStyles:h}=ra(e),{positionClasses:v}=Bi(e),{roundedClasses:g}=Vt(e),{sizeClasses:b,sizeStyles:_}=ta(e),E=nu(e,e.symbol,!1),w=Hi(e,n),k=C(()=>{var S;return e.active!==void 0?e.active:w.isLink.value?(S=w.isActive)==null?void 0:S.value:E==null?void 0:E.isSelected.value}),P=C(()=>(E==null?void 0:E.disabled.value)||e.disabled),O=C(()=>e.variant==="elevated"&&!(e.disabled||e.flat||e.border)),T=C(()=>{if(e.value!==void 0)return Object(e.value)===e.value?JSON.stringify(e.value,null,0):e.value});function y(S){var A;P.value||w.isLink.value&&(S.metaKey||S.ctrlKey||S.shiftKey||S.button!==0||n.target==="_blank")||((A=w.navigate)==null||A.call(w,S),E==null||E.toggle())}return Jb(w,E==null?void 0:E.select),pe(()=>{var N,te;const S=w.isLink.value?"a":e.tag,A=!!(e.prependIcon||s.prepend),z=!!(e.appendIcon||s.append),M=!!(e.icon&&e.icon!==!0),B=(E==null?void 0:E.isSelected.value)&&(!w.isLink.value||((N=w.isActive)==null?void 0:N.value))||!E||((te=w.isActive)==null?void 0:te.value);return Xe(m(S,{type:S==="a"?void 0:"button",class:["v-btn",E==null?void 0:E.selectedClass.value,{"v-btn--active":k.value,"v-btn--block":e.block,"v-btn--disabled":P.value,"v-btn--elevated":O.value,"v-btn--flat":e.flat,"v-btn--icon":!!e.icon,"v-btn--loading":e.loading,"v-btn--stacked":e.stacked},r.value,a.value,B?i.value:void 0,u.value,d.value,f.value,v.value,g.value,b.value,l.value,e.class],style:[B?o.value:void 0,c.value,h.value,_.value,e.style],disabled:P.value||void 0,href:w.href.value,onClick:y,value:T.value},{default:()=>{var ee;return[xs(!0,"v-btn"),!e.icon&&A&&m("span",{key:"prepend",class:"v-btn__prepend"},[s.prepend?m(Ze,{key:"prepend-defaults",disabled:!e.prependIcon,defaults:{VIcon:{icon:e.prependIcon}}},s.prepend):m(it,{key:"prepend-icon",icon:e.prependIcon},null)]),m("span",{class:"v-btn__content","data-no-activator":""},[!s.default&&M?m(it,{key:"content-icon",icon:e.icon},null):m(Ze,{key:"content-defaults",disabled:!M,defaults:{VIcon:{icon:e.icon}}},{default:()=>{var se;return[((se=s.default)==null?void 0:se.call(s))??e.text]}})]),!e.icon&&z&&m("span",{key:"append",class:"v-btn__append"},[s.append?m(Ze,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VIcon:{icon:e.appendIcon}}},s.append):m(it,{key:"append-icon",icon:e.appendIcon},null)]),!!e.loading&&m("span",{key:"loader",class:"v-btn__loader"},[((ee=s.loader)==null?void 0:ee.call(s))??m($b,{color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0,size:"23",width:"2"},null)])]}}),[[bn("ripple"),!P.value&&e.ripple,null]])}),{}}});function yo(e,t){return{x:e.x+t.x,y:e.y+t.y}}function r0(e,t){return{x:e.x-t.x,y:e.y-t.y}}function zc(e,t){if(e.side==="top"||e.side==="bottom"){const{side:n,align:s}=e,r=s==="left"?0:s==="center"?t.width/2:s==="right"?t.width:s,a=n==="top"?0:n==="bottom"?t.height:n;return yo({x:r,y:a},t)}else if(e.side==="left"||e.side==="right"){const{side:n,align:s}=e,r=n==="left"?0:n==="right"?t.width:n,a=s==="top"?0:s==="center"?t.height/2:s==="bottom"?t.height:s;return yo({x:r,y:a},t)}return yo({x:t.width/2,y:t.height/2},t)}const Mm={static:o0,connected:u0},a0=K({locationStrategy:{type:[String,Function],default:"static",validator:e=>typeof e=="function"||e in Mm},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies");function i0(e,t){const n=U({}),s=U();st&&(Zn(()=>!!(t.isActive.value&&e.locationStrategy),a=>{var i,o;we(()=>e.locationStrategy,a),pt(()=>{s.value=void 0}),typeof e.locationStrategy=="function"?s.value=(i=e.locationStrategy(t,e,n))==null?void 0:i.updateLocation:s.value=(o=Mm[e.locationStrategy](t,e,n))==null?void 0:o.updateLocation}),window.addEventListener("resize",r,{passive:!0}),pt(()=>{window.removeEventListener("resize",r),s.value=void 0}));function r(a){var i;(i=s.value)==null||i.call(s,a)}return{contentStyles:n,updateLocation:s}}function o0(){}function l0(e,t){t?e.style.removeProperty("left"):e.style.removeProperty("right");const n=Gl(e);return t?n.x+=parseFloat(e.style.right||0):n.x-=parseFloat(e.style.left||0),n.y-=parseFloat(e.style.top||0),n}function u0(e,t,n){vb(e.activatorEl.value)&&Object.assign(n.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0});const{preferredAnchor:r,preferredOrigin:a}=ql(()=>{const v=Yo(t.location,e.isRtl.value),g=t.origin==="overlap"?v:t.origin==="auto"?mo(v):Yo(t.origin,e.isRtl.value);return v.side===g.side&&v.align===ho(g).align?{preferredAnchor:Sc(v),preferredOrigin:Sc(g)}:{preferredAnchor:v,preferredOrigin:g}}),[i,o,l,u]=["minWidth","minHeight","maxWidth","maxHeight"].map(v=>C(()=>{const g=parseFloat(t[v]);return isNaN(g)?1/0:g})),c=C(()=>{if(Array.isArray(t.offset))return t.offset;if(typeof t.offset=="string"){const v=t.offset.split(" ").map(parseFloat);return v.length<2&&v.push(0),v}return typeof t.offset=="number"?[t.offset,0]:[0,0]});let d=!1;const f=new ResizeObserver(()=>{d&&h()});we([e.activatorEl,e.contentEl],(v,g)=>{let[b,_]=v,[E,w]=g;E&&f.unobserve(E),b&&f.observe(b),w&&f.unobserve(w),_&&f.observe(_)},{immediate:!0}),pt(()=>{f.disconnect()});function h(){if(d=!1,requestAnimationFrame(()=>{requestAnimationFrame(()=>d=!0)}),!e.activatorEl.value||!e.contentEl.value)return;const v=e.activatorEl.value.getBoundingClientRect(),g=l0(e.contentEl.value,e.isRtl.value),b=ai(e.contentEl.value),_=12;b.length||(b.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(g.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),g.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)));const E=b.reduce((z,M)=>{const B=M.getBoundingClientRect(),N=new Hs({x:M===document.documentElement?0:B.x,y:M===document.documentElement?0:B.y,width:M.clientWidth,height:M.clientHeight});return z?new Hs({x:Math.max(z.left,N.left),y:Math.max(z.top,N.top),width:Math.min(z.right,N.right)-Math.max(z.left,N.left),height:Math.min(z.bottom,N.bottom)-Math.max(z.top,N.top)}):N},void 0);E.x+=_,E.y+=_,E.width-=_*2,E.height-=_*2;let w={anchor:r.value,origin:a.value};function k(z){const M=new Hs(g),B=zc(z.anchor,v),N=zc(z.origin,M);let{x:te,y:ee}=r0(B,N);switch(z.anchor.side){case"top":ee-=c.value[0];break;case"bottom":ee+=c.value[0];break;case"left":te-=c.value[0];break;case"right":te+=c.value[0];break}switch(z.anchor.align){case"top":ee-=c.value[1];break;case"bottom":ee+=c.value[1];break;case"left":te-=c.value[1];break;case"right":te+=c.value[1];break}return M.x+=te,M.y+=ee,M.width=Math.min(M.width,l.value),M.height=Math.min(M.height,u.value),{overflows:xc(M,E),x:te,y:ee}}let P=0,O=0;const T={x:0,y:0},y={x:!1,y:!1};let S=-1;for(;!(S++>10);){const{x:z,y:M,overflows:B}=k(w);P+=z,O+=M,g.x+=z,g.y+=M;{const N=Cc(w.anchor),te=B.x.before||B.x.after,ee=B.y.before||B.y.after;let se=!1;if(["x","y"].forEach(Y=>{if(Y==="x"&&te&&!y.x||Y==="y"&&ee&&!y.y){const J={anchor:{...w.anchor},origin:{...w.origin}},me=Y==="x"?N==="y"?ho:mo:N==="y"?mo:ho;J.anchor=me(J.anchor),J.origin=me(J.origin);const{overflows:fe}=k(J);(fe[Y].before<=B[Y].before&&fe[Y].after<=B[Y].after||fe[Y].before+fe[Y].after<(B[Y].before+B[Y].after)/2)&&(w=J,se=y[Y]=!0)}}),se)continue}B.x.before&&(P+=B.x.before,g.x+=B.x.before),B.x.after&&(P-=B.x.after,g.x-=B.x.after),B.y.before&&(O+=B.y.before,g.y+=B.y.before),B.y.after&&(O-=B.y.after,g.y-=B.y.after);{const N=xc(g,E);T.x=E.width-N.x.before-N.x.after,T.y=E.height-N.y.before-N.y.after,P+=N.x.before,g.x+=N.x.before,O+=N.y.before,g.y+=N.y.before}break}const A=Cc(w.anchor);return Object.assign(n.value,{"--v-overlay-anchor-origin":`${w.anchor.side} ${w.anchor.align}`,transformOrigin:`${w.origin.side} ${w.origin.align}`,top:de(po(O)),left:e.isRtl.value?void 0:de(po(P)),right:e.isRtl.value?de(po(-P)):void 0,minWidth:de(A==="y"?Math.min(i.value,v.width):i.value),maxWidth:de(jc($r(T.x,i.value===1/0?0:i.value,l.value))),maxHeight:de(jc($r(T.y,o.value===1/0?0:o.value,u.value)))}),{available:T,contentBox:g}}return we(()=>[r.value,a.value,t.offset,t.minWidth,t.minHeight,t.maxWidth,t.maxHeight],()=>h()),bt(()=>{const v=h();if(!v)return;const{available:g,contentBox:b}=v;b.height>g.y&&requestAnimationFrame(()=>{h(),requestAnimationFrame(()=>{h()})})}),{updateLocation:h}}function po(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function jc(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let tl=!0;const ui=[];function c0(e){!tl||ui.length?(ui.push(e),nl()):(tl=!1,e(),nl())}let Wc=-1;function nl(){cancelAnimationFrame(Wc),Wc=requestAnimationFrame(()=>{const e=ui.shift();e&&e(),ui.length?nl():tl=!0})}const $a={none:null,close:m0,block:h0,reposition:v0},d0=K({scrollStrategy:{type:[String,Function],default:"block",validator:e=>typeof e=="function"||e in $a}},"VOverlay-scroll-strategies");function f0(e,t){if(!st)return;let n;_n(async()=>{n==null||n.stop(),t.isActive.value&&e.scrollStrategy&&(n=Kr(),await bt(),n.active&&n.run(()=>{var s;typeof e.scrollStrategy=="function"?e.scrollStrategy(t,e,n):(s=$a[e.scrollStrategy])==null||s.call($a,t,e,n)}))}),pt(()=>{n==null||n.stop()})}function m0(e){function t(n){e.isActive.value=!1}Dm(e.activatorEl.value??e.contentEl.value,t)}function h0(e,t){var i;const n=(i=e.root.value)==null?void 0:i.offsetParent,s=[...new Set([...ai(e.activatorEl.value,t.contained?n:void 0),...ai(e.contentEl.value,t.contained?n:void 0)])].filter(o=>!o.classList.contains("v-overlay-scroll-blocked")),r=window.innerWidth-document.documentElement.offsetWidth,a=(o=>Jl(o)&&o)(n||document.documentElement);a&&e.root.value.classList.add("v-overlay--scroll-blocked"),s.forEach((o,l)=>{o.style.setProperty("--v-body-scroll-x",de(-o.scrollLeft)),o.style.setProperty("--v-body-scroll-y",de(-o.scrollTop)),o!==document.documentElement&&o.style.setProperty("--v-scrollbar-offset",de(r)),o.classList.add("v-overlay-scroll-blocked")}),pt(()=>{s.forEach((o,l)=>{const u=parseFloat(o.style.getPropertyValue("--v-body-scroll-x")),c=parseFloat(o.style.getPropertyValue("--v-body-scroll-y"));o.style.removeProperty("--v-body-scroll-x"),o.style.removeProperty("--v-body-scroll-y"),o.style.removeProperty("--v-scrollbar-offset"),o.classList.remove("v-overlay-scroll-blocked"),o.scrollLeft=-u,o.scrollTop=-c}),a&&e.root.value.classList.remove("v-overlay--scroll-blocked")})}function v0(e,t,n){let s=!1,r=-1,a=-1;function i(o){c0(()=>{var c,d;const l=performance.now();(d=(c=e.updateLocation).value)==null||d.call(c,o),s=(performance.now()-l)/(1e3/60)>2})}a=(typeof requestIdleCallback>"u"?o=>o():requestIdleCallback)(()=>{n.run(()=>{Dm(e.activatorEl.value??e.contentEl.value,o=>{s?(cancelAnimationFrame(r),r=requestAnimationFrame(()=>{r=requestAnimationFrame(()=>{i(o)})})):i(o)})})}),pt(()=>{typeof cancelIdleCallback<"u"&&cancelIdleCallback(a),cancelAnimationFrame(r)})}function Dm(e,t){const n=[document,...ai(e)];n.forEach(s=>{s.addEventListener("scroll",t,{passive:!0})}),pt(()=>{n.forEach(s=>{s.removeEventListener("scroll",t)})})}const sl=Symbol.for("vuetify:v-menu"),g0=K({closeDelay:[Number,String],openDelay:[Number,String]},"delay");function y0(e,t){const n={},s=r=>()=>{if(!st)return Promise.resolve(!0);const a=r==="openDelay";return n.closeDelay&&window.clearTimeout(n.closeDelay),delete n.closeDelay,n.openDelay&&window.clearTimeout(n.openDelay),delete n.openDelay,new Promise(i=>{const o=parseInt(e[r]??0,10);n[r]=window.setTimeout(()=>{t==null||t(a),i(a)},o)})};return{runCloseDelay:s("closeDelay"),runOpenDelay:s("openDelay")}}const p0=K({activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...g0()},"VOverlay-activator");function b0(e,t){let{isActive:n,isTop:s}=t;const r=U();let a=!1,i=!1,o=!0;const l=C(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),u=C(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!l.value),{runOpenDelay:c,runCloseDelay:d}=y0(e,w=>{w===(e.openOnHover&&a||l.value&&i)&&!(e.openOnHover&&n.value&&!s.value)&&(n.value!==w&&(o=!0),n.value=w)}),f={onClick:w=>{w.stopPropagation(),r.value=w.currentTarget||w.target,n.value=!n.value},onMouseenter:w=>{var k;(k=w.sourceCapabilities)!=null&&k.firesTouchEvents||(a=!0,r.value=w.currentTarget||w.target,c())},onMouseleave:w=>{a=!1,d()},onFocus:w=>{Jo&&!w.target.matches(":focus-visible")||(i=!0,w.stopPropagation(),r.value=w.currentTarget||w.target,c())},onBlur:w=>{i=!1,w.stopPropagation(),d()}},h=C(()=>{const w={};return u.value&&(w.onClick=f.onClick),e.openOnHover&&(w.onMouseenter=f.onMouseenter,w.onMouseleave=f.onMouseleave),l.value&&(w.onFocus=f.onFocus,w.onBlur=f.onBlur),w}),v=C(()=>{const w={};if(e.openOnHover&&(w.onMouseenter=()=>{a=!0,c()},w.onMouseleave=()=>{a=!1,d()}),l.value&&(w.onFocusin=()=>{i=!0,c()},w.onFocusout=()=>{i=!1,d()}),e.closeOnContentClick){const k=je(sl,null);w.onClick=()=>{n.value=!1,k==null||k.closeParents()}}return w}),g=C(()=>{const w={};return e.openOnHover&&(w.onMouseenter=()=>{o&&(a=!0,o=!1,c())},w.onMouseleave=()=>{a=!1,d()}),w});we(s,w=>{w&&(e.openOnHover&&!a&&(!l.value||!i)||l.value&&!i&&(!e.openOnHover||!a))&&(n.value=!1)});const b=U();_n(()=>{b.value&&bt(()=>{r.value=Ko(b.value)})});const _=dt("useActivator");let E;return we(()=>!!e.activator,w=>{w&&st?(E=Kr(),E.run(()=>{w0(e,_,{activatorEl:r,activatorEvents:h})})):E&&E.stop()},{flush:"post",immediate:!0}),pt(()=>{E==null||E.stop()}),{activatorEl:r,activatorRef:b,activatorEvents:h,contentEvents:v,scrimEvents:g}}function w0(e,t,n){let{activatorEl:s,activatorEvents:r}=n;we(()=>e.activator,(l,u)=>{if(u&&l!==u){const c=o(u);c&&i(c)}l&&bt(()=>a())},{immediate:!0}),we(()=>e.activatorProps,()=>{a()}),pt(()=>{i()});function a(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:o(),u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;l&&Wp(l,ge(r.value,u))}function i(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:o(),u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;l&&qp(l,ge(r.value,u))}function o(){var c,d;let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator,u;if(l)if(l==="parent"){let f=(d=(c=t==null?void 0:t.proxy)==null?void 0:c.$el)==null?void 0:d.parentNode;for(;f.hasAttribute("data-no-activator");)f=f.parentNode;u=f}else typeof l=="string"?u=document.querySelector(l):"$el"in l?u=l.$el:u=l;return s.value=(u==null?void 0:u.nodeType)===Node.ELEMENT_NODE?u:null,s.value}}const zi=["sm","md","lg","xl","xxl"],rl=Symbol.for("vuetify:display"),qc={mobileBreakpoint:"lg",thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},S0=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:qc;return Lt(qc,e)};function Zc(e){return st&&!e?window.innerWidth:typeof e=="object"&&e.clientWidth||0}function Gc(e){return st&&!e?window.innerHeight:typeof e=="object"&&e.clientHeight||0}function Kc(e){const t=st&&!e?window.navigator.userAgent:"ssr";function n(v){return!!t.match(v)}const s=n(/android/i),r=n(/iphone|ipad|ipod/i),a=n(/cordova/i),i=n(/electron/i),o=n(/chrome/i),l=n(/edge/i),u=n(/firefox/i),c=n(/opera/i),d=n(/win/i),f=n(/mac/i),h=n(/linux/i);return{android:s,ios:r,cordova:a,electron:i,chrome:o,edge:l,firefox:u,opera:c,win:d,mac:f,linux:h,touch:mb,ssr:t==="ssr"}}function C0(e,t){const{thresholds:n,mobileBreakpoint:s}=S0(e),r=Ce(Gc(t)),a=Ce(Kc(t)),i=ct({}),o=Ce(Zc(t));function l(){r.value=Gc(),o.value=Zc()}function u(){l(),a.value=Kc()}return _n(()=>{const c=o.value<n.sm,d=o.value<n.md&&!c,f=o.value<n.lg&&!(d||c),h=o.value<n.xl&&!(f||d||c),v=o.value<n.xxl&&!(h||f||d||c),g=o.value>=n.xxl,b=c?"xs":d?"sm":f?"md":h?"lg":v?"xl":"xxl",_=typeof s=="number"?s:n[s],E=o.value<_;i.xs=c,i.sm=d,i.md=f,i.lg=h,i.xl=v,i.xxl=g,i.smAndUp=!c,i.mdAndUp=!(c||d),i.lgAndUp=!(c||d||f),i.xlAndUp=!(c||d||f||h),i.smAndDown=!(f||h||v||g),i.mdAndDown=!(h||v||g),i.lgAndDown=!(v||g),i.xlAndDown=!g,i.name=b,i.height=r.value,i.width=o.value,i.mobile=E,i.mobileBreakpoint=s,i.platform=a.value,i.thresholds=n}),st&&window.addEventListener("resize",l,{passive:!0}),{...ki(i),update:u,ssr:!!t}}function Nm(){const e=je(rl);if(!e)throw new Error("Could not find Vuetify display injection");return e}function x0(){if(!st)return Ce(!1);const{ssr:e}=Nm();if(e){const t=Ce(!1);return Tn(()=>{t.value=!0}),t}else return Ce(!0)}const _0=K({eager:Boolean},"lazy");function E0(e,t){const n=Ce(!1),s=C(()=>n.value||e.eager||t.value);we(t,()=>n.value=!0);function r(){e.eager||(n.value=!1)}return{isBooted:n,hasContent:s,onAfterLeave:r}}function ia(){const t=dt("useScopeId").vnode.scopeId;return{scopeId:t?{[t]:""}:void 0}}const Yc=Symbol.for("vuetify:stack"),ur=ct([]);function T0(e,t,n){const s=dt("useStack"),r=!n,a=je(Yc,void 0),i=ct({activeChildren:new Set});_t(Yc,i);const o=Ce(+t.value);Zn(e,()=>{var d;const c=(d=ur.at(-1))==null?void 0:d[1];o.value=c?c+10:+t.value,r&&ur.push([s.uid,o.value]),a==null||a.activeChildren.add(s.uid),pt(()=>{if(r){const f=ye(ur).findIndex(h=>h[0]===s.uid);ur.splice(f,1)}a==null||a.activeChildren.delete(s.uid)})});const l=Ce(!0);r&&_n(()=>{var d;const c=((d=ur.at(-1))==null?void 0:d[0])===s.uid;setTimeout(()=>l.value=c)});const u=C(()=>!i.activeChildren.size);return{globalTop:Yr(l),localTop:u,stackStyles:C(()=>({zIndex:o.value}))}}function k0(e){return{teleportTarget:C(()=>{const n=e.value;if(n===!0||!st)return;const s=n===!1?document.body:typeof n=="string"?document.querySelector(n):n;if(s==null)return;let r=s.querySelector(":scope > .v-overlay-container");return r||(r=document.createElement("div"),r.className="v-overlay-container",s.appendChild(r)),r})}}const oa=K({transition:{type:[Boolean,String,Object],default:"fade-transition",validator:e=>e!==!0}},"transition"),Hn=(e,t)=>{let{slots:n}=t;const{transition:s,disabled:r,...a}=e,{component:i=Sn,...o}=typeof s=="object"?s:{};return Kn(i,ge(typeof s=="string"?{name:r?"":s}:o,a,{disabled:r}),n)};function O0(){return!0}function Fm(e,t,n){if(!e||Lm(e,n)===!1)return!1;const s=fm(t);if(typeof ShadowRoot<"u"&&s instanceof ShadowRoot&&s.host===e.target)return!1;const r=(typeof n.value=="object"&&n.value.include||(()=>[]))();return r.push(t),!r.some(a=>a==null?void 0:a.contains(e.target))}function Lm(e,t){return(typeof t.value=="object"&&t.value.closeConditional||O0)(e)}function I0(e,t,n){const s=typeof n.value=="function"?n.value:n.value.handler;t._clickOutside.lastMousedownWasOutside&&Fm(e,t,n)&&setTimeout(()=>{Lm(e,n)&&s&&s(e)},0)}function Jc(e,t){const n=fm(e);t(document),typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&t(n)}const A0={mounted(e,t){const n=r=>I0(r,e,t),s=r=>{e._clickOutside.lastMousedownWasOutside=Fm(r,e,t)};Jc(e,r=>{r.addEventListener("click",n,!0),r.addEventListener("mousedown",s,!0)}),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[t.instance.$.uid]={onClick:n,onMousedown:s}},unmounted(e,t){e._clickOutside&&(Jc(e,n=>{var a;if(!n||!((a=e._clickOutside)!=null&&a[t.instance.$.uid]))return;const{onClick:s,onMousedown:r}=e._clickOutside[t.instance.$.uid];n.removeEventListener("click",s,!0),n.removeEventListener("mousedown",r,!0)}),delete e._clickOutside[t.instance.$.uid])}};function V0(e){const{modelValue:t,color:n,...s}=e;return m(Sn,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&m("div",ge({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},s),null)]})}const la=K({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...p0(),...Se(),...Qn(),..._0(),...a0(),...d0(),...We(),...oa()},"VOverlay"),Gn=oe()({name:"VOverlay",directives:{ClickOutside:A0},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...la()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,afterLeave:()=>!0},setup(e,t){let{slots:n,attrs:s,emit:r}=t;const a=Ke(e,"modelValue"),i=C({get:()=>a.value,set:J=>{J&&e.disabled||(a.value=J)}}),{teleportTarget:o}=k0(C(()=>e.attach||e.contained)),{themeClasses:l}=et(e),{rtlClasses:u,isRtl:c}=ts(),{hasContent:d,onAfterLeave:f}=E0(e,i),h=ws(C(()=>typeof e.scrim=="string"?e.scrim:null)),{globalTop:v,localTop:g,stackStyles:b}=T0(i,he(e,"zIndex"),e._disableGlobalStack),{activatorEl:_,activatorRef:E,activatorEvents:w,contentEvents:k,scrimEvents:P}=b0(e,{isActive:i,isTop:g}),{dimensionStyles:O}=es(e),T=x0(),{scopeId:y}=ia();we(()=>e.disabled,J=>{J&&(i.value=!1)});const S=U(),A=U(),{contentStyles:z,updateLocation:M}=i0(e,{isRtl:c,contentEl:A,activatorEl:_,isActive:i});f0(e,{root:S,contentEl:A,activatorEl:_,isActive:i,updateLocation:M});function B(J){r("click:outside",J),e.persistent?Y():i.value=!1}function N(){return i.value&&v.value}st&&we(i,J=>{J?window.addEventListener("keydown",te):window.removeEventListener("keydown",te)},{immediate:!0});function te(J){var me,fe;J.key==="Escape"&&v.value&&(e.persistent?Y():(i.value=!1,(me=A.value)!=null&&me.contains(document.activeElement)&&((fe=_.value)==null||fe.focus())))}const ee=Kb();Zn(()=>e.closeOnBack,()=>{Yb(ee,J=>{v.value&&i.value?(J(!1),e.persistent?Y():i.value=!1):J()})});const se=U();we(()=>i.value&&(e.absolute||e.contained)&&o.value==null,J=>{if(J){const me=hm(S.value);me&&me!==document.scrollingElement&&(se.value=me.scrollTop)}});function Y(){e.noClickAnimation||A.value&&Fs(A.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:ri})}return pe(()=>{var J;return m(_e,null,[(J=n.activator)==null?void 0:J.call(n,{isActive:i.value,props:ge({ref:E},w.value,e.activatorProps)}),T.value&&d.value&&m(Ry,{disabled:!o.value,to:o.value},{default:()=>[m("div",ge({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":i.value,"v-overlay--contained":e.contained},l.value,u.value,e.class],style:[b.value,{top:de(se.value)},e.style],ref:S},y,s),[m(V0,ge({color:h,modelValue:i.value&&!!e.scrim},P.value),null),m(Hn,{appear:!0,persisted:!0,transition:e.transition,target:_.value,onAfterLeave:()=>{f(),r("afterLeave")}},{default:()=>{var me;return[Xe(m("div",ge({ref:A,class:["v-overlay__content",e.contentClass],style:[O.value,z.value]},k.value,e.contentProps),[(me=n.default)==null?void 0:me.call(n,{isActive:i})]),[[Ys,i.value],[bn("click-outside"),{handler:B,closeConditional:N,include:()=>[_.value]}]])]}})])]})])}),{activatorEl:_,animateClick:Y,contentEl:A,globalTop:v,localTop:g,updateLocation:M}}}),bo=Symbol("Forwarded refs");function wo(e,t){let n=e;for(;n;){const s=Reflect.getOwnPropertyDescriptor(n,t);if(s)return s;n=Object.getPrototypeOf(n)}}function Qs(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return e[bo]=n,new Proxy(e,{get(r,a){if(Reflect.has(r,a))return Reflect.get(r,a);if(!(typeof a=="symbol"||a.startsWith("__"))){for(const i of n)if(i.value&&Reflect.has(i.value,a)){const o=Reflect.get(i.value,a);return typeof o=="function"?o.bind(i.value):o}}},has(r,a){if(Reflect.has(r,a))return!0;if(typeof a=="symbol"||a.startsWith("__"))return!1;for(const i of n)if(i.value&&Reflect.has(i.value,a))return!0;return!1},getOwnPropertyDescriptor(r,a){var o;const i=Reflect.getOwnPropertyDescriptor(r,a);if(i)return i;if(!(typeof a=="symbol"||a.startsWith("__"))){for(const l of n){if(!l.value)continue;const u=wo(l.value,a)??("_"in l.value?wo((o=l.value._)==null?void 0:o.setupState,a):void 0);if(u)return u}for(const l of n){const u=l.value&&l.value[bo];if(!u)continue;const c=u.slice();for(;c.length;){const d=c.shift(),f=wo(d.value,a);if(f)return f;const h=d.value&&d.value[bo];h&&c.push(...h)}}}}})}const P0=K({multiLine:Boolean,timeout:{type:[Number,String],default:5e3},vertical:Boolean,...sa({location:"bottom"}),...$i(),...At(),...un(),...We(),...Qr(la({transition:"v-snackbar-transition"}),["persistent","noClickAnimation","scrim","scrollStrategy"])},"VSnackbar"),M0=oe()({name:"VSnackbar",props:P0(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"modelValue"),{locationStyles:r}=ra(e),{positionClasses:a}=Bi(e),{scopeId:i}=ia(),{themeClasses:o}=et(e),{colorClasses:l,colorStyles:u,variantClasses:c}=_s(e),{roundedClasses:d}=Vt(e),f=U();we(s,v),we(()=>e.timeout,v),Tn(()=>{s.value&&v()});let h=-1;function v(){window.clearTimeout(h);const b=Number(e.timeout);!s.value||b===-1||(h=window.setTimeout(()=>{s.value=!1},b))}function g(){window.clearTimeout(h)}return pe(()=>{const[b]=Gn.filterProps(e);return m(Gn,ge({ref:f,class:["v-snackbar",{"v-snackbar--active":s.value,"v-snackbar--multi-line":e.multiLine&&!e.vertical,"v-snackbar--vertical":e.vertical},a.value,e.class],style:e.style},b,{modelValue:s.value,"onUpdate:modelValue":_=>s.value=_,contentProps:ge({class:["v-snackbar__wrapper",o.value,l.value,d.value,c.value],style:[r.value,u.value],onPointerenter:g,onPointerleave:v},b.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:"none",_disableGlobalStack:!0},i),{default:()=>[xs(!1,"v-snackbar"),n.default&&m("div",{class:"v-snackbar__content",role:"status","aria-live":"polite"},[n.default()]),n.actions&&m(Ze,{defaults:{VBtn:{variant:"text",ripple:!1}}},{default:()=>[m("div",{class:"v-snackbar__actions"},[n.actions()])]})],activator:n.activator})}),Qs({},f)}}),D0={__name:"snackbar",setup(e){const{snackbar:t,timeout:n,msg:s,color:r,icon:a}=Bp(Fi());return(i,o)=>(Ae(),St("div",null,[rt(t)?(Ae(),ut(M0,{key:0,modelValue:rt(t),"onUpdate:modelValue":o[1]||(o[1]=l=>Me(t)?t.value=l:null),timeout:rt(n),color:rt(r)},{actions:F(()=>[m(Be,{color:"white",variant:"text",onClick:o[0]||(o[0]=l=>t.value=!1)},{default:F(()=>[Pe(" Cerrar ")]),_:1})]),default:F(()=>[rt(a)?(Ae(),ut(it,{key:0,icon:rt(a)},null,8,["icon"])):tn("",!0),Pe(" "+ie(rt(s))+" ",1)]),_:1},8,["modelValue","timeout","color"])):tn("",!0)]))}};function Rm(e,t){return function(){return e.apply(t,arguments)}}const{toString:N0}=Object.prototype,{getPrototypeOf:iu}=Object,ji=(e=>t=>{const n=N0.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),cn=e=>(e=e.toLowerCase(),t=>ji(t)===e),Wi=e=>t=>typeof t===e,{isArray:er}=Array,jr=Wi("undefined");function F0(e){return e!==null&&!jr(e)&&e.constructor!==null&&!jr(e.constructor)&&Bt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const $m=cn("ArrayBuffer");function L0(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&$m(e.buffer),t}const R0=Wi("string"),Bt=Wi("function"),Bm=Wi("number"),qi=e=>e!==null&&typeof e=="object",$0=e=>e===!0||e===!1,Ba=e=>{if(ji(e)!=="object")return!1;const t=iu(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},B0=cn("Date"),H0=cn("File"),U0=cn("Blob"),z0=cn("FileList"),j0=e=>qi(e)&&Bt(e.pipe),W0=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Bt(e.append)&&((t=ji(e))==="formdata"||t==="object"&&Bt(e.toString)&&e.toString()==="[object FormData]"))},q0=cn("URLSearchParams"),Z0=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ua(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),er(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const a=n?Object.getOwnPropertyNames(e):Object.keys(e),i=a.length;let o;for(s=0;s<i;s++)o=a[s],t.call(null,e[o],o,e)}}function Hm(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const Um=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),zm=e=>!jr(e)&&e!==Um;function al(){const{caseless:e}=zm(this)&&this||{},t={},n=(s,r)=>{const a=e&&Hm(t,r)||r;Ba(t[a])&&Ba(s)?t[a]=al(t[a],s):Ba(s)?t[a]=al({},s):er(s)?t[a]=s.slice():t[a]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&ua(arguments[s],n);return t}const G0=(e,t,n,{allOwnKeys:s}={})=>(ua(t,(r,a)=>{n&&Bt(r)?e[a]=Rm(r,n):e[a]=r},{allOwnKeys:s}),e),K0=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Y0=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},J0=(e,t,n,s)=>{let r,a,i;const o={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),a=r.length;a-- >0;)i=r[a],(!s||s(i,e,t))&&!o[i]&&(t[i]=e[i],o[i]=!0);e=n!==!1&&iu(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},X0=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Q0=e=>{if(!e)return null;if(er(e))return e;let t=e.length;if(!Bm(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},e1=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&iu(Uint8Array)),t1=(e,t)=>{const s=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=s.next())&&!r.done;){const a=r.value;t.call(e,a[0],a[1])}},n1=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},s1=cn("HTMLFormElement"),r1=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),Xc=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),a1=cn("RegExp"),jm=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};ua(n,(r,a)=>{t(r,a,e)!==!1&&(s[a]=r)}),Object.defineProperties(e,s)},i1=e=>{jm(e,(t,n)=>{if(Bt(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Bt(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},o1=(e,t)=>{const n={},s=r=>{r.forEach(a=>{n[a]=!0})};return er(e)?s(e):s(String(e).split(t)),n},l1=()=>{},u1=(e,t)=>(e=+e,Number.isFinite(e)?e:t),So="abcdefghijklmnopqrstuvwxyz",Qc="0123456789",Wm={DIGIT:Qc,ALPHA:So,ALPHA_DIGIT:So+So.toUpperCase()+Qc},c1=(e=16,t=Wm.ALPHA_DIGIT)=>{let n="";const{length:s}=t;for(;e--;)n+=t[Math.random()*s|0];return n};function d1(e){return!!(e&&Bt(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const f1=e=>{const t=new Array(10),n=(s,r)=>{if(qi(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const a=er(s)?[]:{};return ua(s,(i,o)=>{const l=n(i,r+1);!jr(l)&&(a[o]=l)}),t[r]=void 0,a}}return s};return n(e,0)},m1=cn("AsyncFunction"),h1=e=>e&&(qi(e)||Bt(e))&&Bt(e.then)&&Bt(e.catch),D={isArray:er,isArrayBuffer:$m,isBuffer:F0,isFormData:W0,isArrayBufferView:L0,isString:R0,isNumber:Bm,isBoolean:$0,isObject:qi,isPlainObject:Ba,isUndefined:jr,isDate:B0,isFile:H0,isBlob:U0,isRegExp:a1,isFunction:Bt,isStream:j0,isURLSearchParams:q0,isTypedArray:e1,isFileList:z0,forEach:ua,merge:al,extend:G0,trim:Z0,stripBOM:K0,inherits:Y0,toFlatObject:J0,kindOf:ji,kindOfTest:cn,endsWith:X0,toArray:Q0,forEachEntry:t1,matchAll:n1,isHTMLForm:s1,hasOwnProperty:Xc,hasOwnProp:Xc,reduceDescriptors:jm,freezeMethods:i1,toObjectSet:o1,toCamelCase:r1,noop:l1,toFiniteNumber:u1,findKey:Hm,global:Um,isContextDefined:zm,ALPHABET:Wm,generateString:c1,isSpecCompliantForm:d1,toJSONObject:f1,isAsyncFn:m1,isThenable:h1};function Ie(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r)}D.inherits(Ie,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const qm=Ie.prototype,Zm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Zm[e]={value:e}});Object.defineProperties(Ie,Zm);Object.defineProperty(qm,"isAxiosError",{value:!0});Ie.from=(e,t,n,s,r,a)=>{const i=Object.create(qm);return D.toFlatObject(e,i,function(l){return l!==Error.prototype},o=>o!=="isAxiosError"),Ie.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};const v1=null;function il(e){return D.isPlainObject(e)||D.isArray(e)}function Gm(e){return D.endsWith(e,"[]")?e.slice(0,-2):e}function ed(e,t,n){return e?e.concat(t).map(function(r,a){return r=Gm(r),!n&&a?"["+r+"]":r}).join(n?".":""):t}function g1(e){return D.isArray(e)&&!e.some(il)}const y1=D.toFlatObject(D,{},null,function(t){return/^is[A-Z]/.test(t)});function Zi(e,t,n){if(!D.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=D.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,b){return!D.isUndefined(b[g])});const s=n.metaTokens,r=n.visitor||c,a=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(t);if(!D.isFunction(r))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(D.isDate(v))return v.toISOString();if(!l&&D.isBlob(v))throw new Ie("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(v)||D.isTypedArray(v)?l&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,g,b){let _=v;if(v&&!b&&typeof v=="object"){if(D.endsWith(g,"{}"))g=s?g:g.slice(0,-2),v=JSON.stringify(v);else if(D.isArray(v)&&g1(v)||(D.isFileList(v)||D.endsWith(g,"[]"))&&(_=D.toArray(v)))return g=Gm(g),_.forEach(function(w,k){!(D.isUndefined(w)||w===null)&&t.append(i===!0?ed([g],k,a):i===null?g:g+"[]",u(w))}),!1}return il(v)?!0:(t.append(ed(b,g,a),u(v)),!1)}const d=[],f=Object.assign(y1,{defaultVisitor:c,convertValue:u,isVisitable:il});function h(v,g){if(!D.isUndefined(v)){if(d.indexOf(v)!==-1)throw Error("Circular reference detected in "+g.join("."));d.push(v),D.forEach(v,function(_,E){(!(D.isUndefined(_)||_===null)&&r.call(t,_,D.isString(E)?E.trim():E,g,f))===!0&&h(_,g?g.concat(E):[E])}),d.pop()}}if(!D.isObject(e))throw new TypeError("data must be an object");return h(e),t}function td(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function ou(e,t){this._pairs=[],e&&Zi(e,this,t)}const Km=ou.prototype;Km.append=function(t,n){this._pairs.push([t,n])};Km.toString=function(t){const n=t?function(s){return t.call(this,s,td)}:td;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function p1(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ym(e,t,n){if(!t)return e;const s=n&&n.encode||p1,r=n&&n.serialize;let a;if(r?a=r(t,n):a=D.isURLSearchParams(t)?t.toString():new ou(t,n).toString(s),a){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+a}return e}class b1{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){D.forEach(this.handlers,function(s){s!==null&&t(s)})}}const nd=b1,Jm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},w1=typeof URLSearchParams<"u"?URLSearchParams:ou,S1=typeof FormData<"u"?FormData:null,C1=typeof Blob<"u"?Blob:null,x1=(()=>{let e;return typeof navigator<"u"&&((e=navigator.product)==="ReactNative"||e==="NativeScript"||e==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),_1=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),rn={isBrowser:!0,classes:{URLSearchParams:w1,FormData:S1,Blob:C1},isStandardBrowserEnv:x1,isStandardBrowserWebWorkerEnv:_1,protocols:["http","https","file","blob","url","data"]};function E1(e,t){return Zi(e,new rn.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,a){return rn.isNode&&D.isBuffer(n)?(this.append(s,n.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},t))}function T1(e){return D.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function k1(e){const t={},n=Object.keys(e);let s;const r=n.length;let a;for(s=0;s<r;s++)a=n[s],t[a]=e[a];return t}function Xm(e){function t(n,s,r,a){let i=n[a++];const o=Number.isFinite(+i),l=a>=n.length;return i=!i&&D.isArray(r)?r.length:i,l?(D.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!o):((!r[i]||!D.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],a)&&D.isArray(r[i])&&(r[i]=k1(r[i])),!o)}if(D.isFormData(e)&&D.isFunction(e.entries)){const n={};return D.forEachEntry(e,(s,r)=>{t(T1(s),r,n,0)}),n}return null}const O1={"Content-Type":void 0};function I1(e,t,n){if(D.isString(e))try{return(t||JSON.parse)(e),D.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Gi={transitional:Jm,adapter:["xhr","http"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,a=D.isObject(t);if(a&&D.isHTMLForm(t)&&(t=new FormData(t)),D.isFormData(t))return r&&r?JSON.stringify(Xm(t)):t;if(D.isArrayBuffer(t)||D.isBuffer(t)||D.isStream(t)||D.isFile(t)||D.isBlob(t))return t;if(D.isArrayBufferView(t))return t.buffer;if(D.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(a){if(s.indexOf("application/x-www-form-urlencoded")>-1)return E1(t,this.formSerializer).toString();if((o=D.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Zi(o?{"files[]":t}:t,l&&new l,this.formSerializer)}}return a||r?(n.setContentType("application/json",!1),I1(t)):t}],transformResponse:[function(t){const n=this.transitional||Gi.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(t&&D.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(o){if(i)throw o.name==="SyntaxError"?Ie.from(o,Ie.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rn.classes.FormData,Blob:rn.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};D.forEach(["delete","get","head"],function(t){Gi.headers[t]={}});D.forEach(["post","put","patch"],function(t){Gi.headers[t]=D.merge(O1)});const lu=Gi,A1=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),V1=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&A1[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},sd=Symbol("internals");function cr(e){return e&&String(e).trim().toLowerCase()}function Ha(e){return e===!1||e==null?e:D.isArray(e)?e.map(Ha):String(e)}function P1(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const M1=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Co(e,t,n,s,r){if(D.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!D.isString(t)){if(D.isString(s))return t.indexOf(s)!==-1;if(D.isRegExp(s))return s.test(t)}}function D1(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function N1(e,t){const n=D.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,a,i){return this[s].call(this,t,r,a,i)},configurable:!0})})}class Ki{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function a(o,l,u){const c=cr(l);if(!c)throw new Error("header name must be a non-empty string");const d=D.findKey(r,c);(!d||r[d]===void 0||u===!0||u===void 0&&r[d]!==!1)&&(r[d||l]=Ha(o))}const i=(o,l)=>D.forEach(o,(u,c)=>a(u,c,l));return D.isPlainObject(t)||t instanceof this.constructor?i(t,n):D.isString(t)&&(t=t.trim())&&!M1(t)?i(V1(t),n):t!=null&&a(n,t,s),this}get(t,n){if(t=cr(t),t){const s=D.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return P1(r);if(D.isFunction(n))return n.call(this,r,s);if(D.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=cr(t),t){const s=D.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Co(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function a(i){if(i=cr(i),i){const o=D.findKey(s,i);o&&(!n||Co(s,s[o],o,n))&&(delete s[o],r=!0)}}return D.isArray(t)?t.forEach(a):a(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const a=n[s];(!t||Co(this,this[a],a,t,!0))&&(delete this[a],r=!0)}return r}normalize(t){const n=this,s={};return D.forEach(this,(r,a)=>{const i=D.findKey(s,a);if(i){n[i]=Ha(r),delete n[a];return}const o=t?D1(a):String(a).trim();o!==a&&delete n[a],n[o]=Ha(r),s[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return D.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&D.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[sd]=this[sd]={accessors:{}}).accessors,r=this.prototype;function a(i){const o=cr(i);s[o]||(N1(r,i),s[o]=!0)}return D.isArray(t)?t.forEach(a):a(t),this}}Ki.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.freezeMethods(Ki.prototype);D.freezeMethods(Ki);const yn=Ki;function xo(e,t){const n=this||lu,s=t||n,r=yn.from(s.headers);let a=s.data;return D.forEach(e,function(o){a=o.call(n,a,r.normalize(),t?t.status:void 0)}),r.normalize(),a}function Qm(e){return!!(e&&e.__CANCEL__)}function ca(e,t,n){Ie.call(this,e??"canceled",Ie.ERR_CANCELED,t,n),this.name="CanceledError"}D.inherits(ca,Ie,{__CANCEL__:!0});function F1(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new Ie("Request failed with status code "+n.status,[Ie.ERR_BAD_REQUEST,Ie.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}const L1=rn.isStandardBrowserEnv?function(){return{write:function(n,s,r,a,i,o){const l=[];l.push(n+"="+encodeURIComponent(s)),D.isNumber(r)&&l.push("expires="+new Date(r).toGMTString()),D.isString(a)&&l.push("path="+a),D.isString(i)&&l.push("domain="+i),o===!0&&l.push("secure"),document.cookie=l.join("; ")},read:function(n){const s=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return s?decodeURIComponent(s[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function R1(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function $1(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function eh(e,t){return e&&!R1(t)?$1(e,t):t}const B1=rn.isStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let s;function r(a){let i=a;return t&&(n.setAttribute("href",i),i=n.href),n.setAttribute("href",i),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return s=r(window.location.href),function(i){const o=D.isString(i)?r(i):i;return o.protocol===s.protocol&&o.host===s.host}}():function(){return function(){return!0}}();function H1(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function U1(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,a=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=s[a];i||(i=u),n[r]=l,s[r]=u;let d=a,f=0;for(;d!==r;)f+=n[d++],d=d%e;if(r=(r+1)%e,r===a&&(a=(a+1)%e),u-i<t)return;const h=c&&u-c;return h?Math.round(f*1e3/h):void 0}}function rd(e,t){let n=0;const s=U1(50,250);return r=>{const a=r.loaded,i=r.lengthComputable?r.total:void 0,o=a-n,l=s(o),u=a<=i;n=a;const c={loaded:a,total:i,progress:i?a/i:void 0,bytes:o,rate:l||void 0,estimated:l&&i&&u?(i-a)/l:void 0,event:r};c[t?"download":"upload"]=!0,e(c)}}const z1=typeof XMLHttpRequest<"u",j1=z1&&function(e){return new Promise(function(n,s){let r=e.data;const a=yn.from(e.headers).normalize(),i=e.responseType;let o;function l(){e.cancelToken&&e.cancelToken.unsubscribe(o),e.signal&&e.signal.removeEventListener("abort",o)}D.isFormData(r)&&(rn.isStandardBrowserEnv||rn.isStandardBrowserWebWorkerEnv?a.setContentType(!1):a.setContentType("multipart/form-data;",!1));let u=new XMLHttpRequest;if(e.auth){const h=e.auth.username||"",v=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";a.set("Authorization","Basic "+btoa(h+":"+v))}const c=eh(e.baseURL,e.url);u.open(e.method.toUpperCase(),Ym(c,e.params,e.paramsSerializer),!0),u.timeout=e.timeout;function d(){if(!u)return;const h=yn.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders()),g={data:!i||i==="text"||i==="json"?u.responseText:u.response,status:u.status,statusText:u.statusText,headers:h,config:e,request:u};F1(function(_){n(_),l()},function(_){s(_),l()},g),u=null}if("onloadend"in u?u.onloadend=d:u.onreadystatechange=function(){!u||u.readyState!==4||u.status===0&&!(u.responseURL&&u.responseURL.indexOf("file:")===0)||setTimeout(d)},u.onabort=function(){u&&(s(new Ie("Request aborted",Ie.ECONNABORTED,e,u)),u=null)},u.onerror=function(){s(new Ie("Network Error",Ie.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let v=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const g=e.transitional||Jm;e.timeoutErrorMessage&&(v=e.timeoutErrorMessage),s(new Ie(v,g.clarifyTimeoutError?Ie.ETIMEDOUT:Ie.ECONNABORTED,e,u)),u=null},rn.isStandardBrowserEnv){const h=(e.withCredentials||B1(c))&&e.xsrfCookieName&&L1.read(e.xsrfCookieName);h&&a.set(e.xsrfHeaderName,h)}r===void 0&&a.setContentType(null),"setRequestHeader"in u&&D.forEach(a.toJSON(),function(v,g){u.setRequestHeader(g,v)}),D.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),i&&i!=="json"&&(u.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&u.addEventListener("progress",rd(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&u.upload&&u.upload.addEventListener("progress",rd(e.onUploadProgress)),(e.cancelToken||e.signal)&&(o=h=>{u&&(s(!h||h.type?new ca(null,e,u):h),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(o),e.signal&&(e.signal.aborted?o():e.signal.addEventListener("abort",o)));const f=H1(c);if(f&&rn.protocols.indexOf(f)===-1){s(new Ie("Unsupported protocol "+f+":",Ie.ERR_BAD_REQUEST,e));return}u.send(r||null)})},Ua={http:v1,xhr:j1};D.forEach(Ua,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const W1={getAdapter:e=>{e=D.isArray(e)?e:[e];const{length:t}=e;let n,s;for(let r=0;r<t&&(n=e[r],!(s=D.isString(n)?Ua[n.toLowerCase()]:n));r++);if(!s)throw s===!1?new Ie(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(D.hasOwnProp(Ua,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`);if(!D.isFunction(s))throw new TypeError("adapter is not a function");return s},adapters:Ua};function _o(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ca(null,e)}function ad(e){return _o(e),e.headers=yn.from(e.headers),e.data=xo.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),W1.getAdapter(e.adapter||lu.adapter)(e).then(function(s){return _o(e),s.data=xo.call(e,e.transformResponse,s),s.headers=yn.from(s.headers),s},function(s){return Qm(s)||(_o(e),s&&s.response&&(s.response.data=xo.call(e,e.transformResponse,s.response),s.response.headers=yn.from(s.response.headers))),Promise.reject(s)})}const id=e=>e instanceof yn?e.toJSON():e;function Ws(e,t){t=t||{};const n={};function s(u,c,d){return D.isPlainObject(u)&&D.isPlainObject(c)?D.merge.call({caseless:d},u,c):D.isPlainObject(c)?D.merge({},c):D.isArray(c)?c.slice():c}function r(u,c,d){if(D.isUndefined(c)){if(!D.isUndefined(u))return s(void 0,u,d)}else return s(u,c,d)}function a(u,c){if(!D.isUndefined(c))return s(void 0,c)}function i(u,c){if(D.isUndefined(c)){if(!D.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function o(u,c,d){if(d in t)return s(u,c);if(d in e)return s(void 0,u)}const l={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:o,headers:(u,c)=>r(id(u),id(c),!0)};return D.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=l[c]||r,f=d(e[c],t[c],c);D.isUndefined(f)&&d!==o||(n[c]=f)}),n}const th="1.4.0",uu={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{uu[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const od={};uu.transitional=function(t,n,s){function r(a,i){return"[Axios v"+th+"] Transitional option '"+a+"'"+i+(s?". "+s:"")}return(a,i,o)=>{if(t===!1)throw new Ie(r(i," has been removed"+(n?" in "+n:"")),Ie.ERR_DEPRECATED);return n&&!od[i]&&(od[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(a,i,o):!0}};function q1(e,t,n){if(typeof e!="object")throw new Ie("options must be an object",Ie.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const a=s[r],i=t[a];if(i){const o=e[a],l=o===void 0||i(o,a,e);if(l!==!0)throw new Ie("option "+a+" must be "+l,Ie.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Ie("Unknown option "+a,Ie.ERR_BAD_OPTION)}}const ol={assertOptions:q1,validators:uu},Mn=ol.validators;class ci{constructor(t){this.defaults=t,this.interceptors={request:new nd,response:new nd}}request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ws(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:a}=n;s!==void 0&&ol.assertOptions(s,{silentJSONParsing:Mn.transitional(Mn.boolean),forcedJSONParsing:Mn.transitional(Mn.boolean),clarifyTimeoutError:Mn.transitional(Mn.boolean)},!1),r!=null&&(D.isFunction(r)?n.paramsSerializer={serialize:r}:ol.assertOptions(r,{encode:Mn.function,serialize:Mn.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i;i=a&&D.merge(a.common,a[n.method]),i&&D.forEach(["delete","get","head","post","put","patch","common"],v=>{delete a[v]}),n.headers=yn.concat(i,a);const o=[];let l=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(l=l&&g.synchronous,o.unshift(g.fulfilled,g.rejected))});const u=[];this.interceptors.response.forEach(function(g){u.push(g.fulfilled,g.rejected)});let c,d=0,f;if(!l){const v=[ad.bind(this),void 0];for(v.unshift.apply(v,o),v.push.apply(v,u),f=v.length,c=Promise.resolve(n);d<f;)c=c.then(v[d++],v[d++]);return c}f=o.length;let h=n;for(d=0;d<f;){const v=o[d++],g=o[d++];try{h=v(h)}catch(b){g.call(this,b);break}}try{c=ad.call(this,h)}catch(v){return Promise.reject(v)}for(d=0,f=u.length;d<f;)c=c.then(u[d++],u[d++]);return c}getUri(t){t=Ws(this.defaults,t);const n=eh(t.baseURL,t.url);return Ym(n,t.params,t.paramsSerializer)}}D.forEach(["delete","get","head","options"],function(t){ci.prototype[t]=function(n,s){return this.request(Ws(s||{},{method:t,url:n,data:(s||{}).data}))}});D.forEach(["post","put","patch"],function(t){function n(s){return function(a,i,o){return this.request(Ws(o||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:a,data:i}))}}ci.prototype[t]=n(),ci.prototype[t+"Form"]=n(!0)});const za=ci;class cu{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(a){n=a});const s=this;this.promise.then(r=>{if(!s._listeners)return;let a=s._listeners.length;for(;a-- >0;)s._listeners[a](r);s._listeners=null}),this.promise.then=r=>{let a;const i=new Promise(o=>{s.subscribe(o),a=o}).then(r);return i.cancel=function(){s.unsubscribe(a)},i},t(function(a,i,o){s.reason||(s.reason=new ca(a,i,o),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new cu(function(r){t=r}),cancel:t}}}const Z1=cu;function G1(e){return function(n){return e.apply(null,n)}}function K1(e){return D.isObject(e)&&e.isAxiosError===!0}const ll={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ll).forEach(([e,t])=>{ll[t]=e});const Y1=ll;function nh(e){const t=new za(e),n=Rm(za.prototype.request,t);return D.extend(n,za.prototype,t,{allOwnKeys:!0}),D.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return nh(Ws(e,r))},n}const ot=nh(lu);ot.Axios=za;ot.CanceledError=ca;ot.CancelToken=Z1;ot.isCancel=Qm;ot.VERSION=th;ot.toFormData=Zi;ot.AxiosError=Ie;ot.Cancel=ot.CanceledError;ot.all=function(t){return Promise.all(t)};ot.spread=G1;ot.isAxiosError=K1;ot.mergeConfig=Ws;ot.AxiosHeaders=yn;ot.formToJSON=e=>Xm(D.isHTMLForm(e)?new FormData(e):e);ot.HttpStatusCode=Y1;ot.default=ot;const J1=ot,Yi=window.location.origin,Ji=window.location.pathname.split("/");`${Yi}${Ji[1]}`;`${Yi}${Ji[1]}`;const X1=`${Yi}/${Ji[1]}/logout.php`,Q1=`${Yi}/${Ji[1]}`,du=window.location.pathname;du.split("/");console.log("path",du);du[1];const fu=window.location.href,ew=fu.lastIndexOf("/"),tw=fu.lastIndexOf("/",ew-1),nw=fu.slice(0,tw),gt=J1.create();var sw=localStorage.getItem("token");gt.interceptors.request.use(async e=>(e.baseURL=nw,e.headers={"Content-Type":"application/json",Authorization:`Bearer ${sw}`,Accept:"application/json","Access-Control-Allow-Methods":"GET,PUT,POST"},e.withCredentials=!1,e.crossDomain=!0,e),e=>{Promise.reject(e)});gt.interceptors.response.use(e=>e,async function(e){if((e==null?void 0:e.response.status)==401){window.parent.location.replace=X1;return}return(e==null?void 0:e.response.status)==404&&(window.parent.location.href=Q1),Promise.reject(e)});const ja=U([]),hs="Para acceder a este acción, su cuenta debe estar habilitada. Comuníquese con el administrador del sistema.",vs=(e,t=null,n=null)=>{if(ja&&ja.length==0)return!1;var s=ja.value.includes(e);return s?!(t&&form.boleta!=="0"):!1},rw=()=>{var e=localStorage.getItem("pms"),t=[];e=JSON.parse(e),e&&e.map(n=>{const s=atob(n);t.push(s)}),ja.value=t};rw();class Es extends Error{}class aw extends Es{constructor(t){super(`Invalid DateTime: ${t.toMessage()}`)}}class iw extends Es{constructor(t){super(`Invalid Interval: ${t.toMessage()}`)}}class ow extends Es{constructor(t){super(`Invalid Duration: ${t.toMessage()}`)}}class gr extends Es{}class sh extends Es{constructor(t){super(`Invalid unit ${t}`)}}class Ft extends Es{}class Dn extends Es{constructor(){super("Zone is an abstract class")}}const Q="numeric",Jt="short",Ot="long",di={year:Q,month:Q,day:Q},rh={year:Q,month:Jt,day:Q},lw={year:Q,month:Jt,day:Q,weekday:Jt},ah={year:Q,month:Ot,day:Q},ih={year:Q,month:Ot,day:Q,weekday:Ot},oh={hour:Q,minute:Q},lh={hour:Q,minute:Q,second:Q},uh={hour:Q,minute:Q,second:Q,timeZoneName:Jt},ch={hour:Q,minute:Q,second:Q,timeZoneName:Ot},dh={hour:Q,minute:Q,hourCycle:"h23"},fh={hour:Q,minute:Q,second:Q,hourCycle:"h23"},mh={hour:Q,minute:Q,second:Q,hourCycle:"h23",timeZoneName:Jt},hh={hour:Q,minute:Q,second:Q,hourCycle:"h23",timeZoneName:Ot},vh={year:Q,month:Q,day:Q,hour:Q,minute:Q},gh={year:Q,month:Q,day:Q,hour:Q,minute:Q,second:Q},yh={year:Q,month:Jt,day:Q,hour:Q,minute:Q},ph={year:Q,month:Jt,day:Q,hour:Q,minute:Q,second:Q},uw={year:Q,month:Jt,day:Q,weekday:Jt,hour:Q,minute:Q},bh={year:Q,month:Ot,day:Q,hour:Q,minute:Q,timeZoneName:Jt},wh={year:Q,month:Ot,day:Q,hour:Q,minute:Q,second:Q,timeZoneName:Jt},Sh={year:Q,month:Ot,day:Q,weekday:Ot,hour:Q,minute:Q,timeZoneName:Ot},Ch={year:Q,month:Ot,day:Q,weekday:Ot,hour:Q,minute:Q,second:Q,timeZoneName:Ot};class da{get type(){throw new Dn}get name(){throw new Dn}get ianaName(){return this.name}get isUniversal(){throw new Dn}offsetName(t,n){throw new Dn}formatOffset(t,n){throw new Dn}offset(t){throw new Dn}equals(t){throw new Dn}get isValid(){throw new Dn}}let Eo=null;class Xi extends da{static get instance(){return Eo===null&&(Eo=new Xi),Eo}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(t,{format:n,locale:s}){return _h(t,n,s)}formatOffset(t,n){return Er(this.offset(t),n)}offset(t){return-new Date(t).getTimezoneOffset()}equals(t){return t.type==="system"}get isValid(){return!0}}let Wa={};function cw(e){return Wa[e]||(Wa[e]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),Wa[e]}const dw={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function fw(e,t){const n=e.format(t).replace(/\u200E/g,""),s=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,r,a,i,o,l,u,c]=s;return[i,r,a,o,l,u,c]}function mw(e,t){const n=e.formatToParts(t),s=[];for(let r=0;r<n.length;r++){const{type:a,value:i}=n[r],o=dw[a];a==="era"?s[o]=i:Te(o)||(s[o]=parseInt(i,10))}return s}let Ta={};class Cn extends da{static create(t){return Ta[t]||(Ta[t]=new Cn(t)),Ta[t]}static resetCache(){Ta={},Wa={}}static isValidSpecifier(t){return this.isValidZone(t)}static isValidZone(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch{return!1}}constructor(t){super(),this.zoneName=t,this.valid=Cn.isValidZone(t)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(t,{format:n,locale:s}){return _h(t,n,s,this.name)}formatOffset(t,n){return Er(this.offset(t),n)}offset(t){const n=new Date(t);if(isNaN(n))return NaN;const s=cw(this.name);let[r,a,i,o,l,u,c]=s.formatToParts?mw(s,n):fw(s,n);o==="BC"&&(r=-Math.abs(r)+1);const f=vu({year:r,month:a,day:i,hour:l===24?0:l,minute:u,second:c,millisecond:0});let h=+n;const v=h%1e3;return h-=v>=0?v:1e3+v,(f-h)/(60*1e3)}equals(t){return t.type==="iana"&&t.name===this.name}get isValid(){return this.valid}}let ld={};function hw(e,t={}){const n=JSON.stringify([e,t]);let s=ld[n];return s||(s=new Intl.ListFormat(e,t),ld[n]=s),s}let ul={};function cl(e,t={}){const n=JSON.stringify([e,t]);let s=ul[n];return s||(s=new Intl.DateTimeFormat(e,t),ul[n]=s),s}let dl={};function vw(e,t={}){const n=JSON.stringify([e,t]);let s=dl[n];return s||(s=new Intl.NumberFormat(e,t),dl[n]=s),s}let fl={};function gw(e,t={}){const{base:n,...s}=t,r=JSON.stringify([e,s]);let a=fl[r];return a||(a=new Intl.RelativeTimeFormat(e,t),fl[r]=a),a}let yr=null;function yw(){return yr||(yr=new Intl.DateTimeFormat().resolvedOptions().locale,yr)}function pw(e){const t=e.indexOf("-x-");t!==-1&&(e=e.substring(0,t));const n=e.indexOf("-u-");if(n===-1)return[e];{let s,r;try{s=cl(e).resolvedOptions(),r=e}catch{const l=e.substring(0,n);s=cl(l).resolvedOptions(),r=l}const{numberingSystem:a,calendar:i}=s;return[r,a,i]}}function bw(e,t,n){return(n||t)&&(e.includes("-u-")||(e+="-u"),n&&(e+=`-ca-${n}`),t&&(e+=`-nu-${t}`)),e}function ww(e){const t=[];for(let n=1;n<=12;n++){const s=re.utc(2016,n,1);t.push(e(s))}return t}function Sw(e){const t=[];for(let n=1;n<=7;n++){const s=re.utc(2016,11,13+n);t.push(e(s))}return t}function ka(e,t,n,s,r){const a=e.listingMode(n);return a==="error"?null:a==="en"?s(t):r(t)}function Cw(e){return e.numberingSystem&&e.numberingSystem!=="latn"?!1:e.numberingSystem==="latn"||!e.locale||e.locale.startsWith("en")||new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem==="latn"}class xw{constructor(t,n,s){this.padTo=s.padTo||0,this.floor=s.floor||!1;const{padTo:r,floor:a,...i}=s;if(!n||Object.keys(i).length>0){const o={useGrouping:!1,...s};s.padTo>0&&(o.minimumIntegerDigits=s.padTo),this.inf=vw(t,o)}}format(t){if(this.inf){const n=this.floor?Math.floor(t):t;return this.inf.format(n)}else{const n=this.floor?Math.floor(t):hu(t,3);return nt(n,this.padTo)}}}class _w{constructor(t,n,s){this.opts=s,this.originalZone=void 0;let r;if(this.opts.timeZone)this.dt=t;else if(t.zone.type==="fixed"){const i=-1*(t.offset/60),o=i>=0?`Etc/GMT+${i}`:`Etc/GMT${i}`;t.offset!==0&&Cn.create(o).valid?(r=o,this.dt=t):(r="UTC",this.dt=t.offset===0?t:t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone)}else t.zone.type==="system"?this.dt=t:t.zone.type==="iana"?(this.dt=t,r=t.zone.name):(r="UTC",this.dt=t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone);const a={...this.opts};a.timeZone=a.timeZone||r,this.dtf=cl(n,a)}format(){return this.originalZone?this.formatToParts().map(({value:t})=>t).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const t=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?t.map(n=>{if(n.type==="timeZoneName"){const s=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:s}}else return n}):t}resolvedOptions(){return this.dtf.resolvedOptions()}}class Ew{constructor(t,n,s){this.opts={style:"long",...s},!n&&xh()&&(this.rtf=gw(t,s))}format(t,n){return this.rtf?this.rtf.format(t,n):Uw(n,t,this.opts.numeric,this.opts.style!=="long")}formatToParts(t,n){return this.rtf?this.rtf.formatToParts(t,n):[]}}class He{static fromOpts(t){return He.create(t.locale,t.numberingSystem,t.outputCalendar,t.defaultToEN)}static create(t,n,s,r=!1){const a=t||Je.defaultLocale,i=a||(r?"en-US":yw()),o=n||Je.defaultNumberingSystem,l=s||Je.defaultOutputCalendar;return new He(i,o,l,a)}static resetCache(){yr=null,ul={},dl={},fl={}}static fromObject({locale:t,numberingSystem:n,outputCalendar:s}={}){return He.create(t,n,s)}constructor(t,n,s,r){const[a,i,o]=pw(t);this.locale=a,this.numberingSystem=n||i||null,this.outputCalendar=s||o||null,this.intl=bw(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=r,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=Cw(this)),this.fastNumbersCached}listingMode(){const t=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return t&&n?"en":"intl"}clone(t){return!t||Object.getOwnPropertyNames(t).length===0?this:He.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,t.defaultToEN||!1)}redefaultToEN(t={}){return this.clone({...t,defaultToEN:!0})}redefaultToSystem(t={}){return this.clone({...t,defaultToEN:!1})}months(t,n=!1,s=!0){return ka(this,t,s,kh,()=>{const r=n?{month:t,day:"numeric"}:{month:t},a=n?"format":"standalone";return this.monthsCache[a][t]||(this.monthsCache[a][t]=ww(i=>this.extract(i,r,"month"))),this.monthsCache[a][t]})}weekdays(t,n=!1,s=!0){return ka(this,t,s,Ah,()=>{const r=n?{weekday:t,year:"numeric",month:"long",day:"numeric"}:{weekday:t},a=n?"format":"standalone";return this.weekdaysCache[a][t]||(this.weekdaysCache[a][t]=Sw(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[a][t]})}meridiems(t=!0){return ka(this,void 0,t,()=>Vh,()=>{if(!this.meridiemCache){const n={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[re.utc(2016,11,13,9),re.utc(2016,11,13,19)].map(s=>this.extract(s,n,"dayperiod"))}return this.meridiemCache})}eras(t,n=!0){return ka(this,t,n,Ph,()=>{const s={era:t};return this.eraCache[t]||(this.eraCache[t]=[re.utc(-40,1,1),re.utc(2017,1,1)].map(r=>this.extract(r,s,"era"))),this.eraCache[t]})}extract(t,n,s){const r=this.dtFormatter(t,n),a=r.formatToParts(),i=a.find(o=>o.type.toLowerCase()===s);return i?i.value:null}numberFormatter(t={}){return new xw(this.intl,t.forceSimple||this.fastNumbers,t)}dtFormatter(t,n={}){return new _w(t,this.intl,n)}relFormatter(t={}){return new Ew(this.intl,this.isEnglish(),t)}listFormatter(t={}){return hw(this.intl,t)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar}}let To=null;class yt extends da{static get utcInstance(){return To===null&&(To=new yt(0)),To}static instance(t){return t===0?yt.utcInstance:new yt(t)}static parseSpecifier(t){if(t){const n=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new yt(eo(n[1],n[2]))}return null}constructor(t){super(),this.fixed=t}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${Er(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${Er(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(t,n){return Er(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(t){return t.type==="fixed"&&t.fixed===this.fixed}get isValid(){return!0}}class Tw extends da{constructor(t){super(),this.zoneName=t}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function Bn(e,t){if(Te(e)||e===null)return t;if(e instanceof da)return e;if(kw(e)){const n=e.toLowerCase();return n==="default"?t:n==="local"||n==="system"?Xi.instance:n==="utc"||n==="gmt"?yt.utcInstance:yt.parseSpecifier(n)||Cn.create(e)}else return bs(e)?yt.instance(e):typeof e=="object"&&e.offset&&typeof e.offset=="number"?e:new Tw(e)}let ud=()=>Date.now(),cd="system",dd=null,fd=null,md=null,hd=60,vd;class Je{static get now(){return ud}static set now(t){ud=t}static set defaultZone(t){cd=t}static get defaultZone(){return Bn(cd,Xi.instance)}static get defaultLocale(){return dd}static set defaultLocale(t){dd=t}static get defaultNumberingSystem(){return fd}static set defaultNumberingSystem(t){fd=t}static get defaultOutputCalendar(){return md}static set defaultOutputCalendar(t){md=t}static get twoDigitCutoffYear(){return hd}static set twoDigitCutoffYear(t){hd=t%100}static get throwOnInvalid(){return vd}static set throwOnInvalid(t){vd=t}static resetCaches(){He.resetCache(),Cn.resetCache()}}function Te(e){return typeof e>"u"}function bs(e){return typeof e=="number"}function Qi(e){return typeof e=="number"&&e%1===0}function kw(e){return typeof e=="string"}function Ow(e){return Object.prototype.toString.call(e)==="[object Date]"}function xh(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function Iw(e){return Array.isArray(e)?e:[e]}function gd(e,t,n){if(e.length!==0)return e.reduce((s,r)=>{const a=[t(r),r];return s&&n(s[0],a[0])===s[0]?s:a},null)[1]}function Aw(e,t){return t.reduce((n,s)=>(n[s]=e[s],n),{})}function qs(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function vn(e,t,n){return Qi(e)&&e>=t&&e<=n}function Vw(e,t){return e-t*Math.floor(e/t)}function nt(e,t=2){const n=e<0;let s;return n?s="-"+(""+-e).padStart(t,"0"):s=(""+e).padStart(t,"0"),s}function $n(e){if(!(Te(e)||e===null||e===""))return parseInt(e,10)}function is(e){if(!(Te(e)||e===null||e===""))return parseFloat(e)}function mu(e){if(!(Te(e)||e===null||e==="")){const t=parseFloat("0."+e)*1e3;return Math.floor(t)}}function hu(e,t,n=!1){const s=10**t;return(n?Math.trunc:Math.round)(e*s)/s}function fa(e){return e%4===0&&(e%100!==0||e%400===0)}function _r(e){return fa(e)?366:365}function fi(e,t){const n=Vw(t-1,12)+1,s=e+(t-n)/12;return n===2?fa(s)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function vu(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t),t.setUTCFullYear(e.year,e.month-1,e.day)),+t}function mi(e){const t=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7,n=e-1,s=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return t===4||s===3?53:52}function ml(e){return e>99?e:e>Je.twoDigitCutoffYear?1900+e:2e3+e}function _h(e,t,n,s=null){const r=new Date(e),a={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};s&&(a.timeZone=s);const i={timeZoneName:t,...a},o=new Intl.DateTimeFormat(n,i).formatToParts(r).find(l=>l.type.toLowerCase()==="timezonename");return o?o.value:null}function eo(e,t){let n=parseInt(e,10);Number.isNaN(n)&&(n=0);const s=parseInt(t,10)||0,r=n<0||Object.is(n,-0)?-s:s;return n*60+r}function Eh(e){const t=Number(e);if(typeof e=="boolean"||e===""||Number.isNaN(t))throw new Ft(`Invalid unit value ${e}`);return t}function hi(e,t){const n={};for(const s in e)if(qs(e,s)){const r=e[s];if(r==null)continue;n[t(s)]=Eh(r)}return n}function Er(e,t){const n=Math.trunc(Math.abs(e/60)),s=Math.trunc(Math.abs(e%60)),r=e>=0?"+":"-";switch(t){case"short":return`${r}${nt(n,2)}:${nt(s,2)}`;case"narrow":return`${r}${n}${s>0?`:${s}`:""}`;case"techie":return`${r}${nt(n,2)}${nt(s,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function to(e){return Aw(e,["hour","minute","second","millisecond"])}const Pw=["January","February","March","April","May","June","July","August","September","October","November","December"],Th=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Mw=["J","F","M","A","M","J","J","A","S","O","N","D"];function kh(e){switch(e){case"narrow":return[...Mw];case"short":return[...Th];case"long":return[...Pw];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const Oh=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Ih=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Dw=["M","T","W","T","F","S","S"];function Ah(e){switch(e){case"narrow":return[...Dw];case"short":return[...Ih];case"long":return[...Oh];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Vh=["AM","PM"],Nw=["Before Christ","Anno Domini"],Fw=["BC","AD"],Lw=["B","A"];function Ph(e){switch(e){case"narrow":return[...Lw];case"short":return[...Fw];case"long":return[...Nw];default:return null}}function Rw(e){return Vh[e.hour<12?0:1]}function $w(e,t){return Ah(t)[e.weekday-1]}function Bw(e,t){return kh(t)[e.month-1]}function Hw(e,t){return Ph(t)[e.year<0?0:1]}function Uw(e,t,n="always",s=!1){const r={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},a=["hours","minutes","seconds"].indexOf(e)===-1;if(n==="auto"&&a){const d=e==="days";switch(t){case 1:return d?"tomorrow":`next ${r[e][0]}`;case-1:return d?"yesterday":`last ${r[e][0]}`;case 0:return d?"today":`this ${r[e][0]}`}}const i=Object.is(t,-0)||t<0,o=Math.abs(t),l=o===1,u=r[e],c=s?l?u[1]:u[2]||u[1]:l?r[e][0]:e;return i?`${o} ${c} ago`:`in ${o} ${c}`}function yd(e,t){let n="";for(const s of e)s.literal?n+=s.val:n+=t(s.val);return n}const zw={D:di,DD:rh,DDD:ah,DDDD:ih,t:oh,tt:lh,ttt:uh,tttt:ch,T:dh,TT:fh,TTT:mh,TTTT:hh,f:vh,ff:yh,fff:bh,ffff:Sh,F:gh,FF:ph,FFF:wh,FFFF:Ch};class mt{static create(t,n={}){return new mt(t,n)}static parseFormat(t){let n=null,s="",r=!1;const a=[];for(let i=0;i<t.length;i++){const o=t.charAt(i);o==="'"?(s.length>0&&a.push({literal:r||/^\s+$/.test(s),val:s}),n=null,s="",r=!r):r||o===n?s+=o:(s.length>0&&a.push({literal:/^\s+$/.test(s),val:s}),s=o,n=o)}return s.length>0&&a.push({literal:r||/^\s+$/.test(s),val:s}),a}static macroTokenToFormatOpts(t){return zw[t]}constructor(t,n){this.opts=n,this.loc=t,this.systemLoc=null}formatWithSystemDefault(t,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,{...this.opts,...n}).format()}formatDateTime(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n}).format()}formatDateTimeParts(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n}).formatToParts()}formatInterval(t,n={}){return this.loc.dtFormatter(t.start,{...this.opts,...n}).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())}resolvedOptions(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n}).resolvedOptions()}num(t,n=0){if(this.opts.forceSimple)return nt(t,n);const s={...this.opts};return n>0&&(s.padTo=n),this.loc.numberFormatter(s).format(t)}formatDateTimeFromString(t,n){const s=this.loc.listingMode()==="en",r=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",a=(h,v)=>this.loc.extract(t,h,v),i=h=>t.isOffsetFixed&&t.offset===0&&h.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,h.format):"",o=()=>s?Rw(t):a({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(h,v)=>s?Bw(t,h):a(v?{month:h}:{month:h,day:"numeric"},"month"),u=(h,v)=>s?$w(t,h):a(v?{weekday:h}:{weekday:h,month:"long",day:"numeric"},"weekday"),c=h=>{const v=mt.macroTokenToFormatOpts(h);return v?this.formatWithSystemDefault(t,v):h},d=h=>s?Hw(t,h):a({era:h},"era"),f=h=>{switch(h){case"S":return this.num(t.millisecond);case"u":case"SSS":return this.num(t.millisecond,3);case"s":return this.num(t.second);case"ss":return this.num(t.second,2);case"uu":return this.num(Math.floor(t.millisecond/10),2);case"uuu":return this.num(Math.floor(t.millisecond/100));case"m":return this.num(t.minute);case"mm":return this.num(t.minute,2);case"h":return this.num(t.hour%12===0?12:t.hour%12);case"hh":return this.num(t.hour%12===0?12:t.hour%12,2);case"H":return this.num(t.hour);case"HH":return this.num(t.hour,2);case"Z":return i({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return i({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return i({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:this.loc.locale});case"z":return t.zoneName;case"a":return o();case"d":return r?a({day:"numeric"},"day"):this.num(t.day);case"dd":return r?a({day:"2-digit"},"day"):this.num(t.day,2);case"c":return this.num(t.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(t.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return r?a({month:"numeric",day:"numeric"},"month"):this.num(t.month);case"LL":return r?a({month:"2-digit",day:"numeric"},"month"):this.num(t.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return r?a({month:"numeric"},"month"):this.num(t.month);case"MM":return r?a({month:"2-digit"},"month"):this.num(t.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return r?a({year:"numeric"},"year"):this.num(t.year);case"yy":return r?a({year:"2-digit"},"year"):this.num(t.year.toString().slice(-2),2);case"yyyy":return r?a({year:"numeric"},"year"):this.num(t.year,4);case"yyyyyy":return r?a({year:"numeric"},"year"):this.num(t.year,6);case"G":return d("short");case"GG":return d("long");case"GGGGG":return d("narrow");case"kk":return this.num(t.weekYear.toString().slice(-2),2);case"kkkk":return this.num(t.weekYear,4);case"W":return this.num(t.weekNumber);case"WW":return this.num(t.weekNumber,2);case"o":return this.num(t.ordinal);case"ooo":return this.num(t.ordinal,3);case"q":return this.num(t.quarter);case"qq":return this.num(t.quarter,2);case"X":return this.num(Math.floor(t.ts/1e3));case"x":return this.num(t.ts);default:return c(h)}};return yd(mt.parseFormat(n),f)}formatDurationFromString(t,n){const s=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},r=l=>u=>{const c=s(u);return c?this.num(l.get(c),u.length):u},a=mt.parseFormat(n),i=a.reduce((l,{literal:u,val:c})=>u?l:l.concat(c),[]),o=t.shiftTo(...i.map(s).filter(l=>l));return yd(a,r(o))}}class Gt{constructor(t,n){this.reason=t,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const Mh=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function tr(...e){const t=e.reduce((n,s)=>n+s.source,"");return RegExp(`^${t}$`)}function nr(...e){return t=>e.reduce(([n,s,r],a)=>{const[i,o,l]=a(t,r);return[{...n,...i},o||s,l]},[{},null,1]).slice(0,2)}function sr(e,...t){if(e==null)return[null,null];for(const[n,s]of t){const r=n.exec(e);if(r)return s(r)}return[null,null]}function Dh(...e){return(t,n)=>{const s={};let r;for(r=0;r<e.length;r++)s[e[r]]=$n(t[n+r]);return[s,null,n+r]}}const Nh=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,jw=`(?:${Nh.source}?(?:\\[(${Mh.source})\\])?)?`,gu=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Fh=RegExp(`${gu.source}${jw}`),yu=RegExp(`(?:T${Fh.source})?`),Ww=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,qw=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Zw=/(\d{4})-?(\d{3})/,Gw=Dh("weekYear","weekNumber","weekDay"),Kw=Dh("year","ordinal"),Yw=/(\d{4})-(\d\d)-(\d\d)/,Lh=RegExp(`${gu.source} ?(?:${Nh.source}|(${Mh.source}))?`),Jw=RegExp(`(?: ${Lh.source})?`);function Us(e,t,n){const s=e[t];return Te(s)?n:$n(s)}function Xw(e,t){return[{year:Us(e,t),month:Us(e,t+1,1),day:Us(e,t+2,1)},null,t+3]}function rr(e,t){return[{hours:Us(e,t,0),minutes:Us(e,t+1,0),seconds:Us(e,t+2,0),milliseconds:mu(e[t+3])},null,t+4]}function ma(e,t){const n=!e[t]&&!e[t+1],s=eo(e[t+1],e[t+2]),r=n?null:yt.instance(s);return[{},r,t+3]}function ha(e,t){const n=e[t]?Cn.create(e[t]):null;return[{},n,t+1]}const Qw=RegExp(`^T?${gu.source}$`),eS=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function tS(e){const[t,n,s,r,a,i,o,l,u]=e,c=t[0]==="-",d=l&&l[0]==="-",f=(h,v=!1)=>h!==void 0&&(v||h&&c)?-h:h;return[{years:f(is(n)),months:f(is(s)),weeks:f(is(r)),days:f(is(a)),hours:f(is(i)),minutes:f(is(o)),seconds:f(is(l),l==="-0"),milliseconds:f(mu(u),d)}]}const nS={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function pu(e,t,n,s,r,a,i){const o={year:t.length===2?ml($n(t)):$n(t),month:Th.indexOf(n)+1,day:$n(s),hour:$n(r),minute:$n(a)};return i&&(o.second=$n(i)),e&&(o.weekday=e.length>3?Oh.indexOf(e)+1:Ih.indexOf(e)+1),o}const sS=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function rS(e){const[,t,n,s,r,a,i,o,l,u,c,d]=e,f=pu(t,r,s,n,a,i,o);let h;return l?h=nS[l]:u?h=0:h=eo(c,d),[f,new yt(h)]}function aS(e){return e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}const iS=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,oS=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,lS=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function pd(e){const[,t,n,s,r,a,i,o]=e;return[pu(t,r,s,n,a,i,o),yt.utcInstance]}function uS(e){const[,t,n,s,r,a,i,o]=e;return[pu(t,o,n,s,r,a,i),yt.utcInstance]}const cS=tr(Ww,yu),dS=tr(qw,yu),fS=tr(Zw,yu),mS=tr(Fh),Rh=nr(Xw,rr,ma,ha),hS=nr(Gw,rr,ma,ha),vS=nr(Kw,rr,ma,ha),gS=nr(rr,ma,ha);function yS(e){return sr(e,[cS,Rh],[dS,hS],[fS,vS],[mS,gS])}function pS(e){return sr(aS(e),[sS,rS])}function bS(e){return sr(e,[iS,pd],[oS,pd],[lS,uS])}function wS(e){return sr(e,[eS,tS])}const SS=nr(rr);function CS(e){return sr(e,[Qw,SS])}const xS=tr(Yw,Jw),_S=tr(Lh),ES=nr(rr,ma,ha);function TS(e){return sr(e,[xS,Rh],[_S,ES])}const kS="Invalid Duration",$h={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},OS={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...$h},Nt=146097/400,Vs=146097/4800,IS={years:{quarters:4,months:12,weeks:Nt/7,days:Nt,hours:Nt*24,minutes:Nt*24*60,seconds:Nt*24*60*60,milliseconds:Nt*24*60*60*1e3},quarters:{months:3,weeks:Nt/28,days:Nt/4,hours:Nt*24/4,minutes:Nt*24*60/4,seconds:Nt*24*60*60/4,milliseconds:Nt*24*60*60*1e3/4},months:{weeks:Vs/7,days:Vs,hours:Vs*24,minutes:Vs*24*60,seconds:Vs*24*60*60,milliseconds:Vs*24*60*60*1e3},...$h},os=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],AS=os.slice(0).reverse();function Nn(e,t,n=!1){const s={values:n?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix};return new ke(s)}function VS(e){return e<0?Math.floor(e):Math.ceil(e)}function Bh(e,t,n,s,r){const a=e[r][n],i=t[n]/a,o=Math.sign(i)===Math.sign(s[r]),l=!o&&s[r]!==0&&Math.abs(i)<=1?VS(i):Math.trunc(i);s[r]+=l,t[n]-=l*a}function PS(e,t){AS.reduce((n,s)=>Te(t[s])?n:(n&&Bh(e,t,n,t,s),s),null)}function MS(e){const t={};for(const[n,s]of Object.entries(e))s!==0&&(t[n]=s);return t}class ke{constructor(t){const n=t.conversionAccuracy==="longterm"||!1;let s=n?IS:OS;t.matrix&&(s=t.matrix),this.values=t.values,this.loc=t.loc||He.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=s,this.isLuxonDuration=!0}static fromMillis(t,n){return ke.fromObject({milliseconds:t},n)}static fromObject(t,n={}){if(t==null||typeof t!="object")throw new Ft(`Duration.fromObject: argument expected to be an object, got ${t===null?"null":typeof t}`);return new ke({values:hi(t,ke.normalizeUnit),loc:He.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(t){if(bs(t))return ke.fromMillis(t);if(ke.isDuration(t))return t;if(typeof t=="object")return ke.fromObject(t);throw new Ft(`Unknown duration argument ${t} of type ${typeof t}`)}static fromISO(t,n){const[s]=wS(t);return s?ke.fromObject(s,n):ke.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static fromISOTime(t,n){const[s]=CS(t);return s?ke.fromObject(s,n):ke.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static invalid(t,n=null){if(!t)throw new Ft("need to specify a reason the Duration is invalid");const s=t instanceof Gt?t:new Gt(t,n);if(Je.throwOnInvalid)throw new ow(s);return new ke({invalid:s})}static normalizeUnit(t){const n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t&&t.toLowerCase()];if(!n)throw new sh(t);return n}static isDuration(t){return t&&t.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(t,n={}){const s={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?mt.create(this.loc,s).formatDurationFromString(this,t):kS}toHuman(t={}){const n=os.map(s=>{const r=this.values[s];return Te(r)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...t,unit:s.slice(0,-1)}).format(r)}).filter(s=>s);return this.loc.listFormatter({type:"conjunction",style:t.listStyle||"narrow",...t}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let t="P";return this.years!==0&&(t+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(t+=this.months+this.quarters*3+"M"),this.weeks!==0&&(t+=this.weeks+"W"),this.days!==0&&(t+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(t+="T"),this.hours!==0&&(t+=this.hours+"H"),this.minutes!==0&&(t+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(t+=hu(this.seconds+this.milliseconds/1e3,3)+"S"),t==="P"&&(t+="T0S"),t}toISOTime(t={}){if(!this.isValid)return null;const n=this.toMillis();if(n<0||n>=864e5)return null;t={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...t};const s=this.shiftTo("hours","minutes","seconds","milliseconds");let r=t.format==="basic"?"hhmm":"hh:mm";(!t.suppressSeconds||s.seconds!==0||s.milliseconds!==0)&&(r+=t.format==="basic"?"ss":":ss",(!t.suppressMilliseconds||s.milliseconds!==0)&&(r+=".SSS"));let a=s.toFormat(r);return t.includePrefix&&(a="T"+a),a}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.as("milliseconds")}valueOf(){return this.toMillis()}plus(t){if(!this.isValid)return this;const n=ke.fromDurationLike(t),s={};for(const r of os)(qs(n.values,r)||qs(this.values,r))&&(s[r]=n.get(r)+this.get(r));return Nn(this,{values:s},!0)}minus(t){if(!this.isValid)return this;const n=ke.fromDurationLike(t);return this.plus(n.negate())}mapUnits(t){if(!this.isValid)return this;const n={};for(const s of Object.keys(this.values))n[s]=Eh(t(this.values[s],s));return Nn(this,{values:n},!0)}get(t){return this[ke.normalizeUnit(t)]}set(t){if(!this.isValid)return this;const n={...this.values,...hi(t,ke.normalizeUnit)};return Nn(this,{values:n})}reconfigure({locale:t,numberingSystem:n,conversionAccuracy:s,matrix:r}={}){const i={loc:this.loc.clone({locale:t,numberingSystem:n}),matrix:r,conversionAccuracy:s};return Nn(this,i)}as(t){return this.isValid?this.shiftTo(t).get(t):NaN}normalize(){if(!this.isValid)return this;const t=this.toObject();return PS(this.matrix,t),Nn(this,{values:t},!0)}rescale(){if(!this.isValid)return this;const t=MS(this.normalize().shiftToAll().toObject());return Nn(this,{values:t},!0)}shiftTo(...t){if(!this.isValid)return this;if(t.length===0)return this;t=t.map(i=>ke.normalizeUnit(i));const n={},s={},r=this.toObject();let a;for(const i of os)if(t.indexOf(i)>=0){a=i;let o=0;for(const u in s)o+=this.matrix[u][i]*s[u],s[u]=0;bs(r[i])&&(o+=r[i]);const l=Math.trunc(o);n[i]=l,s[i]=(o*1e3-l*1e3)/1e3;for(const u in r)os.indexOf(u)>os.indexOf(i)&&Bh(this.matrix,r,u,n,i)}else bs(r[i])&&(s[i]=r[i]);for(const i in s)s[i]!==0&&(n[a]+=i===a?s[i]:s[i]/this.matrix[a][i]);return Nn(this,{values:n},!0).normalize()}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const t={};for(const n of Object.keys(this.values))t[n]=this.values[n]===0?0:-this.values[n];return Nn(this,{values:t},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(t){if(!this.isValid||!t.isValid||!this.loc.equals(t.loc))return!1;function n(s,r){return s===void 0||s===0?r===void 0||r===0:s===r}for(const s of os)if(!n(this.values[s],t.values[s]))return!1;return!0}}const Ps="Invalid Interval";function DS(e,t){return!e||!e.isValid?qe.invalid("missing or invalid start"):!t||!t.isValid?qe.invalid("missing or invalid end"):t<e?qe.invalid("end before start",`The end of an interval must be after its start, but you had start=${e.toISO()} and end=${t.toISO()}`):null}class qe{constructor(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}static invalid(t,n=null){if(!t)throw new Ft("need to specify a reason the Interval is invalid");const s=t instanceof Gt?t:new Gt(t,n);if(Je.throwOnInvalid)throw new iw(s);return new qe({invalid:s})}static fromDateTimes(t,n){const s=mr(t),r=mr(n),a=DS(s,r);return a??new qe({start:s,end:r})}static after(t,n){const s=ke.fromDurationLike(n),r=mr(t);return qe.fromDateTimes(r,r.plus(s))}static before(t,n){const s=ke.fromDurationLike(n),r=mr(t);return qe.fromDateTimes(r.minus(s),r)}static fromISO(t,n){const[s,r]=(t||"").split("/",2);if(s&&r){let a,i;try{a=re.fromISO(s,n),i=a.isValid}catch{i=!1}let o,l;try{o=re.fromISO(r,n),l=o.isValid}catch{l=!1}if(i&&l)return qe.fromDateTimes(a,o);if(i){const u=ke.fromISO(r,n);if(u.isValid)return qe.after(a,u)}else if(l){const u=ke.fromISO(s,n);if(u.isValid)return qe.before(o,u)}}return qe.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static isInterval(t){return t&&t.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(t="milliseconds"){return this.isValid?this.toDuration(t).get(t):NaN}count(t="milliseconds"){if(!this.isValid)return NaN;const n=this.start.startOf(t),s=this.end.startOf(t);return Math.floor(s.diff(n,t).get(t))+(s.valueOf()!==this.end.valueOf())}hasSame(t){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,t):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(t){return this.isValid?this.s>t:!1}isBefore(t){return this.isValid?this.e<=t:!1}contains(t){return this.isValid?this.s<=t&&this.e>t:!1}set({start:t,end:n}={}){return this.isValid?qe.fromDateTimes(t||this.s,n||this.e):this}splitAt(...t){if(!this.isValid)return[];const n=t.map(mr).filter(i=>this.contains(i)).sort(),s=[];let{s:r}=this,a=0;for(;r<this.e;){const i=n[a]||this.e,o=+i>+this.e?this.e:i;s.push(qe.fromDateTimes(r,o)),r=o,a+=1}return s}splitBy(t){const n=ke.fromDurationLike(t);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s}=this,r=1,a;const i=[];for(;s<this.e;){const o=this.start.plus(n.mapUnits(l=>l*r));a=+o>+this.e?this.e:o,i.push(qe.fromDateTimes(s,a)),s=a,r+=1}return i}divideEqually(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]}overlaps(t){return this.e>t.s&&this.s<t.e}abutsStart(t){return this.isValid?+this.e==+t.s:!1}abutsEnd(t){return this.isValid?+t.e==+this.s:!1}engulfs(t){return this.isValid?this.s<=t.s&&this.e>=t.e:!1}equals(t){return!this.isValid||!t.isValid?!1:this.s.equals(t.s)&&this.e.equals(t.e)}intersection(t){if(!this.isValid)return this;const n=this.s>t.s?this.s:t.s,s=this.e<t.e?this.e:t.e;return n>=s?null:qe.fromDateTimes(n,s)}union(t){if(!this.isValid)return this;const n=this.s<t.s?this.s:t.s,s=this.e>t.e?this.e:t.e;return qe.fromDateTimes(n,s)}static merge(t){const[n,s]=t.sort((r,a)=>r.s-a.s).reduce(([r,a],i)=>a?a.overlaps(i)||a.abutsStart(i)?[r,a.union(i)]:[r.concat([a]),i]:[r,i],[[],null]);return s&&n.push(s),n}static xor(t){let n=null,s=0;const r=[],a=t.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),i=Array.prototype.concat(...a),o=i.sort((l,u)=>l.time-u.time);for(const l of o)s+=l.type==="s"?1:-1,s===1?n=l.time:(n&&+n!=+l.time&&r.push(qe.fromDateTimes(n,l.time)),n=null);return qe.merge(r)}difference(...t){return qe.xor([this].concat(t)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:Ps}toLocaleString(t=di,n={}){return this.isValid?mt.create(this.s.loc.clone(n),t).formatInterval(this):Ps}toISO(t){return this.isValid?`${this.s.toISO(t)}/${this.e.toISO(t)}`:Ps}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Ps}toISOTime(t){return this.isValid?`${this.s.toISOTime(t)}/${this.e.toISOTime(t)}`:Ps}toFormat(t,{separator:n=" – "}={}){return this.isValid?`${this.s.toFormat(t)}${n}${this.e.toFormat(t)}`:Ps}toDuration(t,n){return this.isValid?this.e.diff(this.s,t,n):ke.invalid(this.invalidReason)}mapEndpoints(t){return qe.fromDateTimes(t(this.s),t(this.e))}}class Oa{static hasDST(t=Je.defaultZone){const n=re.now().setZone(t).set({month:12});return!t.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(t){return Cn.isValidZone(t)}static normalizeZone(t){return Bn(t,Je.defaultZone)}static months(t="long",{locale:n=null,numberingSystem:s=null,locObj:r=null,outputCalendar:a="gregory"}={}){return(r||He.create(n,s,a)).months(t)}static monthsFormat(t="long",{locale:n=null,numberingSystem:s=null,locObj:r=null,outputCalendar:a="gregory"}={}){return(r||He.create(n,s,a)).months(t,!0)}static weekdays(t="long",{locale:n=null,numberingSystem:s=null,locObj:r=null}={}){return(r||He.create(n,s,null)).weekdays(t)}static weekdaysFormat(t="long",{locale:n=null,numberingSystem:s=null,locObj:r=null}={}){return(r||He.create(n,s,null)).weekdays(t,!0)}static meridiems({locale:t=null}={}){return He.create(t).meridiems()}static eras(t="short",{locale:n=null}={}){return He.create(n,null,"gregory").eras(t)}static features(){return{relative:xh()}}}function bd(e,t){const n=r=>r.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),s=n(t)-n(e);return Math.floor(ke.fromMillis(s).as("days"))}function NS(e,t,n){const s=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{const c=bd(l,u);return(c-c%7)/7}],["days",bd]],r={},a=e;let i,o;for(const[l,u]of s)n.indexOf(l)>=0&&(i=l,r[l]=u(e,t),o=a.plus(r),o>t?(r[l]--,e=a.plus(r)):e=o);return[e,r,o,i]}function FS(e,t,n,s){let[r,a,i,o]=NS(e,t,n);const l=t-r,u=n.filter(d=>["hours","minutes","seconds","milliseconds"].indexOf(d)>=0);u.length===0&&(i<t&&(i=r.plus({[o]:1})),i!==r&&(a[o]=(a[o]||0)+l/(i-r)));const c=ke.fromObject(a,s);return u.length>0?ke.fromMillis(l,s).shiftTo(...u).plus(c):c}const bu={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},wd={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},LS=bu.hanidec.replace(/[\[|\]]/g,"").split("");function RS(e){let t=parseInt(e,10);if(isNaN(t)){t="";for(let n=0;n<e.length;n++){const s=e.charCodeAt(n);if(e[n].search(bu.hanidec)!==-1)t+=LS.indexOf(e[n]);else for(const r in wd){const[a,i]=wd[r];s>=a&&s<=i&&(t+=s-a)}}return parseInt(t,10)}else return t}function jt({numberingSystem:e},t=""){return new RegExp(`${bu[e||"latn"]}${t}`)}const $S="missing Intl.DateTimeFormat.formatToParts support";function Ve(e,t=n=>n){return{regex:e,deser:([n])=>t(RS(n))}}const BS=String.fromCharCode(160),Hh=`[ ${BS}]`,Uh=new RegExp(Hh,"g");function HS(e){return e.replace(/\./g,"\\.?").replace(Uh,Hh)}function Sd(e){return e.replace(/\./g,"").replace(Uh," ").toLowerCase()}function Wt(e,t){return e===null?null:{regex:RegExp(e.map(HS).join("|")),deser:([n])=>e.findIndex(s=>Sd(n)===Sd(s))+t}}function Cd(e,t){return{regex:e,deser:([,n,s])=>eo(n,s),groups:t}}function Ia(e){return{regex:e,deser:([t])=>t}}function US(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function zS(e,t){const n=jt(t),s=jt(t,"{2}"),r=jt(t,"{3}"),a=jt(t,"{4}"),i=jt(t,"{6}"),o=jt(t,"{1,2}"),l=jt(t,"{1,3}"),u=jt(t,"{1,6}"),c=jt(t,"{1,9}"),d=jt(t,"{2,4}"),f=jt(t,"{4,6}"),h=b=>({regex:RegExp(US(b.val)),deser:([_])=>_,literal:!0}),g=(b=>{if(e.literal)return h(b);switch(b.val){case"G":return Wt(t.eras("short",!1),0);case"GG":return Wt(t.eras("long",!1),0);case"y":return Ve(u);case"yy":return Ve(d,ml);case"yyyy":return Ve(a);case"yyyyy":return Ve(f);case"yyyyyy":return Ve(i);case"M":return Ve(o);case"MM":return Ve(s);case"MMM":return Wt(t.months("short",!0,!1),1);case"MMMM":return Wt(t.months("long",!0,!1),1);case"L":return Ve(o);case"LL":return Ve(s);case"LLL":return Wt(t.months("short",!1,!1),1);case"LLLL":return Wt(t.months("long",!1,!1),1);case"d":return Ve(o);case"dd":return Ve(s);case"o":return Ve(l);case"ooo":return Ve(r);case"HH":return Ve(s);case"H":return Ve(o);case"hh":return Ve(s);case"h":return Ve(o);case"mm":return Ve(s);case"m":return Ve(o);case"q":return Ve(o);case"qq":return Ve(s);case"s":return Ve(o);case"ss":return Ve(s);case"S":return Ve(l);case"SSS":return Ve(r);case"u":return Ia(c);case"uu":return Ia(o);case"uuu":return Ve(n);case"a":return Wt(t.meridiems(),0);case"kkkk":return Ve(a);case"kk":return Ve(d,ml);case"W":return Ve(o);case"WW":return Ve(s);case"E":case"c":return Ve(n);case"EEE":return Wt(t.weekdays("short",!1,!1),1);case"EEEE":return Wt(t.weekdays("long",!1,!1),1);case"ccc":return Wt(t.weekdays("short",!0,!1),1);case"cccc":return Wt(t.weekdays("long",!0,!1),1);case"Z":case"ZZ":return Cd(new RegExp(`([+-]${o.source})(?::(${s.source}))?`),2);case"ZZZ":return Cd(new RegExp(`([+-]${o.source})(${s.source})?`),2);case"z":return Ia(/[a-z_+-/]{1,256}?/i);case" ":return Ia(/[^\S\n\r]/);default:return h(b)}})(e)||{invalidReason:$S};return g.token=e,g}const jS={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour:{numeric:"h","2-digit":"hh"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function WS(e,t){const{type:n,value:s}=e;if(n==="literal"){const i=/^\s+$/.test(s);return{literal:!i,val:i?" ":s}}const r=t[n];let a=jS[n];if(typeof a=="object"&&(a=a[r]),a)return{literal:!1,val:a}}function qS(e){return[`^${e.map(n=>n.regex).reduce((n,s)=>`${n}(${s.source})`,"")}$`,e]}function ZS(e,t,n){const s=e.match(t);if(s){const r={};let a=1;for(const i in n)if(qs(n,i)){const o=n[i],l=o.groups?o.groups+1:1;!o.literal&&o.token&&(r[o.token.val[0]]=o.deser(s.slice(a,a+l))),a+=l}return[s,r]}else return[s,{}]}function GS(e){const t=a=>{switch(a){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}};let n=null,s;return Te(e.z)||(n=Cn.create(e.z)),Te(e.Z)||(n||(n=new yt(e.Z)),s=e.Z),Te(e.q)||(e.M=(e.q-1)*3+1),Te(e.h)||(e.h<12&&e.a===1?e.h+=12:e.h===12&&e.a===0&&(e.h=0)),e.G===0&&e.y&&(e.y=-e.y),Te(e.u)||(e.S=mu(e.u)),[Object.keys(e).reduce((a,i)=>{const o=t(i);return o&&(a[o]=e[i]),a},{}),n,s]}let ko=null;function KS(){return ko||(ko=re.fromMillis(1555555555555)),ko}function YS(e,t){if(e.literal)return e;const n=mt.macroTokenToFormatOpts(e.val),s=Wh(n,t);return s==null||s.includes(void 0)?e:s}function zh(e,t){return Array.prototype.concat(...e.map(n=>YS(n,t)))}function jh(e,t,n){const s=zh(mt.parseFormat(n),e),r=s.map(i=>zS(i,e)),a=r.find(i=>i.invalidReason);if(a)return{input:t,tokens:s,invalidReason:a.invalidReason};{const[i,o]=qS(r),l=RegExp(i,"i"),[u,c]=ZS(t,l,o),[d,f,h]=c?GS(c):[null,null,void 0];if(qs(c,"a")&&qs(c,"H"))throw new gr("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:s,regex:l,rawMatches:u,matches:c,result:d,zone:f,specificOffset:h}}}function JS(e,t,n){const{result:s,zone:r,specificOffset:a,invalidReason:i}=jh(e,t,n);return[s,r,a,i]}function Wh(e,t){return e?mt.create(t,e).formatDateTimeParts(KS()).map(r=>WS(r,e)):null}const qh=[0,31,59,90,120,151,181,212,243,273,304,334],Zh=[0,31,60,91,121,152,182,213,244,274,305,335];function Rt(e,t){return new Gt("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function Gh(e,t,n){const s=new Date(Date.UTC(e,t-1,n));e<100&&e>=0&&s.setUTCFullYear(s.getUTCFullYear()-1900);const r=s.getUTCDay();return r===0?7:r}function Kh(e,t,n){return n+(fa(e)?Zh:qh)[t-1]}function Yh(e,t){const n=fa(e)?Zh:qh,s=n.findIndex(a=>a<t),r=t-n[s];return{month:s+1,day:r}}function hl(e){const{year:t,month:n,day:s}=e,r=Kh(t,n,s),a=Gh(t,n,s);let i=Math.floor((r-a+10)/7),o;return i<1?(o=t-1,i=mi(o)):i>mi(t)?(o=t+1,i=1):o=t,{weekYear:o,weekNumber:i,weekday:a,...to(e)}}function xd(e){const{weekYear:t,weekNumber:n,weekday:s}=e,r=Gh(t,1,4),a=_r(t);let i=n*7+s-r-3,o;i<1?(o=t-1,i+=_r(o)):i>a?(o=t+1,i-=_r(t)):o=t;const{month:l,day:u}=Yh(o,i);return{year:o,month:l,day:u,...to(e)}}function Oo(e){const{year:t,month:n,day:s}=e,r=Kh(t,n,s);return{year:t,ordinal:r,...to(e)}}function _d(e){const{year:t,ordinal:n}=e,{month:s,day:r}=Yh(t,n);return{year:t,month:s,day:r,...to(e)}}function XS(e){const t=Qi(e.weekYear),n=vn(e.weekNumber,1,mi(e.weekYear)),s=vn(e.weekday,1,7);return t?n?s?!1:Rt("weekday",e.weekday):Rt("week",e.week):Rt("weekYear",e.weekYear)}function QS(e){const t=Qi(e.year),n=vn(e.ordinal,1,_r(e.year));return t?n?!1:Rt("ordinal",e.ordinal):Rt("year",e.year)}function Jh(e){const t=Qi(e.year),n=vn(e.month,1,12),s=vn(e.day,1,fi(e.year,e.month));return t?n?s?!1:Rt("day",e.day):Rt("month",e.month):Rt("year",e.year)}function Xh(e){const{hour:t,minute:n,second:s,millisecond:r}=e,a=vn(t,0,23)||t===24&&n===0&&s===0&&r===0,i=vn(n,0,59),o=vn(s,0,59),l=vn(r,0,999);return a?i?o?l?!1:Rt("millisecond",r):Rt("second",s):Rt("minute",n):Rt("hour",t)}const Io="Invalid DateTime",Ed=864e13;function Aa(e){return new Gt("unsupported zone",`the zone "${e.name}" is not supported`)}function Ao(e){return e.weekData===null&&(e.weekData=hl(e.c)),e.weekData}function dr(e,t){const n={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new re({...n,...t,old:n})}function Qh(e,t,n){let s=e-t*60*1e3;const r=n.offset(s);if(t===r)return[s,t];s-=(r-t)*60*1e3;const a=n.offset(s);return r===a?[s,r]:[e-Math.min(r,a)*60*1e3,Math.max(r,a)]}function Td(e,t){e+=t*60*1e3;const n=new Date(e);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function qa(e,t,n){return Qh(vu(e),t,n)}function kd(e,t){const n=e.o,s=e.c.year+Math.trunc(t.years),r=e.c.month+Math.trunc(t.months)+Math.trunc(t.quarters)*3,a={...e.c,year:s,month:r,day:Math.min(e.c.day,fi(s,r))+Math.trunc(t.days)+Math.trunc(t.weeks)*7},i=ke.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),o=vu(a);let[l,u]=Qh(o,n,e.zone);return i!==0&&(l+=i,u=e.zone.offset(l)),{ts:l,o:u}}function fr(e,t,n,s,r,a){const{setZone:i,zone:o}=n;if(e&&Object.keys(e).length!==0||t){const l=t||o,u=re.fromObject(e,{...n,zone:l,specificOffset:a});return i?u:u.setZone(o)}else return re.invalid(new Gt("unparsable",`the input "${r}" can't be parsed as ${s}`))}function Va(e,t,n=!0){return e.isValid?mt.create(He.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function Vo(e,t){const n=e.c.year>9999||e.c.year<0;let s="";return n&&e.c.year>=0&&(s+="+"),s+=nt(e.c.year,n?6:4),t?(s+="-",s+=nt(e.c.month),s+="-",s+=nt(e.c.day)):(s+=nt(e.c.month),s+=nt(e.c.day)),s}function Od(e,t,n,s,r,a){let i=nt(e.c.hour);return t?(i+=":",i+=nt(e.c.minute),(e.c.second!==0||!n)&&(i+=":")):i+=nt(e.c.minute),(e.c.second!==0||!n)&&(i+=nt(e.c.second),(e.c.millisecond!==0||!s)&&(i+=".",i+=nt(e.c.millisecond,3))),r&&(e.isOffsetFixed&&e.offset===0&&!a?i+="Z":e.o<0?(i+="-",i+=nt(Math.trunc(-e.o/60)),i+=":",i+=nt(Math.trunc(-e.o%60))):(i+="+",i+=nt(Math.trunc(e.o/60)),i+=":",i+=nt(Math.trunc(e.o%60)))),a&&(i+="["+e.zone.ianaName+"]"),i}const ev={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},eC={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},tC={ordinal:1,hour:0,minute:0,second:0,millisecond:0},tv=["year","month","day","hour","minute","second","millisecond"],nC=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],sC=["year","ordinal","hour","minute","second","millisecond"];function Id(e){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new sh(e);return t}function Ad(e,t){const n=Bn(t.zone,Je.defaultZone),s=He.fromObject(t),r=Je.now();let a,i;if(Te(e.year))a=r;else{for(const u of tv)Te(e[u])&&(e[u]=ev[u]);const o=Jh(e)||Xh(e);if(o)return re.invalid(o);const l=n.offset(r);[a,i]=qa(e,l,n)}return new re({ts:a,zone:n,loc:s,o:i})}function Vd(e,t,n){const s=Te(n.round)?!0:n.round,r=(i,o)=>(i=hu(i,s||n.calendary?0:2,!0),t.loc.clone(n).relFormatter(n).format(i,o)),a=i=>n.calendary?t.hasSame(e,i)?0:t.startOf(i).diff(e.startOf(i),i).get(i):t.diff(e,i).get(i);if(n.unit)return r(a(n.unit),n.unit);for(const i of n.units){const o=a(i);if(Math.abs(o)>=1)return r(o,i)}return r(e>t?-0:0,n.units[n.units.length-1])}function Pd(e){let t={},n;return e.length>0&&typeof e[e.length-1]=="object"?(t=e[e.length-1],n=Array.from(e).slice(0,e.length-1)):n=Array.from(e),[t,n]}class re{constructor(t){const n=t.zone||Je.defaultZone;let s=t.invalid||(Number.isNaN(t.ts)?new Gt("invalid input"):null)||(n.isValid?null:Aa(n));this.ts=Te(t.ts)?Je.now():t.ts;let r=null,a=null;if(!s)if(t.old&&t.old.ts===this.ts&&t.old.zone.equals(n))[r,a]=[t.old.c,t.old.o];else{const o=n.offset(this.ts);r=Td(this.ts,o),s=Number.isNaN(r.year)?new Gt("invalid input"):null,r=s?null:r,a=s?null:o}this._zone=n,this.loc=t.loc||He.create(),this.invalid=s,this.weekData=null,this.c=r,this.o=a,this.isLuxonDateTime=!0}static now(){return new re({})}static local(){const[t,n]=Pd(arguments),[s,r,a,i,o,l,u]=n;return Ad({year:s,month:r,day:a,hour:i,minute:o,second:l,millisecond:u},t)}static utc(){const[t,n]=Pd(arguments),[s,r,a,i,o,l,u]=n;return t.zone=yt.utcInstance,Ad({year:s,month:r,day:a,hour:i,minute:o,second:l,millisecond:u},t)}static fromJSDate(t,n={}){const s=Ow(t)?t.valueOf():NaN;if(Number.isNaN(s))return re.invalid("invalid input");const r=Bn(n.zone,Je.defaultZone);return r.isValid?new re({ts:s,zone:r,loc:He.fromObject(n)}):re.invalid(Aa(r))}static fromMillis(t,n={}){if(bs(t))return t<-Ed||t>Ed?re.invalid("Timestamp out of range"):new re({ts:t,zone:Bn(n.zone,Je.defaultZone),loc:He.fromObject(n)});throw new Ft(`fromMillis requires a numerical input, but received a ${typeof t} with value ${t}`)}static fromSeconds(t,n={}){if(bs(t))return new re({ts:t*1e3,zone:Bn(n.zone,Je.defaultZone),loc:He.fromObject(n)});throw new Ft("fromSeconds requires a numerical input")}static fromObject(t,n={}){t=t||{};const s=Bn(n.zone,Je.defaultZone);if(!s.isValid)return re.invalid(Aa(s));const r=Je.now(),a=Te(n.specificOffset)?s.offset(r):n.specificOffset,i=hi(t,Id),o=!Te(i.ordinal),l=!Te(i.year),u=!Te(i.month)||!Te(i.day),c=l||u,d=i.weekYear||i.weekNumber,f=He.fromObject(n);if((c||o)&&d)throw new gr("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&o)throw new gr("Can't mix ordinal dates with month/day");const h=d||i.weekday&&!c;let v,g,b=Td(r,a);h?(v=nC,g=eC,b=hl(b)):o?(v=sC,g=tC,b=Oo(b)):(v=tv,g=ev);let _=!1;for(const y of v){const S=i[y];Te(S)?_?i[y]=g[y]:i[y]=b[y]:_=!0}const E=h?XS(i):o?QS(i):Jh(i),w=E||Xh(i);if(w)return re.invalid(w);const k=h?xd(i):o?_d(i):i,[P,O]=qa(k,a,s),T=new re({ts:P,zone:s,o:O,loc:f});return i.weekday&&c&&t.weekday!==T.weekday?re.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${T.toISO()}`):T}static fromISO(t,n={}){const[s,r]=yS(t);return fr(s,r,n,"ISO 8601",t)}static fromRFC2822(t,n={}){const[s,r]=pS(t);return fr(s,r,n,"RFC 2822",t)}static fromHTTP(t,n={}){const[s,r]=bS(t);return fr(s,r,n,"HTTP",n)}static fromFormat(t,n,s={}){if(Te(t)||Te(n))throw new Ft("fromFormat requires an input string and a format");const{locale:r=null,numberingSystem:a=null}=s,i=He.fromOpts({locale:r,numberingSystem:a,defaultToEN:!0}),[o,l,u,c]=JS(i,t,n);return c?re.invalid(c):fr(o,l,s,`format ${n}`,t,u)}static fromString(t,n,s={}){return re.fromFormat(t,n,s)}static fromSQL(t,n={}){const[s,r]=TS(t);return fr(s,r,n,"SQL",t)}static invalid(t,n=null){if(!t)throw new Ft("need to specify a reason the DateTime is invalid");const s=t instanceof Gt?t:new Gt(t,n);if(Je.throwOnInvalid)throw new aw(s);return new re({invalid:s})}static isDateTime(t){return t&&t.isLuxonDateTime||!1}static parseFormatForOpts(t,n={}){const s=Wh(t,He.fromObject(n));return s?s.map(r=>r?r.val:null).join(""):null}static expandFormat(t,n={}){return zh(mt.parseFormat(t),He.fromObject(n)).map(r=>r.val).join("")}get(t){return this[t]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Ao(this).weekYear:NaN}get weekNumber(){return this.isValid?Ao(this).weekNumber:NaN}get weekday(){return this.isValid?Ao(this).weekday:NaN}get ordinal(){return this.isValid?Oo(this.c).ordinal:NaN}get monthShort(){return this.isValid?Oa.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?Oa.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?Oa.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?Oa.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}get isInLeapYear(){return fa(this.year)}get daysInMonth(){return fi(this.year,this.month)}get daysInYear(){return this.isValid?_r(this.year):NaN}get weeksInWeekYear(){return this.isValid?mi(this.weekYear):NaN}resolvedLocaleOptions(t={}){const{locale:n,numberingSystem:s,calendar:r}=mt.create(this.loc.clone(t),t).resolvedOptions(this);return{locale:n,numberingSystem:s,outputCalendar:r}}toUTC(t=0,n={}){return this.setZone(yt.instance(t),n)}toLocal(){return this.setZone(Je.defaultZone)}setZone(t,{keepLocalTime:n=!1,keepCalendarTime:s=!1}={}){if(t=Bn(t,Je.defaultZone),t.equals(this.zone))return this;if(t.isValid){let r=this.ts;if(n||s){const a=t.offset(this.ts),i=this.toObject();[r]=qa(i,a,t)}return dr(this,{ts:r,zone:t})}else return re.invalid(Aa(t))}reconfigure({locale:t,numberingSystem:n,outputCalendar:s}={}){const r=this.loc.clone({locale:t,numberingSystem:n,outputCalendar:s});return dr(this,{loc:r})}setLocale(t){return this.reconfigure({locale:t})}set(t){if(!this.isValid)return this;const n=hi(t,Id),s=!Te(n.weekYear)||!Te(n.weekNumber)||!Te(n.weekday),r=!Te(n.ordinal),a=!Te(n.year),i=!Te(n.month)||!Te(n.day),o=a||i,l=n.weekYear||n.weekNumber;if((o||r)&&l)throw new gr("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(i&&r)throw new gr("Can't mix ordinal dates with month/day");let u;s?u=xd({...hl(this.c),...n}):Te(n.ordinal)?(u={...this.toObject(),...n},Te(n.day)&&(u.day=Math.min(fi(u.year,u.month),u.day))):u=_d({...Oo(this.c),...n});const[c,d]=qa(u,this.o,this.zone);return dr(this,{ts:c,o:d})}plus(t){if(!this.isValid)return this;const n=ke.fromDurationLike(t);return dr(this,kd(this,n))}minus(t){if(!this.isValid)return this;const n=ke.fromDurationLike(t).negate();return dr(this,kd(this,n))}startOf(t){if(!this.isValid)return this;const n={},s=ke.normalizeUnit(t);switch(s){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0;break}if(s==="weeks"&&(n.weekday=1),s==="quarters"){const r=Math.ceil(this.month/3);n.month=(r-1)*3+1}return this.set(n)}endOf(t){return this.isValid?this.plus({[t]:1}).startOf(t).minus(1):this}toFormat(t,n={}){return this.isValid?mt.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,t):Io}toLocaleString(t=di,n={}){return this.isValid?mt.create(this.loc.clone(n),t).formatDateTime(this):Io}toLocaleParts(t={}){return this.isValid?mt.create(this.loc.clone(t),t).formatDateTimeParts(this):[]}toISO({format:t="extended",suppressSeconds:n=!1,suppressMilliseconds:s=!1,includeOffset:r=!0,extendedZone:a=!1}={}){if(!this.isValid)return null;const i=t==="extended";let o=Vo(this,i);return o+="T",o+=Od(this,i,n,s,r,a),o}toISODate({format:t="extended"}={}){return this.isValid?Vo(this,t==="extended"):null}toISOWeekDate(){return Va(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:t=!1,suppressSeconds:n=!1,includeOffset:s=!0,includePrefix:r=!1,extendedZone:a=!1,format:i="extended"}={}){return this.isValid?(r?"T":"")+Od(this,i==="extended",n,t,s,a):null}toRFC2822(){return Va(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Va(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Vo(this,!0):null}toSQLTime({includeOffset:t=!0,includeZone:n=!1,includeOffsetSpace:s=!0}={}){let r="HH:mm:ss.SSS";return(n||t)&&(s&&(r+=" "),n?r+="z":t&&(r+="ZZ")),Va(this,r,!0)}toSQL(t={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(t)}`:null}toString(){return this.isValid?this.toISO():Io}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(t={}){if(!this.isValid)return{};const n={...this.c};return t.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(t,n="milliseconds",s={}){if(!this.isValid||!t.isValid)return ke.invalid("created by diffing an invalid DateTime");const r={locale:this.locale,numberingSystem:this.numberingSystem,...s},a=Iw(n).map(ke.normalizeUnit),i=t.valueOf()>this.valueOf(),o=i?this:t,l=i?t:this,u=FS(o,l,a,r);return i?u.negate():u}diffNow(t="milliseconds",n={}){return this.diff(re.now(),t,n)}until(t){return this.isValid?qe.fromDateTimes(this,t):this}hasSame(t,n){if(!this.isValid)return!1;const s=t.valueOf(),r=this.setZone(t.zone,{keepLocalTime:!0});return r.startOf(n)<=s&&s<=r.endOf(n)}equals(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)}toRelative(t={}){if(!this.isValid)return null;const n=t.base||re.fromObject({},{zone:this.zone}),s=t.padding?this<n?-t.padding:t.padding:0;let r=["years","months","days","hours","minutes","seconds"],a=t.unit;return Array.isArray(t.unit)&&(r=t.unit,a=void 0),Vd(n,this.plus(s),{...t,numeric:"always",units:r,unit:a})}toRelativeCalendar(t={}){return this.isValid?Vd(t.base||re.fromObject({},{zone:this.zone}),this,{...t,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...t){if(!t.every(re.isDateTime))throw new Ft("min requires all arguments be DateTimes");return gd(t,n=>n.valueOf(),Math.min)}static max(...t){if(!t.every(re.isDateTime))throw new Ft("max requires all arguments be DateTimes");return gd(t,n=>n.valueOf(),Math.max)}static fromFormatExplain(t,n,s={}){const{locale:r=null,numberingSystem:a=null}=s,i=He.fromOpts({locale:r,numberingSystem:a,defaultToEN:!0});return jh(i,t,n)}static fromStringExplain(t,n,s={}){return re.fromFormatExplain(t,n,s)}static get DATE_SHORT(){return di}static get DATE_MED(){return rh}static get DATE_MED_WITH_WEEKDAY(){return lw}static get DATE_FULL(){return ah}static get DATE_HUGE(){return ih}static get TIME_SIMPLE(){return oh}static get TIME_WITH_SECONDS(){return lh}static get TIME_WITH_SHORT_OFFSET(){return uh}static get TIME_WITH_LONG_OFFSET(){return ch}static get TIME_24_SIMPLE(){return dh}static get TIME_24_WITH_SECONDS(){return fh}static get TIME_24_WITH_SHORT_OFFSET(){return mh}static get TIME_24_WITH_LONG_OFFSET(){return hh}static get DATETIME_SHORT(){return vh}static get DATETIME_SHORT_WITH_SECONDS(){return gh}static get DATETIME_MED(){return yh}static get DATETIME_MED_WITH_SECONDS(){return ph}static get DATETIME_MED_WITH_WEEKDAY(){return uw}static get DATETIME_FULL(){return bh}static get DATETIME_FULL_WITH_SECONDS(){return wh}static get DATETIME_HUGE(){return Sh}static get DATETIME_HUGE_WITH_SECONDS(){return Ch}}function mr(e){if(re.isDateTime(e))return e;if(e&&e.valueOf&&bs(e.valueOf()))return re.fromJSDate(e);if(e&&typeof e=="object")return re.fromObject(e);throw new Ft(`Unknown datetime argument: ${e}, of type ${typeof e}`)}const Ds=e=>{e=rC(e);for(var t=e.slice(-4,-1),n=e.substr(e.length-1),s=(t.length==0?"":`${t}-`)+n,r=4;r<e.length;r+=3)s=e.slice(-3-r,-r)+"."+s;return s},rC=e=>typeof e=="string"?e.replace(/^0+|[^0-9kK]+/g,"").toUpperCase():"",vl={"001":1,AD:1,AE:6,AF:6,AG:0,AI:1,AL:1,AM:1,AN:1,AR:1,AS:0,AT:1,AU:1,AX:1,AZ:1,BA:1,BD:0,BE:1,BG:1,BH:6,BM:1,BN:1,BR:0,BS:0,BT:0,BW:0,BY:1,BZ:0,CA:0,CH:1,CL:1,CM:1,CN:1,CO:0,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DM:0,DO:0,DZ:6,EC:1,EE:1,EG:6,ES:1,ET:0,FI:1,FJ:1,FO:1,FR:1,GB:1,"GB-alt-variant":0,GE:1,GF:1,GP:1,GR:1,GT:0,GU:0,HK:0,HN:0,HR:1,HU:1,ID:0,IE:1,IL:0,IN:0,IQ:6,IR:6,IS:1,IT:1,JM:0,JO:6,JP:0,KE:0,KG:1,KH:0,KR:0,KW:6,KZ:1,LA:0,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MH:0,MK:1,MM:0,MN:1,MO:0,MQ:1,MT:0,MV:5,MX:0,MY:1,MZ:0,NI:0,NL:1,NO:1,NP:0,NZ:1,OM:6,PA:0,PE:0,PH:0,PK:0,PL:1,PR:0,PT:0,PY:0,QA:6,RE:1,RO:1,RS:1,RU:1,SA:0,SD:6,SE:1,SG:0,SI:1,SK:1,SM:1,SV:0,SY:6,TH:0,TJ:1,TM:1,TR:1,TT:0,TW:0,UA:1,UM:0,US:0,UY:1,UZ:1,VA:1,VE:0,VI:0,VN:1,WS:0,XK:1,YE:0,ZA:0,ZW:0};function aC(e,t){const n=[];let s=[];const r=nv(e),a=sv(e),i=r.getDay()-vl[t.slice(-2).toUpperCase()],o=a.getDay()-vl[t.slice(-2).toUpperCase()];for(let l=0;l<i;l++){const u=new Date(r);u.setDate(u.getDate()-(i-l)),s.push(u)}for(let l=1;l<=a.getDate();l++){const u=new Date(e.getFullYear(),e.getMonth(),l);s.push(u),s.length===7&&(n.push(s),s=[])}for(let l=1;l<7-o;l++){const u=new Date(a);u.setDate(u.getDate()+l),s.push(u)}return n.push(s),n}function nv(e){return new Date(e.getFullYear(),e.getMonth(),1)}function sv(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}function iC(e){const t=e.split("-").map(a=>a.padStart(2,"0")).join("-"),n=new Date().getTimezoneOffset()/-60,s=n<0?"-":"+",r=Math.abs(n).toString().padStart(2,"0");return`${t}T00:00:00.000${s}${r}:00`}const oC=/([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))/;function lC(e){if(e==null)return new Date;if(e instanceof Date)return e;if(typeof e=="string"){let t;if(oC.test(e)?t=Date.parse(iC(e)):t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const Md=new Date(2e3,0,2);function uC(e){const t=vl[e.slice(-2).toUpperCase()];return jl(7).map(n=>{const s=new Date(Md);return s.setDate(Md.getDate()+t+n),new Intl.DateTimeFormat(e,{weekday:"short"}).format(s)})}function cC(e,t,n){const s=new Date(e);let r={};switch(t){case"fullDateWithWeekday":r={weekday:"long",day:"numeric",month:"long",year:"numeric"};break;case"normalDateWithWeekday":r={weekday:"short",day:"numeric",month:"short"};break;case"keyboardDate":r={};break;case"monthAndDate":r={month:"long",day:"numeric"};break;case"monthAndYear":r={month:"long",year:"numeric"};break;case"dayOfMonth":r={day:"numeric"};break;default:r={timeZone:"UTC",timeZoneName:"short"}}return new Intl.DateTimeFormat(n,r).format(s)}function dC(e,t){const n=new Date(e);return n.setDate(n.getDate()+t),n}function fC(e,t){const n=new Date(e);return n.setMonth(n.getMonth()+t),n}function mC(e){return e.getFullYear()}function hC(e){return e.getMonth()}function vC(e){return new Date(e.getFullYear(),0,1)}function gC(e){return new Date(e.getFullYear(),11,31)}function yC(e,t){return gl(e,t[0])&&bC(e,t[1])}function pC(e){const t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}function gl(e,t){return e.getTime()>t.getTime()}function bC(e,t){return e.getTime()<t.getTime()}function Dd(e,t){return e.getTime()===t.getTime()}function wC(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function SC(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function CC(e,t,n){const s=new Date(e),r=new Date(t);return n==="month"?s.getMonth()-r.getMonth()+(s.getFullYear()-r.getFullYear())*12:Math.floor((s.getTime()-r.getTime())/(1e3*60*60*24))}function xC(e,t){const n=new Date(e);return n.setFullYear(t),n}class _C{constructor(t){this.locale=t.locale}date(t){return lC(t)}toJsDate(t){return t}addDays(t,n){return dC(t,n)}addMonths(t,n){return fC(t,n)}getWeekArray(t){return aC(t,this.locale)}startOfMonth(t){return nv(t)}endOfMonth(t){return sv(t)}format(t,n){return cC(t,n,this.locale)}isEqual(t,n){return Dd(t,n)}isValid(t){return pC(t)}isWithinRange(t,n){return yC(t,n)}isAfter(t,n){return gl(t,n)}isBefore(t,n){return!gl(t,n)&&!Dd(t,n)}isSameDay(t,n){return wC(t,n)}isSameMonth(t,n){return SC(t,n)}setYear(t,n){return xC(t,n)}getDiff(t,n,s){return CC(t,n,s)}getWeekdays(){return uC(this.locale)}getYear(t){return mC(t)}getMonth(t){return hC(t)}startOfYear(t){return vC(t)}endOfYear(t){return gC(t)}}const Nd=Symbol.for("vuetify:date-adapter");function EC(e){return Lt({adapter:_C,locale:{af:"af-ZA",bg:"bg-BG",ca:"ca-ES",ckb:"",cs:"",de:"de-DE",el:"el-GR",en:"en-US",et:"et-EE",fa:"fa-IR",fi:"fi-FI",hr:"hr-HR",hu:"hu-HU",he:"he-IL",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lv:"lv-LV",lt:"lt-LT",nl:"nl-NL",no:"nn-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",srCyrl:"sr-SP",srLatn:"sr-SP",sv:"sv-SE",th:"th-TH",tr:"tr-TR",az:"az-AZ",uk:"uk-UA",vi:"vi-VN",zhHans:"zh-CN",zhHant:"zh-TW"}},e)}const yl=Symbol.for("vuetify:layout"),rv=Symbol.for("vuetify:layout-item"),Fd=1e3,TC=K({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},"layout"),kC=K({name:{type:String},order:{type:[Number,String],default:0},absolute:Boolean},"layout-item");function OC(e){const t=je(yl);if(!t)throw new Error("[Vuetify] Could not find injected layout");const n=e.id??`layout-item-${It()}`,s=dt("useLayoutItem");_t(rv,{id:n});const r=Ce(!1);kf(()=>r.value=!0),Tf(()=>r.value=!1);const{layoutItemStyles:a,layoutItemScrimStyles:i}=t.register(s,{...e,active:C(()=>r.value?!1:e.active.value),id:n});return ln(()=>t.unregister(n)),{layoutItemStyles:a,layoutRect:t.layoutRect,layoutItemScrimStyles:i}}const IC=(e,t,n,s)=>{let r={top:0,left:0,right:0,bottom:0};const a=[{id:"",layer:{...r}}];for(const i of e){const o=t.get(i),l=n.get(i),u=s.get(i);if(!o||!l||!u)continue;const c={...r,[o.value]:parseInt(r[o.value],10)+(u.value?parseInt(l.value,10):0)};a.push({id:i,layer:c}),r=c}return a};function AC(e){const t=je(yl,null),n=C(()=>t?t.rootZIndex.value-100:Fd),s=U([]),r=ct(new Map),a=ct(new Map),i=ct(new Map),o=ct(new Map),l=ct(new Map),{resizeRef:u,contentRect:c}=na(),d=C(()=>{const O=new Map,T=e.overlaps??[];for(const y of T.filter(S=>S.includes(":"))){const[S,A]=y.split(":");if(!s.value.includes(S)||!s.value.includes(A))continue;const z=r.get(S),M=r.get(A),B=a.get(S),N=a.get(A);!z||!M||!B||!N||(O.set(A,{position:z.value,amount:parseInt(B.value,10)}),O.set(S,{position:M.value,amount:-parseInt(N.value,10)}))}return O}),f=C(()=>{const O=[...new Set([...i.values()].map(y=>y.value))].sort((y,S)=>y-S),T=[];for(const y of O){const S=s.value.filter(A=>{var z;return((z=i.get(A))==null?void 0:z.value)===y});T.push(...S)}return IC(T,r,a,o)}),h=C(()=>!Array.from(l.values()).some(O=>O.value)),v=C(()=>f.value[f.value.length-1].layer),g=C(()=>({"--v-layout-left":de(v.value.left),"--v-layout-right":de(v.value.right),"--v-layout-top":de(v.value.top),"--v-layout-bottom":de(v.value.bottom),...h.value?void 0:{transition:"none"}})),b=C(()=>f.value.slice(1).map((O,T)=>{let{id:y}=O;const{layer:S}=f.value[T],A=a.get(y),z=r.get(y);return{id:y,...S,size:Number(A.value),position:z.value}})),_=O=>b.value.find(T=>T.id===O),E=dt("createLayout"),w=Ce(!1);Tn(()=>{w.value=!0}),_t(yl,{register:(O,T)=>{let{id:y,order:S,position:A,layoutSize:z,elementSize:M,active:B,disableTransitions:N,absolute:te}=T;i.set(y,S),r.set(y,A),a.set(y,z),o.set(y,B),N&&l.set(y,N);const se=xr(rv,E==null?void 0:E.vnode).indexOf(O);se>-1?s.value.splice(se,0,y):s.value.push(y);const Y=C(()=>b.value.findIndex(Ee=>Ee.id===y)),J=C(()=>n.value+f.value.length*2-Y.value*2),me=C(()=>{const Ee=A.value==="left"||A.value==="right",Re=A.value==="right",ze=A.value==="bottom",Mt={[A.value]:0,zIndex:J.value,transform:`translate${Ee?"X":"Y"}(${(B.value?0:-110)*(Re||ze?-1:1)}%)`,position:te.value||n.value!==Fd?"absolute":"fixed",...h.value?void 0:{transition:"none"}};if(!w.value)return Mt;const W=b.value[Y.value];if(!W)throw new Error(`[Vuetify] Could not find layout item "${y}"`);const Z=d.value.get(y);return Z&&(W[Z.position]+=Z.amount),{...Mt,height:Ee?`calc(100% - ${W.top}px - ${W.bottom}px)`:M.value?`${M.value}px`:void 0,left:Re?void 0:`${W.left}px`,right:Re?`${W.right}px`:void 0,top:A.value!=="bottom"?`${W.top}px`:void 0,bottom:A.value!=="top"?`${W.bottom}px`:void 0,width:Ee?M.value?`${M.value}px`:void 0:`calc(100% - ${W.left}px - ${W.right}px)`}}),fe=C(()=>({zIndex:J.value-1}));return{layoutItemStyles:me,layoutItemScrimStyles:fe,zIndex:J}},unregister:O=>{i.delete(O),r.delete(O),a.delete(O),o.delete(O),l.delete(O),s.value=s.value.filter(T=>T!==O)},mainRect:v,mainStyles:g,getLayoutItem:_,items:b,layoutRect:c,rootZIndex:n});const k=C(()=>["v-layout",{"v-layout--full-height":e.fullHeight}]),P=C(()=>({zIndex:n.value,position:t?"relative":void 0,overflow:t?"hidden":void 0}));return{layoutClasses:k,layoutStyles:P,getLayoutItem:_,items:b,layoutRect:c,layoutRef:u}}function av(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{blueprint:t,...n}=e,s=Lt(t,n),{aliases:r={},components:a={},directives:i={}}=s,o=ob(s.defaults),l=C0(s.display,s.ssr),u=Eb(s.theme),c=Db(s.icons),d=jb(s.locale),f=EC(s.date);return{install:v=>{for(const g in i)v.directive(g,i[g]);for(const g in a)v.component(g,a[g]);for(const g in r)v.component(g,Xs({...r[g],name:g,aliasName:r[g].name}));if(u.install(v),v.provide(Hr,o),v.provide(rl,l),v.provide(ii,u),v.provide(Xo,c),v.provide(oi,d),v.provide(Nd,f),st&&s.ssr)if(v.$nuxt)v.$nuxt.hook("app:suspense:resolve",()=>{l.update()});else{const{mount:g}=v;v.mount=function(){const b=g(...arguments);return bt(()=>l.update()),v.mount=g,b}}It.reset(),v.mixin({computed:{$vuetify(){return ct({defaults:Ms.call(this,Hr),display:Ms.call(this,rl),theme:Ms.call(this,ii),icons:Ms.call(this,Xo),locale:Ms.call(this,oi),date:Ms.call(this,Nd)})}}})},defaults:o,display:l,theme:u,icons:c,locale:d,date:f}}const VC="3.3.9";av.version=VC;function Ms(e){var s,r;const t=this.$,n=((s=t.parent)==null?void 0:s.provides)??((r=t.vnode.appContext)==null?void 0:r.provides);if(n&&e in n)return n[e]}const PC=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n};const vi=oe()({name:"VCardActions",props:Se(),setup(e,t){let{slots:n}=t;return Yn({VBtn:{variant:"text"}}),pe(()=>{var s;return m("div",{class:["v-card-actions",e.class],style:e.style},[(s=n.default)==null?void 0:s.call(n)])}),{}}}),MC=Cs("v-card-subtitle"),gi=Cs("v-card-title");function DC(e){return{aspectStyles:C(()=>{const t=Number(e.aspectRatio);return t?{paddingBottom:String(1/t*100)+"%"}:void 0})}}const iv=K({aspectRatio:[String,Number],contentClass:String,inline:Boolean,...Se(),...Qn()},"VResponsive"),Wr=oe()({name:"VResponsive",props:iv(),setup(e,t){let{slots:n}=t;const{aspectStyles:s}=DC(e),{dimensionStyles:r}=es(e);return pe(()=>{var a;return m("div",{class:["v-responsive",{"v-responsive--inline":e.inline},e.class],style:[r.value,e.style]},[m("div",{class:"v-responsive__sizer",style:s.value},null),(a=n.additional)==null?void 0:a.call(n),n.default&&m("div",{class:["v-responsive__content",e.contentClass]},[n.default()])])}),{}}});function NC(e,t){if(!Xl)return;const n=t.modifiers||{},s=t.value,{handler:r,options:a}=typeof s=="object"?s:{handler:s,options:{}},i=new IntersectionObserver(function(){var d;let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],l=arguments.length>1?arguments[1]:void 0;const u=(d=e._observe)==null?void 0:d[t.instance.$.uid];if(!u)return;const c=o.some(f=>f.isIntersecting);r&&(!n.quiet||u.init)&&(!n.once||c||u.init)&&r(c,o,l),c&&n.once?ov(e,t):u.init=!0},a);e._observe=Object(e._observe),e._observe[t.instance.$.uid]={init:!1,observer:i},i.observe(e)}function ov(e,t){var s;const n=(s=e._observe)==null?void 0:s[t.instance.$.uid];n&&(n.observer.unobserve(e),delete e._observe[t.instance.$.uid])}const FC={mounted:NC,unmounted:ov},lv=FC,LC=K({alt:String,cover:Boolean,eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},srcset:String,...iv(),...Se(),...oa()},"VImg"),no=oe()({name:"VImg",directives:{intersect:lv},props:LC(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,t){let{emit:n,slots:s}=t;const r=Ce(""),a=U(),i=Ce(e.eager?"loading":"idle"),o=Ce(),l=Ce(),u=C(()=>e.src&&typeof e.src=="object"?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),c=C(()=>u.value.aspect||o.value/l.value||0);we(()=>e.src,()=>{d(i.value!=="idle")}),we(c,(y,S)=>{!y&&S&&a.value&&b(a.value)}),Ll(()=>d());function d(y){if(!(e.eager&&y)&&!(Xl&&!y&&!e.eager)){if(i.value="loading",u.value.lazySrc){const S=new Image;S.src=u.value.lazySrc,b(S,null)}u.value.src&&bt(()=>{var S,A;if(n("loadstart",((S=a.value)==null?void 0:S.currentSrc)||u.value.src),(A=a.value)!=null&&A.complete){if(a.value.naturalWidth||h(),i.value==="error")return;c.value||b(a.value,null),f()}else c.value||b(a.value),v()})}}function f(){var y;v(),i.value="loaded",n("load",((y=a.value)==null?void 0:y.currentSrc)||u.value.src)}function h(){var y;i.value="error",n("error",((y=a.value)==null?void 0:y.currentSrc)||u.value.src)}function v(){const y=a.value;y&&(r.value=y.currentSrc||y.src)}let g=-1;function b(y){let S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;const A=()=>{clearTimeout(g);const{naturalHeight:z,naturalWidth:M}=y;z||M?(o.value=M,l.value=z):!y.complete&&i.value==="loading"&&S!=null?g=window.setTimeout(A,S):(y.currentSrc.endsWith(".svg")||y.currentSrc.startsWith("data:image/svg+xml"))&&(o.value=1,l.value=1)};A()}const _=C(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),E=()=>{var A;if(!u.value.src||i.value==="idle")return null;const y=m("img",{class:["v-img__img",_.value],src:u.value.src,srcset:u.value.srcset,alt:e.alt,sizes:e.sizes,ref:a,onLoad:f,onError:h},null),S=(A=s.sources)==null?void 0:A.call(s);return m(Hn,{transition:e.transition,appear:!0},{default:()=>[Xe(S?m("picture",{class:"v-img__picture"},[S,y]):y,[[Ys,i.value==="loaded"]])]})},w=()=>m(Hn,{transition:e.transition},{default:()=>[u.value.lazySrc&&i.value!=="loaded"&&m("img",{class:["v-img__img","v-img__img--preload",_.value],src:u.value.lazySrc,alt:e.alt},null)]}),k=()=>s.placeholder?m(Hn,{transition:e.transition,appear:!0},{default:()=>[(i.value==="loading"||i.value==="error"&&!s.error)&&m("div",{class:"v-img__placeholder"},[s.placeholder()])]}):null,P=()=>s.error?m(Hn,{transition:e.transition,appear:!0},{default:()=>[i.value==="error"&&m("div",{class:"v-img__error"},[s.error()])]}):null,O=()=>e.gradient?m("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,T=Ce(!1);{const y=we(c,S=>{S&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{T.value=!0})}),y())})}return pe(()=>{const[y]=Wr.filterProps(e);return Xe(m(Wr,ge({class:["v-img",{"v-img--booting":!T.value},e.class],style:[{width:de(e.width==="auto"?o.value:e.width)},e.style]},y,{aspectRatio:c.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>m(_e,null,[m(E,null,null),m(w,null,null),m(O,null,null),m(k,null,null),m(P,null,null)]),default:s.default}),[[bn("intersect"),{handler:d,options:e.options},null,{once:!0}]])}),{currentSrc:r,image:a,state:i,naturalWidth:o,naturalHeight:l}}}),RC=K({start:Boolean,end:Boolean,icon:Fe,image:String,...Se(),...Ut(),...At(),...ea(),...Ye(),...We(),...un({variant:"flat"})},"VAvatar"),Zs=oe()({name:"VAvatar",props:RC(),setup(e,t){let{slots:n}=t;const{themeClasses:s}=et(e),{colorClasses:r,colorStyles:a,variantClasses:i}=_s(e),{densityClasses:o}=Xt(e),{roundedClasses:l}=Vt(e),{sizeClasses:u,sizeStyles:c}=ta(e);return pe(()=>m(e.tag,{class:["v-avatar",{"v-avatar--start":e.start,"v-avatar--end":e.end},s.value,r.value,o.value,l.value,u.value,i.value,e.class],style:[a.value,c.value,e.style]},{default:()=>{var d;return[e.image?m(no,{key:"image",src:e.image,alt:"",cover:!0},null):e.icon?m(it,{key:"icon",icon:e.icon},null):(d=n.default)==null?void 0:d.call(n),xs(!1,"v-avatar")]}})),{}}}),$C=K({appendAvatar:String,appendIcon:Fe,prependAvatar:String,prependIcon:Fe,subtitle:String,title:String,...Se(),...Ut()},"VCardItem"),BC=oe()({name:"VCardItem",props:$C(),setup(e,t){let{slots:n}=t;return pe(()=>{var u;const s=!!(e.prependAvatar||e.prependIcon),r=!!(s||n.prepend),a=!!(e.appendAvatar||e.appendIcon),i=!!(a||n.append),o=!!(e.title||n.title),l=!!(e.subtitle||n.subtitle);return m("div",{class:["v-card-item",e.class],style:e.style},[r&&m("div",{key:"prepend",class:"v-card-item__prepend"},[n.prepend?m(Ze,{key:"prepend-defaults",disabled:!s,defaults:{VAvatar:{density:e.density,icon:e.prependIcon,image:e.prependAvatar}}},n.prepend):s&&m(Zs,{key:"prepend-avatar",density:e.density,icon:e.prependIcon,image:e.prependAvatar},null)]),m("div",{class:"v-card-item__content"},[o&&m(gi,{key:"title"},{default:()=>{var c;return[((c=n.title)==null?void 0:c.call(n))??e.title]}}),l&&m(MC,{key:"subtitle"},{default:()=>{var c;return[((c=n.subtitle)==null?void 0:c.call(n))??e.subtitle]}}),(u=n.default)==null?void 0:u.call(n)]),i&&m("div",{key:"append",class:"v-card-item__append"},[n.append?m(Ze,{key:"append-defaults",disabled:!a,defaults:{VAvatar:{density:e.density,icon:e.appendIcon,image:e.appendAvatar}}},n.append):a&&m(Zs,{key:"append-avatar",density:e.density,icon:e.appendIcon,image:e.appendAvatar},null)])])}),{}}}),yi=Cs("v-card-text"),HC=K({appendAvatar:String,appendIcon:Fe,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:Fe,ripple:{type:[Boolean,Object],default:!0},subtitle:String,text:String,title:String,...Jn(),...Se(),...Ut(),...Qn(),...On(),...ru(),...sa(),...$i(),...At(),...Ui(),...Ye(),...We(),...un({variant:"elevated"})},"VCard"),Un=oe()({name:"VCard",directives:{Ripple:aa},props:HC(),setup(e,t){let{attrs:n,slots:s}=t;const{themeClasses:r}=et(e),{borderClasses:a}=Xn(e),{colorClasses:i,colorStyles:o,variantClasses:l}=_s(e),{densityClasses:u}=Xt(e),{dimensionStyles:c}=es(e),{elevationClasses:d}=In(e),{loaderClasses:f}=au(e),{locationStyles:h}=ra(e),{positionClasses:v}=Bi(e),{roundedClasses:g}=Vt(e),b=Hi(e,n),_=C(()=>e.link!==!1&&b.isLink.value),E=C(()=>!e.disabled&&e.link!==!1&&(e.link||b.isClickable.value));return pe(()=>{const w=_.value?"a":e.tag,k=!!(s.title||e.title),P=!!(s.subtitle||e.subtitle),O=k||P,T=!!(s.append||e.appendAvatar||e.appendIcon),y=!!(s.prepend||e.prependAvatar||e.prependIcon),S=!!(s.image||e.image),A=O||y||T,z=!!(s.text||e.text);return Xe(m(w,{class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":E.value},r.value,a.value,i.value,u.value,d.value,f.value,v.value,g.value,l.value,e.class],style:[o.value,c.value,h.value,e.style],href:b.href.value,onClick:E.value&&b.navigate,tabindex:e.disabled?-1:void 0},{default:()=>{var M;return[S&&m("div",{key:"image",class:"v-card__image"},[s.image?m(Ze,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},s.image):m(no,{key:"image-img",cover:!0,src:e.image},null)]),m(_m,{name:"v-card",active:!!e.loading,color:typeof e.loading=="boolean"?void 0:e.loading},{default:s.loader}),A&&m(BC,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:s.item,prepend:s.prepend,title:s.title,subtitle:s.subtitle,append:s.append}),z&&m(yi,{key:"text"},{default:()=>{var B;return[((B=s.text)==null?void 0:B.call(s))??e.text]}}),(M=s.default)==null?void 0:M.call(s),s.actions&&m(vi,null,{default:s.actions}),xs(E.value,"v-card")]}}),[[bn("ripple"),E.value&&e.ripple]])}),{}}});const UC=K({text:String,clickable:Boolean,...Se(),...We()},"VLabel"),uv=oe()({name:"VLabel",props:UC(),setup(e,t){let{slots:n}=t;return pe(()=>{var s;return m("label",{class:["v-label",{"v-label--clickable":e.clickable},e.class],style:e.style},[e.text,(s=n.default)==null?void 0:s.call(n)])}),{}}});const cv=Symbol.for("vuetify:selection-control-group"),dv=K({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:Fe,trueIcon:Fe,ripple:{type:Boolean,default:!0},multiple:{type:Boolean,default:null},name:String,readonly:Boolean,modelValue:null,type:String,valueComparator:{type:Function,default:Js},...Se(),...Ut(),...We()},"SelectionControlGroup"),zC=K({...dv({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup");oe()({name:"VSelectionControlGroup",props:zC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"modelValue"),r=It(),a=C(()=>e.id||`v-selection-control-group-${r}`),i=C(()=>e.name||a.value),o=new Set;return _t(cv,{modelValue:s,forceUpdate:()=>{o.forEach(l=>l())},onForceUpdate:l=>{o.add(l),pt(()=>{o.delete(l)})}}),Yn({[e.defaultsTarget]:{color:he(e,"color"),disabled:he(e,"disabled"),density:he(e,"density"),error:he(e,"error"),inline:he(e,"inline"),modelValue:s,multiple:C(()=>!!e.multiple||e.multiple==null&&Array.isArray(s.value)),name:i,falseIcon:he(e,"falseIcon"),trueIcon:he(e,"trueIcon"),readonly:he(e,"readonly"),ripple:he(e,"ripple"),type:he(e,"type"),valueComparator:he(e,"valueComparator")}}),pe(()=>{var l;return m("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[(l=n.default)==null?void 0:l.call(n)])}),{}}});const fv=K({label:String,trueValue:null,falseValue:null,value:null,...Se(),...dv()},"VSelectionControl");function jC(e){const t=je(cv,void 0),{densityClasses:n}=Xt(e),s=Ke(e,"modelValue"),r=C(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),a=C(()=>e.falseValue!==void 0?e.falseValue:!1),i=C(()=>!!e.multiple||e.multiple==null&&Array.isArray(s.value)),o=C({get(){const d=t?t.modelValue.value:s.value;return i.value?d.some(f=>e.valueComparator(f,r.value)):e.valueComparator(d,r.value)},set(d){if(e.readonly)return;const f=d?r.value:a.value;let h=f;i.value&&(h=d?[...Wn(s.value),f]:Wn(s.value).filter(v=>!e.valueComparator(v,r.value))),t?t.modelValue.value=h:s.value=h}}),{textColorClasses:l,textColorStyles:u}=on(C(()=>o.value&&!e.error&&!e.disabled?e.color:void 0)),c=C(()=>o.value?e.trueIcon:e.falseIcon);return{group:t,densityClasses:n,trueValue:r,falseValue:a,model:o,textColorClasses:l,textColorStyles:u,icon:c}}const WC=oe()({name:"VSelectionControl",directives:{Ripple:aa},inheritAttrs:!1,props:fv(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:s}=t;const{group:r,densityClasses:a,icon:i,model:o,textColorClasses:l,textColorStyles:u,trueValue:c}=jC(e),d=It(),f=C(()=>e.id||`input-${d}`),h=Ce(!1),v=Ce(!1),g=U();r==null||r.onForceUpdate(()=>{g.value&&(g.value.checked=o.value)});function b(w){h.value=!0,(!Jo||Jo&&w.target.matches(":focus-visible"))&&(v.value=!0)}function _(){h.value=!1,v.value=!1}function E(w){e.readonly&&r&&bt(()=>r.forceUpdate()),o.value=w.target.checked}return pe(()=>{var O,T;const w=s.label?s.label({label:e.label,props:{for:f.value}}):e.label,[k,P]=Wl(n);return m("div",ge({class:["v-selection-control",{"v-selection-control--dirty":o.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":h.value,"v-selection-control--focus-visible":v.value,"v-selection-control--inline":e.inline},a.value,e.class]},k,{style:e.style}),[m("div",{class:["v-selection-control__wrapper",l.value],style:u.value},[(O=s.default)==null?void 0:O.call(s),Xe(m("div",{class:["v-selection-control__input"]},[i.value&&m(it,{key:"icon",icon:i.value},null),m("input",ge({ref:g,checked:o.value,disabled:!!(e.readonly||e.disabled),id:f.value,onBlur:_,onFocus:b,onInput:E,"aria-disabled":!!(e.readonly||e.disabled),type:e.type,value:c.value,name:e.name,"aria-checked":e.type==="checkbox"?o.value:void 0},P),null),(T=s.input)==null?void 0:T.call(s,{model:o,textColorClasses:l,textColorStyles:u,props:{onFocus:b,onBlur:_,id:f.value}})]),[[bn("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),w&&m(uv,{for:f.value,clickable:!0},{default:()=>[w]})])}),{isFocused:h,input:g}}}),mv=K({indeterminate:Boolean,indeterminateIcon:{type:Fe,default:"$checkboxIndeterminate"},...fv({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),pl=oe()({name:"VCheckboxBtn",props:mv(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"indeterminate"),r=Ke(e,"modelValue");function a(l){s.value&&(s.value=!1)}const i=C(()=>s.value?e.indeterminateIcon:e.falseIcon),o=C(()=>s.value?e.indeterminateIcon:e.trueIcon);return pe(()=>m(WC,ge(e,{modelValue:r.value,"onUpdate:modelValue":[l=>r.value=l,a],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:i.value,trueIcon:o.value,"aria-checked":s.value?"mixed":void 0}),n)),{}}});function hv(e){const{t}=Ri();function n(s){let{name:r}=s;const a={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[r],i=e[`onClick:${r}`],o=i&&a?t(`$vuetify.input.${a}`,e.label??""):void 0;return m(it,{icon:e[`${r}Icon`],"aria-label":o,onClick:i},null)}return{InputIcon:n}}const qC=K({disabled:Boolean,group:Boolean,hideOnLeave:Boolean,leaveAbsolute:Boolean,mode:String,origin:String},"transition");function Pt(e,t,n){return oe()({name:e,props:qC({mode:n,origin:t}),setup(s,r){let{slots:a}=r;const i={onBeforeEnter(o){s.origin&&(o.style.transformOrigin=s.origin)},onLeave(o){if(s.leaveAbsolute){const{offsetTop:l,offsetLeft:u,offsetWidth:c,offsetHeight:d}=o;o._transitionInitialStyles={position:o.style.position,top:o.style.top,left:o.style.left,width:o.style.width,height:o.style.height},o.style.position="absolute",o.style.top=`${l}px`,o.style.left=`${u}px`,o.style.width=`${c}px`,o.style.height=`${d}px`}s.hideOnLeave&&o.style.setProperty("display","none","important")},onAfterLeave(o){if(s.leaveAbsolute&&(o!=null&&o._transitionInitialStyles)){const{position:l,top:u,left:c,width:d,height:f}=o._transitionInitialStyles;delete o._transitionInitialStyles,o.style.position=l||"",o.style.top=u||"",o.style.left=c||"",o.style.width=d||"",o.style.height=f||""}}};return()=>{const o=s.group?wp:Sn;return Kn(o,{name:s.disabled?"":e,css:!s.disabled,...s.group?void 0:{mode:s.mode},...s.disabled?{}:i},a.default)}}})}function vv(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"in-out";return oe()({name:e,props:{mode:{type:String,default:n},disabled:Boolean},setup(s,r){let{slots:a}=r;return()=>Kn(Sn,{name:s.disabled?"":e,css:!s.disabled,...s.disabled?{}:t},a.default)}})}function gv(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";const n=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1)?"width":"height",s=Ht(`offset-${n}`);return{onBeforeEnter(i){i._parent=i.parentNode,i._initialStyle={transition:i.style.transition,overflow:i.style.overflow,[n]:i.style[n]}},onEnter(i){const o=i._initialStyle;i.style.setProperty("transition","none","important"),i.style.overflow="hidden";const l=`${i[s]}px`;i.style[n]="0",i.offsetHeight,i.style.transition=o.transition,e&&i._parent&&i._parent.classList.add(e),requestAnimationFrame(()=>{i.style[n]=l})},onAfterEnter:a,onEnterCancelled:a,onLeave(i){i._initialStyle={transition:"",overflow:i.style.overflow,[n]:i.style[n]},i.style.overflow="hidden",i.style[n]=`${i[s]}px`,i.offsetHeight,requestAnimationFrame(()=>i.style[n]="0")},onAfterLeave:r,onLeaveCancelled:r};function r(i){e&&i._parent&&i._parent.classList.remove(e),a(i)}function a(i){const o=i._initialStyle[n];i.style.overflow=i._initialStyle.overflow,o!=null&&(i.style[n]=o),delete i._initialStyle}}const ZC=K({target:Object},"v-dialog-transition"),wu=oe()({name:"VDialogTransition",props:ZC(),setup(e,t){let{slots:n}=t;const s={onBeforeEnter(r){r.style.pointerEvents="none",r.style.visibility="hidden"},async onEnter(r,a){var f;await new Promise(h=>requestAnimationFrame(h)),await new Promise(h=>requestAnimationFrame(h)),r.style.visibility="";const{x:i,y:o,sx:l,sy:u,speed:c}=Rd(e.target,r),d=Fs(r,[{transform:`translate(${i}px, ${o}px) scale(${l}, ${u})`,opacity:0},{}],{duration:225*c,easing:cb});(f=Ld(r))==null||f.forEach(h=>{Fs(h,[{opacity:0},{opacity:0,offset:.33},{}],{duration:225*2*c,easing:ri})}),d.finished.then(()=>a())},onAfterEnter(r){r.style.removeProperty("pointer-events")},onBeforeLeave(r){r.style.pointerEvents="none"},async onLeave(r,a){var f;await new Promise(h=>requestAnimationFrame(h));const{x:i,y:o,sx:l,sy:u,speed:c}=Rd(e.target,r);Fs(r,[{},{transform:`translate(${i}px, ${o}px) scale(${l}, ${u})`,opacity:0}],{duration:125*c,easing:db}).finished.then(()=>a()),(f=Ld(r))==null||f.forEach(h=>{Fs(h,[{},{opacity:0,offset:.2},{opacity:0}],{duration:125*2*c,easing:ri})})},onAfterLeave(r){r.style.removeProperty("pointer-events")}};return()=>e.target?m(Sn,ge({name:"dialog-transition"},s,{css:!1}),n):m(Sn,{name:"dialog-transition"},n)}});function Ld(e){var n;const t=(n=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list"))==null?void 0:n.children;return t&&[...t]}function Rd(e,t){const n=e.getBoundingClientRect(),s=Gl(t),[r,a]=getComputedStyle(t).transformOrigin.split(" ").map(_=>parseFloat(_)),[i,o]=getComputedStyle(t).getPropertyValue("--v-overlay-anchor-origin").split(" ");let l=n.left+n.width/2;i==="left"||o==="left"?l-=n.width/2:(i==="right"||o==="right")&&(l+=n.width/2);let u=n.top+n.height/2;i==="top"||o==="top"?u-=n.height/2:(i==="bottom"||o==="bottom")&&(u+=n.height/2);const c=n.width/s.width,d=n.height/s.height,f=Math.max(1,c,d),h=c/f||0,v=d/f||0,g=s.width*s.height/(window.innerWidth*window.innerHeight),b=g>.12?Math.min(1.5,(g-.12)*10+1):1;return{x:l-(r+s.left),y:u-(a+s.top),sx:h,sy:v,speed:b}}Pt("fab-transition","center center","out-in");Pt("dialog-bottom-transition");Pt("dialog-top-transition");Pt("fade-transition");Pt("scale-transition");Pt("scroll-x-transition");Pt("scroll-x-reverse-transition");Pt("scroll-y-transition");Pt("scroll-y-reverse-transition");Pt("slide-x-transition");Pt("slide-x-reverse-transition");const yv=Pt("slide-y-transition");Pt("slide-y-reverse-transition");const pv=vv("expand-transition",gv()),bv=vv("expand-x-transition",gv("",!0)),GC=K({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},...Se(),...oa({transition:{component:yv,leaveAbsolute:!0,group:!0}})},"VMessages"),KC=oe()({name:"VMessages",props:GC(),setup(e,t){let{slots:n}=t;const s=C(()=>Wn(e.messages)),{textColorClasses:r,textColorStyles:a}=on(C(()=>e.color));return pe(()=>m(Hn,{transition:e.transition,tag:"div",class:["v-messages",r.value,e.class],style:[a.value,e.style],role:"alert","aria-live":"polite"},{default:()=>[e.active&&s.value.map((i,o)=>m("div",{class:"v-messages__message",key:`${o}-${s.value}`},[n.message?n.message({message:i}):i]))]})),{}}}),wv=K({focused:Boolean,"onUpdate:focused":an()},"focus");function Su(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn();const n=Ke(e,"focused"),s=C(()=>({[`${t}--focused`]:n.value}));function r(){n.value=!0}function a(){n.value=!1}return{focusClasses:s,isFocused:n,focus:r,blur:a}}const YC=Symbol.for("vuetify:form");function Sv(){return je(YC,null)}const JC=K({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...wv()},"validation");function XC(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn(),n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:It();const s=Ke(e,"modelValue"),r=C(()=>e.validationValue===void 0?s.value:e.validationValue),a=Sv(),i=U([]),o=Ce(!0),l=C(()=>!!(Wn(s.value===""?null:s.value).length||Wn(r.value===""?null:r.value).length)),u=C(()=>!!(e.disabled??(a==null?void 0:a.isDisabled.value))),c=C(()=>!!(e.readonly??(a==null?void 0:a.isReadonly.value))),d=C(()=>e.errorMessages.length?Wn(e.errorMessages).slice(0,Math.max(0,+e.maxErrors)):i.value),f=C(()=>{let k=(e.validateOn??(a==null?void 0:a.validateOn.value))||"input";k==="lazy"&&(k="input lazy");const P=new Set((k==null?void 0:k.split(" "))??[]);return{blur:P.has("blur")||P.has("input"),input:P.has("input"),submit:P.has("submit"),lazy:P.has("lazy")}}),h=C(()=>e.error||e.errorMessages.length?!1:e.rules.length?o.value?i.value.length||f.value.lazy?null:!0:!i.value.length:!0),v=Ce(!1),g=C(()=>({[`${t}--error`]:h.value===!1,[`${t}--dirty`]:l.value,[`${t}--disabled`]:u.value,[`${t}--readonly`]:c.value})),b=C(()=>e.name??rt(n));Ll(()=>{a==null||a.register({id:b.value,validate:w,reset:_,resetValidation:E})}),ln(()=>{a==null||a.unregister(b.value)}),Tn(async()=>{f.value.lazy||await w(!0),a==null||a.update(b.value,h.value,d.value)}),Zn(()=>f.value.input,()=>{we(r,()=>{if(r.value!=null)w();else if(e.focused){const k=we(()=>e.focused,P=>{P||w(),k()})}})}),Zn(()=>f.value.blur,()=>{we(()=>e.focused,k=>{k||w()})}),we(h,()=>{a==null||a.update(b.value,h.value,d.value)});function _(){s.value=null,bt(E)}function E(){o.value=!0,f.value.lazy?i.value=[]:w(!0)}async function w(){let k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const P=[];v.value=!0;for(const O of e.rules){if(P.length>=+(e.maxErrors??1))break;const y=await(typeof O=="function"?O:()=>O)(r.value);if(y!==!0){if(y!==!1&&typeof y!="string"){console.warn(`${y} is not a valid value. Rule functions must return boolean true or a string.`);continue}P.push(y||"")}}return i.value=P,v.value=!1,o.value=k,i.value}return{errorMessages:d,isDirty:l,isDisabled:u,isReadonly:c,isPristine:o,isValid:h,isValidating:v,reset:_,resetValidation:E,validate:w,validationClasses:g}}const Cu=K({id:String,appendIcon:Fe,centerAffix:{type:Boolean,default:!0},prependIcon:Fe,hideDetails:[Boolean,String],hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":an(),"onClick:append":an(),...Se(),...Ut(),...JC()},"VInput"),pi=oe()({name:"VInput",props:{...Cu()},emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:s,emit:r}=t;const{densityClasses:a}=Xt(e),{rtlClasses:i}=ts(),{InputIcon:o}=hv(e),l=It(),u=C(()=>e.id||`input-${l}`),c=C(()=>`${u.value}-messages`),{errorMessages:d,isDirty:f,isDisabled:h,isReadonly:v,isPristine:g,isValid:b,isValidating:_,reset:E,resetValidation:w,validate:k,validationClasses:P}=XC(e,"v-input",u),O=C(()=>({id:u,messagesId:c,isDirty:f,isDisabled:h,isReadonly:v,isPristine:g,isValid:b,isValidating:_,reset:E,resetValidation:w,validate:k})),T=C(()=>{var y;return(y=e.errorMessages)!=null&&y.length||!g.value&&d.value.length?d.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages});return pe(()=>{var M,B,N,te;const y=!!(s.prepend||e.prependIcon),S=!!(s.append||e.appendIcon),A=T.value.length>0,z=!e.hideDetails||e.hideDetails==="auto"&&(A||!!s.details);return m("div",{class:["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix},a.value,i.value,P.value,e.class],style:e.style},[y&&m("div",{key:"prepend",class:"v-input__prepend"},[(M=s.prepend)==null?void 0:M.call(s,O.value),e.prependIcon&&m(o,{key:"prepend-icon",name:"prepend"},null)]),s.default&&m("div",{class:"v-input__control"},[(B=s.default)==null?void 0:B.call(s,O.value)]),S&&m("div",{key:"append",class:"v-input__append"},[e.appendIcon&&m(o,{key:"append-icon",name:"append"},null),(N=s.append)==null?void 0:N.call(s,O.value)]),z&&m("div",{class:"v-input__details"},[m(KC,{id:c.value,active:A,messages:T.value},{message:s.message}),(te=s.details)==null?void 0:te.call(s,O.value)])])}),{reset:E,resetValidation:w,validate:k}}}),QC=K({...Cu(),...Qr(mv(),["inline"])},"VCheckbox"),bl=oe()({name:"VCheckbox",inheritAttrs:!1,props:QC(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,t){let{attrs:n,slots:s}=t;const r=Ke(e,"modelValue"),{isFocused:a,focus:i,blur:o}=Su(e),l=It(),u=C(()=>e.id||`checkbox-${l}`);return pe(()=>{const[c,d]=Wl(n),[f,h]=pi.filterProps(e),[v,g]=pl.filterProps(e);return m(pi,ge({class:["v-checkbox",e.class]},c,f,{modelValue:r.value,"onUpdate:modelValue":b=>r.value=b,id:u.value,focused:a.value,style:e.style}),{...s,default:b=>{let{id:_,messagesId:E,isDisabled:w,isReadonly:k}=b;return m(pl,ge(v,{id:_.value,"aria-describedby":E.value,disabled:w.value,readonly:k.value},d,{modelValue:r.value,"onUpdate:modelValue":P=>r.value=P,onFocus:i,onBlur:o}),s)}})}),{}}});const ex=K({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...la({origin:"center center",scrollStrategy:"block",transition:{component:wu},zIndex:2400})},"VDialog"),wl=oe()({name:"VDialog",props:ex(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"modelValue"),{scopeId:r}=ia(),a=U();function i(l){var d,f;const u=l.relatedTarget,c=l.target;if(u!==c&&((d=a.value)!=null&&d.contentEl)&&((f=a.value)!=null&&f.globalTop)&&![document,a.value.contentEl].includes(c)&&!a.value.contentEl.contains(c)){const h=im(a.value.contentEl);if(!h.length)return;const v=h[0],g=h[h.length-1];u===v?g.focus():v.focus()}}st&&we(()=>s.value&&e.retainFocus,l=>{l?document.addEventListener("focusin",i):document.removeEventListener("focusin",i)},{immediate:!0}),we(s,async l=>{var u,c;await bt(),l?(u=a.value.contentEl)==null||u.focus({preventScroll:!0}):(c=a.value.activatorEl)==null||c.focus({preventScroll:!0})});const o=C(()=>ge({"aria-haspopup":"dialog","aria-expanded":String(s.value)},e.activatorProps));return pe(()=>{const[l]=Gn.filterProps(e);return m(Gn,ge({ref:a,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},l,{modelValue:s.value,"onUpdate:modelValue":u=>s.value=u,"aria-modal":"true",activatorProps:o.value,role:"dialog"},r),{activator:n.activator,default:function(){for(var u=arguments.length,c=new Array(u),d=0;d<u;d++)c[d]=arguments[d];return m(Ze,{root:"VDialog"},{default:()=>{var f;return[(f=n.default)==null?void 0:f.call(n,...c)]}})}})}),Qs({},a)}});const tx=K({color:String,inset:Boolean,length:[Number,String],thickness:[Number,String],vertical:Boolean,...Se(),...We()},"VDivider"),pr=oe()({name:"VDivider",props:tx(),setup(e,t){let{attrs:n}=t;const{themeClasses:s}=et(e),{textColorClasses:r,textColorStyles:a}=on(he(e,"color")),i=C(()=>{const o={};return e.length&&(o[e.vertical?"maxHeight":"maxWidth"]=de(e.length)),e.thickness&&(o[e.vertical?"borderRightWidth":"borderTopWidth"]=de(e.thickness)),o});return pe(()=>m("hr",{class:[{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},s.value,r.value,e.class],style:[i.value,a.value,e.style],"aria-orientation":!n.role||n.role==="separator"?e.vertical?"vertical":"horizontal":void 0,role:`${n.role||"separator"}`},null)),{}}});const nx=K({app:Boolean,color:String,height:{type:[Number,String],default:"auto"},...Jn(),...Se(),...On(),...kC(),...At(),...Ye({tag:"footer"}),...We()},"VFooter"),sx=oe()({name:"VFooter",props:nx(),setup(e,t){let{slots:n}=t;const{themeClasses:s}=et(e),{backgroundColorClasses:r,backgroundColorStyles:a}=ws(he(e,"color")),{borderClasses:i}=Xn(e),{elevationClasses:o}=In(e),{roundedClasses:l}=Vt(e),u=Ce(32),{resizeRef:c}=na(h=>{h.length&&(u.value=h[0].target.clientHeight)}),d=C(()=>e.height==="auto"?u.value:parseInt(e.height,10)),{layoutItemStyles:f}=OC({id:e.name,order:C(()=>parseInt(e.order,10)),position:C(()=>"bottom"),layoutSize:d,elementSize:C(()=>e.height==="auto"?void 0:d.value),active:C(()=>e.app),absolute:he(e,"absolute")});return pe(()=>m(e.tag,{ref:c,class:["v-footer",s.value,r.value,i.value,o.value,l.value,e.class],style:[a.value,e.app?f.value:{height:de(e.height)},e.style]},n)),{}}});const rx=K({fluid:{type:Boolean,default:!1},...Se(),...Ye()},"VContainer"),$d=oe()({name:"VContainer",props:rx(),setup(e,t){let{slots:n}=t;const{rtlClasses:s}=ts();return pe(()=>m(e.tag,{class:["v-container",{"v-container--fluid":e.fluid},s.value,e.class],style:e.style},n)),{}}}),Cv=(()=>zi.reduce((e,t)=>(e[t]={type:[Boolean,String,Number],default:!1},e),{}))(),xv=(()=>zi.reduce((e,t)=>{const n="offset"+xn(t);return e[n]={type:[String,Number],default:null},e},{}))(),_v=(()=>zi.reduce((e,t)=>{const n="order"+xn(t);return e[n]={type:[String,Number],default:null},e},{}))(),Bd={col:Object.keys(Cv),offset:Object.keys(xv),order:Object.keys(_v)};function ax(e,t,n){let s=e;if(!(n==null||n===!1)){if(t){const r=t.replace(e,"");s+=`-${r}`}return e==="col"&&(s="v-"+s),e==="col"&&(n===""||n===!0)||(s+=`-${n}`),s.toLowerCase()}}const ix=["auto","start","end","center","baseline","stretch"],ox=K({cols:{type:[Boolean,String,Number],default:!1},...Cv,offset:{type:[String,Number],default:null},...xv,order:{type:[String,Number],default:null},..._v,alignSelf:{type:String,default:null,validator:e=>ix.includes(e)},...Se(),...Ye()},"VCol"),Ne=oe()({name:"VCol",props:ox(),setup(e,t){let{slots:n}=t;const s=C(()=>{const r=[];let a;for(a in Bd)Bd[a].forEach(o=>{const l=e[o],u=ax(a,o,l);u&&r.push(u)});const i=r.some(o=>o.startsWith("v-col-"));return r.push({"v-col":!i||!e.cols,[`v-col-${e.cols}`]:e.cols,[`offset-${e.offset}`]:e.offset,[`order-${e.order}`]:e.order,[`align-self-${e.alignSelf}`]:e.alignSelf}),r});return()=>{var r;return Kn(e.tag,{class:[s.value,e.class],style:e.style},(r=n.default)==null?void 0:r.call(n))}}}),xu=["start","end","center"],Ev=["space-between","space-around","space-evenly"];function _u(e,t){return zi.reduce((n,s)=>{const r=e+xn(s);return n[r]=t(),n},{})}const lx=[...xu,"baseline","stretch"],Tv=e=>lx.includes(e),kv=_u("align",()=>({type:String,default:null,validator:Tv})),ux=[...xu,...Ev],Ov=e=>ux.includes(e),Iv=_u("justify",()=>({type:String,default:null,validator:Ov})),cx=[...xu,...Ev,"stretch"],Av=e=>cx.includes(e),Vv=_u("alignContent",()=>({type:String,default:null,validator:Av})),Hd={align:Object.keys(kv),justify:Object.keys(Iv),alignContent:Object.keys(Vv)},dx={align:"align",justify:"justify",alignContent:"align-content"};function fx(e,t,n){let s=dx[e];if(n!=null){if(t){const r=t.replace(e,"");s+=`-${r}`}return s+=`-${n}`,s.toLowerCase()}}const mx=K({dense:Boolean,noGutters:Boolean,align:{type:String,default:null,validator:Tv},...kv,justify:{type:String,default:null,validator:Ov},...Iv,alignContent:{type:String,default:null,validator:Av},...Vv,...Se(),...Ye()},"VRow"),lt=oe()({name:"VRow",props:mx(),setup(e,t){let{slots:n}=t;const s=C(()=>{const r=[];let a;for(a in Hd)Hd[a].forEach(i=>{const o=e[i],l=fx(a,i,o);l&&r.push(l)});return r.push({"v-row--no-gutters":e.noGutters,"v-row--dense":e.dense,[`align-${e.align}`]:e.align,[`justify-${e.justify}`]:e.justify,[`align-content-${e.alignContent}`]:e.alignContent}),r});return()=>{var r;return Kn(e.tag,{class:["v-row",s.value,e.class],style:e.style},(r=n.default)==null?void 0:r.call(n))}}}),Tr=Cs("flex-grow-1","div","VSpacer");const Pv=Symbol.for("vuetify:v-item-group"),hx=K({...Se(),...eu({selectedClass:"v-item--selected"}),...Ye(),...We()},"VItemGroup"),vx=oe()({name:"VItemGroup",props:hx(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:s}=et(e),{isSelected:r,select:a,next:i,prev:o,selected:l}=su(e,Pv);return()=>m(e.tag,{class:["v-item-group",s.value,e.class],style:e.style},{default:()=>{var u;return[(u=n.default)==null?void 0:u.call(n,{isSelected:r,select:a,next:i,prev:o,selected:l.value})]}})}}),gx=oe()({name:"VItem",props:tu(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const{isSelected:s,select:r,toggle:a,selectedClass:i,value:o,disabled:l}=nu(e,Pv);return()=>{var u;return(u=n.default)==null?void 0:u.call(n,{isSelected:s.value,selectedClass:i.value,select:r,toggle:a,value:o.value,disabled:l.value})}}});const yx=K({fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,...Se(),...Ut(),...Ye(),...We()},"VTable"),bi=oe()({name:"VTable",props:yx(),setup(e,t){let{slots:n}=t;const{themeClasses:s}=et(e),{densityClasses:r}=Xt(e);return pe(()=>m(e.tag,{class:["v-table",{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!n.top,"v-table--has-bottom":!!n.bottom,"v-table--hover":e.hover},s.value,r.value,e.class],style:e.style},{default:()=>{var a,i,o;return[(a=n.top)==null?void 0:a.call(n),n.default?m("div",{class:"v-table__wrapper",style:{height:de(e.height)}},[m("table",null,[n.default()])]):(i=n.wrapper)==null?void 0:i.call(n),(o=n.bottom)==null?void 0:o.call(n)]}})),{}}}),ht=e=>(Xg("data-v-3457b272"),e=e(),Qg(),e),px={class:"text-subtitle-2 pa-1"},bx=ht(()=>I("strong",null,"N° NC",-1)),wx=["disabled"],Sx=ht(()=>I("strong",null,"N° OT",-1)),Cx=["disabled"],xx=ht(()=>I("strong",null,"RUT",-1)),_x=["disabled"],Ex=ht(()=>I("strong",null,"FECHA",-1)),Tx=["disabled"],kx=ht(()=>I("div",{class:"text-grey"},"INFORMACIÓN REQUIRENTE",-1)),Ox={class:"font-weight-medium"},Ix={class:"font-weight-medium"},Ax={class:"font-weight-medium"},Vx=ht(()=>I("div",{class:"text-subtitle-2"},"RETIENE EL 13 %",-1)),Px=["value"],Mx=ht(()=>I("div",{class:"text-subtitle-2"},"BHE EMITIDA A:",-1)),Dx=ht(()=>I("div",null," BHE RUT 0-0 ",-1)),Nx=ht(()=>I("div",{class:"text-subtitle-2"},"BHE GLOSA:",-1)),Fx=ht(()=>I("thead",null,[I("tr",{class:"custom-thead"},[I("th",{class:"text-left"},"N°"),I("th",{class:"text-left"},"ITEM"),I("th",{class:"text-left"},"DESCRIPCIÓN"),I("th",{class:"text-left"},"CANTIDAD"),I("th",{class:"text-left"},"PRECIO UNITARIO"),I("th",{class:"text-left"},"TOTAL ITEM")])],-1)),Lx=ht(()=>I("td",null,"1",-1)),Rx=ht(()=>I("thead",null,[I("tr",null,[I("th",{class:"text-left"},"-"),I("th",{class:"text-left"},"N° NC"),I("th",{class:"text-left"},"N° OT"),I("th",{class:"text-left"},"Detalle"),I("th",{class:"text-left"},"Cliente"),I("th",{class:"text-left"},"Monto"),I("th",{class:"text-left"},"#")])],-1)),$x=["onClick"],Bx=ht(()=>I("td",null,null,-1)),Hx={class:"text-center"},Ux={class:"mt-5 text-center"},zx=ht(()=>I("div",{class:"font-weight-medium"},"TOTAL IMPUESTO A PAGAR:",-1)),jx=ht(()=>I("div",{class:"font-weight-medium"},"TOTAL DILIGENCIAS Y GASTOS TERCERO:",-1)),Wx=ht(()=>I("div",{class:"font-weight-medium"},"TOTAL BHE:",-1)),qx={class:"font-weight-medium"},Zx={class:"font-weight-medium"},Gx={class:"font-weight-medium"},Kx={__name:"Caja",setup(e){const{success:t,error:n,warning:s,hide:r}=Fi(),a=U(!1),i=U(!1),o=U(null),l=U(null),u=U(null),c=U(null);U("");const d=U(!1),f=U(null),h=U(null),v=U(!1),g=U(0),b=U([]),_=U([]),E=U("s"),w=U(null),k=U(null),P=U(!1),O=U(0),T=[{title:`Retiene ${O} Notario`,value:"s"},{title:`Retiene ${O} Cliente`,value:"n"}],y=U({cliente:null,rut:null,obs:null}),S=U({taxes:0,rightsAndTax:0,totalPayment:0,totalBhe:0,total:0}),A=U([]),z=U([]),M=W=>{W?w.value="0-0":w.value=null},B=()=>{i.value=!1,o.value=null,l.value=null,u.value=null,c.value=null,b.value=[],y.value={cliente:null,rut:null,obs:null},w.value=null,E.value=null,c.value=null,_.value=[],a.value=!1,S.value.taxes=0,S.value.rightsAndTax=0,S.value.totalPayment=0,S.value.totalBhe=0,S.value.total=0},N=(W,Z)=>{if(W==="nc")if(!o.value)n({msg:'Ingrese un valor para "nc"'});else{var $={nc:o.value};ee($)}else W==="ot"?l.value?se(W):n({msg:'Ingrese un valor para "ot"'}):W==="rut"?u.value?se(W):n({msg:'Ingrese un valor para "rut"'}):W==="fromDate"?c.value?se(W):n({msg:'Ingrese un valor para "fecha"'}):alert("Tipo de dato no reconocido")},te=()=>{if(!E.value){n({msg:"Ingrese un retenedor"});return}if(!w.value){n({msg:"Ingrese un RUT para emitir la BHE"});return}if(!k.value){n({msg:"Ingrese una glosa"});return}h.value=null,S.value.taxes>0?A.value=z.value.filter(W=>W.tipo!=="Por Pagar"):A.value=z.value,A.value&&A.value.length>0&&(P.value=!0)},ee=async W=>{var dn,An;const Z={accion:"traeNCCaja",nc:W.nc};var $=await gt.post("/ot/api/",Z).catch(p=>!1);if(d.value=!1,$){var ae=$.data.data[0];if(!$.data.estado){s({msg:$.data.mensaje});return}if(ae){const x=ae.detalle.map(q=>({...q,total_item:ae.con_cobro===1&&q.item=="DERECHOS"?0:q.total_item}));b.value=x,o.value=ae.id,l.value=ae.ot,g.value=ae.con_cobro,u.value=ae.rut?Ds(ae.rut):ae.rut,y.value.rut=ae.rut,y.value.cliente=ae.cliente,y.value.obs=ae.tipo_ot,w.value=ae.rut?Ds(ae.rut):ae.rut,E.value=ae.retiene_emisor,k.value=ae.glosa??"Servicios Notariales",c.value=(dn=ae.detalle[0])!=null&&dn.fecha?J((An=ae.detalle[0])==null?void 0:An.fecha,"front"):null;let V=ae.detalle.filter(q=>q.item=="DERECHOS");V=V.reduce((q,j)=>q+parseInt(j.total),0),S.value.totalBhe=V;let R=ae.detalle.filter(q=>q.item=="IMPUESTOS");S.value.taxes=R.reduce((q,j)=>q+parseInt(j.total),0);let L=ae.detalle.filter(q=>q.item!="IMPUESTOS"&&q.item!="DERECHOS");S.value.othersExpenses=L.reduce((q,j)=>q+parseInt(j.total),0);const G=x.reduce((q,j)=>q+parseInt(j.total_item),0);S.value.totalPayment=G,S.total=ae.total,a.value=!0}}else s({msg:"Ocurrio un error intentelo nuevamente."});i.value=!1},se=async W=>{i.value=!0;const Z={accion:"buscarNCCaja",rut:W=="rut"&&u.value?Ds(u.value):null,fecha:W=="fromDate"?J(c.value,"back"):null,ot:W=="ot"?l.value:null,nc:W=="nc"?o.value:null};var $=await gt.post("/ot/api/",Z).catch(dn=>!1);if($){var ae=$.data.data;if(!$.data.estado){s({msg:$.data.mensaje}),i.value=!1;return}ae.length>1&&(_.value=ae,d.value=!0),ae.length==1&&ee(ae[0])}else s({msg:"Ocurrio un error intentelo nuevamente."});i.value=!1},Y=W=>{f.value=W},J=(W,Z)=>{if(Z=="back")var $=re.fromFormat(W,"yyyy-MM-dd").toFormat("dd-MM-yyyy");if(Z=="front")var $=re.fromFormat(W,"dd-MM-yyyy").toFormat("yyyy-MM-dd");return $??null},me=async()=>{if(!vs("bhe_generar")){n({msg:hs});return}if(!E.value){n({msg:"Ingrese un retenedor"});return}if(!w.value){n({msg:"Ingrese un RUT para emitir la BHE"});return}if(!k.value){n({msg:"Ingrese una glosa"});return}v.value=!0;let W=0;S.totalBhe>0&&g==0?W=S.value.totalBhe:W=S.value.totalPayment,console.log("montoHonorario =>",W);const Z={accion:"emitirBHE",form:{ot:l.value,nc:o.value,rut:w.value,glosa:k.value??"Servicios notariales",retiene_emisor:E.value,porcentaje:13,montoHonorario:W,tipo_pago:A.value[h.value].tipo}};var $=await gt.post("/caja_v2/api/",Z).catch(dn=>!1);if($){var ae=$.data;ae.estado?(v.value=!1,t({msg:ae.mensaje}),P.value=!1,ae.data.boleta_pdf&&ae.data.boleta_pdf.length>1&&Ee(ae.data.boleta_pdf),ae.data.comprobante_url&&ae.data.comprobante_url.length>1&&Re(ae.data.comprobante_url)):s({msg:ae.mensaje})}else s({msg:"Ocurrio un error, Intentelo nuevamente"});v.value=!1},fe=async()=>{const W={accion:"tiposPagos"};var Z=await gt.post("/caja_v2/api/",W).catch($=>!1);Z?z.value=Z.data.data:s({msg:"Ocurrio un error, Intentelo nuevamente"})},Ee=W=>{var Z=window.open();Z.document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64,"+W+"'></iframe>"),Z.document.close()},Re=W=>{window.open(W,"_blank")};fe();const ze=W=>{if(W&&W.length>3){var Z=`./icons/${W}.png`,$=new URL(Z,import.meta.url).href;return $}else return console.log("dos"),new URL("./assets/icons/documento.png",self.location).href};return(async()=>{const W={accion:"creaTablas"};var Z=await gt.post("/caja_v2/api/",W).catch($=>!1);Z||s({msg:"Ocurrio un error, Intentelo nuevamente"})})(),(W,Z)=>(Ae(),St(_e,null,[m($d,{fluid:"",class:"ma-0 pa-0"},{default:F(()=>[m(Wr,{class:"fill-height"},{default:F(()=>[m(Un,{class:"mx-auto pa-3",flat:""},{default:F(()=>[m(lt,null,{default:F(()=>[m(Ne,{cols:"12",sm:"12"},{default:F(()=>[m(lt,{"no-gutters":""},{default:F(()=>[m(Ne,{cols:"4",lg:"3",md:"4",class:"pa-0"},{default:F(()=>[I("ul",px,[I("li",null,[bx,Xe(I("input",{"onUpdate:modelValue":Z[0]||(Z[0]=$=>o.value=$),class:"input-caja",disabled:a.value,onKeyup:Z[1]||(Z[1]=Ca($=>N("nc",$),["enter"]))},null,40,wx),[[Os,o.value]]),m(Be,{variant:"text",color:"primary",density:"compact",icon:"mdi-magnify",class:"pl-1 mt-n1",loading:i.value,onClick:Z[2]||(Z[2]=$=>N("nc",$))},null,8,["loading"])]),I("li",null,[Sx,Xe(I("input",{"onUpdate:modelValue":Z[3]||(Z[3]=$=>l.value=$),class:"input-caja",disabled:a.value,onKeyup:Z[4]||(Z[4]=Ca($=>N("ot",$),["enter"]))},null,40,Cx),[[Os,l.value]]),m(Be,{variant:"text",color:"primary",density:"compact",icon:"mdi-magnify",class:"pl-1 mt-n1",loading:i.value,onClick:Z[5]||(Z[5]=$=>N("ot",$))},null,8,["loading"])]),I("li",null,[xx,Xe(I("input",{"onUpdate:modelValue":Z[6]||(Z[6]=$=>u.value=$),onInput:Z[7]||(Z[7]=$=>u.value=rt(Ds)($.target.value,u.value)),class:"input-caja",disabled:a.value,onKeyup:Z[8]||(Z[8]=Ca($=>N("rut",$),["enter"]))},null,40,_x),[[Os,u.value]]),m(Be,{variant:"text",color:"primary",density:"compact",icon:"mdi-magnify",class:"pl-1 mt-n1",loading:i.value,onClick:Z[9]||(Z[9]=$=>N("rut",$))},null,8,["loading"])]),I("li",null,[Ex,Xe(I("input",{"onUpdate:modelValue":Z[10]||(Z[10]=$=>c.value=$),class:"input-caja",type:"date",disabled:a.value,onKeyup:Z[11]||(Z[11]=Ca($=>N("fromDate",$),["enter"]))},null,40,Tx),[[Os,c.value]]),m(Be,{variant:"text",color:"primary",density:"compact",icon:"mdi-magnify",class:"pl-1 mt-n1",loading:i.value,onClick:Z[12]||(Z[12]=$=>N("fromDate",$))},null,8,["loading"])]),I("li",null,[m(Be,{size:"x-small",color:"error",variant:"text",class:"button ml-n2",onClick:B,"append-icon":"mdi-close"},{default:F(()=>[Pe("Limpiar filtros")]),_:1})])])]),_:1}),m(Ne,{cols:"9",lg:"9",md:"8"},{default:F(()=>[m(lt,{"no-gutters":""},{default:F(()=>[m(Ne,{cols:"6",lg:"6",md:"6",class:"text-subtitle-2"},{default:F(()=>[kx,I("div",Ox,"NOMBRE: "+ie(y.value.cliente),1),I("div",Ix,"RUT: "+ie(y.value.rut),1),I("div",Ax,"OBSERVACIONES: "+ie(y.value.obs),1)]),_:1}),m(Ne,{cols:"12",lg:"6",md:"6"},{default:F(()=>[m(lt,{"no-gutters":""},{default:F(()=>[m(Ne,{cols:"4"},{default:F(()=>[Vx]),_:1}),m(Ne,{cols:"8"},{default:F(()=>[Xe(I("select",{"onUpdate:modelValue":Z[13]||(Z[13]=$=>E.value=$),class:"input-caja"},[(Ae(),St(_e,null,Ns(T,$=>I("option",{value:$.value},ie($.title),9,Px)),64))],512),[[Tp,E.value]])]),_:1})]),_:1}),m(lt,{"no-gutters":"",class:"mt-1"},{default:F(()=>[m(Ne,{cols:"4"},{default:F(()=>[Mx,m(bl,{modelValue:W.checkbox,"onUpdate:modelValue":Z[14]||(Z[14]=$=>W.checkbox=$),onChange:Z[15]||(Z[15]=$=>M($.target.checked)),density:"compact"},{label:F(()=>[Dx]),_:1},8,["modelValue"])]),_:1}),m(Ne,{cols:"8"},{default:F(()=>[Xe(I("input",{"onUpdate:modelValue":Z[16]||(Z[16]=$=>w.value=$),onInput:Z[17]||(Z[17]=$=>w.value=rt(Ds)($.target.value)),class:"input-caja"},null,544),[[Os,w.value]])]),_:1})]),_:1}),m(lt,{"no-gutters":"",class:"mt-1"},{default:F(()=>[m(Ne,{cols:"4"},{default:F(()=>[Nx]),_:1}),m(Ne,{cols:"8"},{default:F(()=>[Xe(I("textarea",{"onUpdate:modelValue":Z[18]||(Z[18]=$=>k.value=$),class:"input-caja",maxlength:"95"},null,512),[[Os,k.value]])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),m(lt),m(lt,null,{default:F(()=>[m(Ne,null,{default:F(()=>[m(bi,{density:"compact"},{default:F(()=>[Fx,I("tbody",null,[b.value.length>0?(Ae(!0),St(_e,{key:0},Ns(b.value,($,ae)=>(Ae(),St("tr",{key:$.id},[I("td",null,ie(ae+1),1),I("td",null,ie($.item),1),I("td",null,ie($.item_detalle),1),Lx,I("td",null,ie(W.$filters.moneda($.total)),1),I("td",null,ie(W.$filters.moneda($.total_item)),1)]))),128)):tn("",!0)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),m(lt,{justify:"center"},{default:F(()=>[m(wl,{modelValue:d.value,"onUpdate:modelValue":Z[21]||(Z[21]=$=>d.value=$),scrollable:"",width:"auto"},{default:F(()=>[m(Un,null,{default:F(()=>[m(gi,null,{default:F(()=>[Pe("Seleccionar Nota de Cobro")]),_:1}),m(pr),m(yi,{style:{height:"600px"}},{default:F(()=>[m(bi,{density:"compact"},{default:F(()=>[Rx,I("tbody",null,[(Ae(!0),St(_e,null,Ns(_.value,$=>(Ae(),St("tr",{key:$.name,class:Ar({"selected-row":f.value===$}),onClick:ae=>Y($)},[I("td",null,[m(it,{color:f.value===$?"green":"black",icon:f.value===$?"mdi-radiobox-marked":"mdi-radiobox-blank"},null,8,["color","icon"])]),I("td",null,ie($.nc),1),I("td",null,ie($.ot),1),I("td",null,ie($.materia),1),I("td",null,ie($.cliente),1),I("td",null,ie(W.$filters.moneda($.total)),1),Bx],10,$x))),128))])]),_:1})]),_:1}),m(pr),m(vi,null,{default:F(()=>[m(Be,{color:"blue-darken-1",variant:"text",onClick:Z[19]||(Z[19]=$=>d.value=!1)},{default:F(()=>[Pe(" Cerrar ")]),_:1}),m(Tr),m(Be,{color:"blue-darken-1","append-icon":"mdi-arrow-right-circle-outline",onClick:Z[20]||(Z[20]=$=>ee(f.value)),disabled:f.value==null},{default:F(()=>[Pe(" Continuar ")]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),m(wl,{modelValue:P.value,"onUpdate:modelValue":Z[26]||(Z[26]=$=>P.value=$),scrollable:"","min-width":"1200",persistent:""},{default:F(()=>[m(Un,null,{default:F(()=>[m(gi,null,{default:F(()=>[Pe("Seleccionar forma de pago ")]),_:1}),m(pr),m(yi,{style:{height:"400px"}},{default:F(()=>[m(vx,{"selected-class":"bg-primary",modelValue:h.value,"onUpdate:modelValue":Z[22]||(Z[22]=$=>h.value=$)},{default:F(()=>[m($d,null,{default:F(()=>[m(lt,null,{default:F(()=>[(Ae(!0),St(_e,null,Ns(A.value,($,ae)=>(Ae(),ut(Ne,{key:ae,cols:"4"},{default:F(()=>[m(gx,null,{default:F(({isSelected:dn,selectedClass:An,toggle:p})=>[m(Un,{class:Ar(["d-flex flex-column align-center justify-center",["d-flex align-center",An]]),onClick:p},{default:F(()=>[m(no,{src:ze($.icon),width:"70",height:"70",class:"align-self-center"},null,8,["src"]),I("div",Hx,ie($.tipo),1)]),_:2},1032,["class","onClick"])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"]),I("div",Ux,[m(Tr),S.value.totalBhe>0&&g.value==0?(Ae(),ut(Be,{key:0,color:"green-darken-1",size:"x-large","append-icon":"mdi-arrow-right-circle-outline",onClick:Z[23]||(Z[23]=$=>me()),disabled:h.value==null,loading:v.value},{default:F(()=>[Pe(" Emitir BHE $ "+ie(W.$filters.moneda(S.value.totalBhe)),1)]),_:1},8,["disabled","loading"])):(Ae(),ut(Be,{key:1,color:"green-darken-1",size:"x-large","append-icon":"mdi-arrow-right-circle-outline",onClick:Z[24]||(Z[24]=$=>me()),disabled:h.value==null,loading:v.value},{default:F(()=>[Pe(" Emitir Comprobante Pago $ "+ie(W.$filters.moneda(S.value.totalPayment)),1)]),_:1},8,["disabled","loading"]))])]),_:1}),m(pr),m(vi,null,{default:F(()=>[m(Be,{color:"blue-darken-1",variant:"text",onClick:Z[25]||(Z[25]=$=>P.value=!1)},{default:F(()=>[Pe(" Cerrar ")]),_:1}),m(Tr)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(sx,{app:"",height:"95",class:"bg-grey-lighten-3"},{default:F(()=>[m(lt,null,{default:F(()=>[m(Ne,{cols:"12",lg:"10",md:"10",sm:"12",xs:"12",class:"text-right"},{default:F(()=>[m(lt,null,{default:F(()=>[m(Ne,{cols:"9",class:"text-subtitle-1"},{default:F(()=>[zx,jx,Wx]),_:1}),m(Ne,{cols:"3",class:"text-subtitle-1 text-right"},{default:F(()=>[I("div",qx,"$ "+ie(W.$filters.moneda(S.value.taxes)),1),I("div",Zx,"$ "+ie(W.$filters.moneda(S.value.othersExpenses)),1),I("div",Gx,"$ "+ie(W.$filters.moneda(S.value.totalBhe)),1)]),_:1})]),_:1})]),_:1}),m(Ne,{cols:"12",lg:"2",md:"2",sm:"12",xs:"12",class:"d-flex align-end"},{default:F(()=>{var $;return[m(Be,{block:"",color:"primary",size:"x-large",onClick:te,disabled:(($=S.value)==null?void 0:$.totalPayment)==0},{default:F(()=>{var ae;return[Pe("PAGAR $"+ie(W.$filters.moneda((ae=S.value)==null?void 0:ae.totalPayment)),1)]}),_:1},8,["disabled"])]}),_:1})]),_:1})]),_:1})],64))}},Yx=PC(Kx,[["__scopeId","data-v-3457b272"]]),Mv={__name:"confirm",setup(e,{expose:t}){const n=U(),s=U(),r=U(!1),a=U(null),i=U(null),o=U(null),l=d=>{a.value=d.title,i.value=d.message,o.value=d.okButton,n.value=void 0,s.value=void 0;const f=new Promise((h,v)=>{n.value=h,s.value=v});return r.value=!0,f},u=()=>{r.value=!1,n.value&&n.value(!0)},c=()=>{r.value=!1,n.value&&n.value(!1)};return t({show:l,_confirm:u,_cancel:c}),(d,f)=>(Ae(),ut(lt,{justify:"center"},{default:F(()=>[m(wl,{modelValue:r.value,"onUpdate:modelValue":f[0]||(f[0]=h=>r.value=h),persistent:"",width:"auto",ref:"popup"},{default:F(()=>[m(Un,null,{default:F(()=>[m(gi,{class:"text-h5"},{default:F(()=>[Pe(ie(a.value),1)]),_:1}),m(yi,null,{default:F(()=>[Pe(ie(i.value),1)]),_:1}),m(vi,null,{default:F(()=>[m(Tr),m(Be,{color:"error",variant:"text",onClick:c},{default:F(()=>[Pe(ie(d.cancelButton||"Cancelar"),1)]),_:1}),m(Be,{color:"primary",onClick:u},{default:F(()=>[Pe(ie(o.value||"Confirmar"),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}};const Jx=Cs("v-alert-title"),Xx=["success","info","warning","error"],Qx=K({border:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:Fe,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>Xx.includes(e)},...Se(),...Ut(),...Qn(),...On(),...sa(),...$i(),...At(),...Ye(),...We(),...un({variant:"flat"})},"VAlert"),e_=oe()({name:"VAlert",props:Qx(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{emit:n,slots:s}=t;const r=Ke(e,"modelValue"),a=C(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),i=C(()=>({color:e.color??e.type,variant:e.variant})),{themeClasses:o}=et(e),{colorClasses:l,colorStyles:u,variantClasses:c}=_s(i),{densityClasses:d}=Xt(e),{dimensionStyles:f}=es(e),{elevationClasses:h}=In(e),{locationStyles:v}=ra(e),{positionClasses:g}=Bi(e),{roundedClasses:b}=Vt(e),{textColorClasses:_,textColorStyles:E}=on(he(e,"borderColor")),{t:w}=Ri(),k=C(()=>({"aria-label":w(e.closeLabel),onClick(P){r.value=!1,n("click:close",P)}}));return()=>{const P=!!(s.prepend||a.value),O=!!(s.title||e.title),T=!!(s.close||e.closable);return r.value&&m(e.tag,{class:["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},o.value,l.value,d.value,h.value,g.value,b.value,c.value,e.class],style:[u.value,f.value,v.value,e.style],role:"alert"},{default:()=>{var y,S;return[xs(!1,"v-alert"),e.border&&m("div",{key:"border",class:["v-alert__border",_.value],style:E.value},null),P&&m("div",{key:"prepend",class:"v-alert__prepend"},[s.prepend?m(Ze,{key:"prepend-defaults",disabled:!a.value,defaults:{VIcon:{density:e.density,icon:a.value,size:e.prominent?44:28}}},s.prepend):m(it,{key:"prepend-icon",density:e.density,icon:a.value,size:e.prominent?44:28},null)]),m("div",{class:"v-alert__content"},[O&&m(Jx,{key:"title"},{default:()=>{var A;return[((A=s.title)==null?void 0:A.call(s))??e.title]}}),((y=s.text)==null?void 0:y.call(s))??e.text,(S=s.default)==null?void 0:S.call(s)]),s.append&&m("div",{key:"append",class:"v-alert__append"},[s.append()]),T&&m("div",{key:"close",class:"v-alert__close"},[s.close?m(Ze,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>{var A;return[(A=s.close)==null?void 0:A.call(s,{props:k.value})]}}):m(Be,ge({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},k.value),null)])]}})}}});const Dv=Symbol.for("vuetify:v-chip-group"),t_=K({column:Boolean,filter:Boolean,valueComparator:{type:Function,default:Js},...Se(),...eu({selectedClass:"v-chip--selected"}),...Ye(),...We(),...un({variant:"tonal"})},"VChipGroup");oe()({name:"VChipGroup",props:t_(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{themeClasses:s}=et(e),{isSelected:r,select:a,next:i,prev:o,selected:l}=su(e,Dv);return Yn({VChip:{color:he(e,"color"),disabled:he(e,"disabled"),filter:he(e,"filter"),variant:he(e,"variant")}}),pe(()=>m(e.tag,{class:["v-chip-group",{"v-chip-group--column":e.column},s.value,e.class],style:e.style},{default:()=>{var u;return[(u=n.default)==null?void 0:u.call(n,{isSelected:r,select:a,next:i,prev:o,selected:l.value})]}})),{}}});const n_=K({activeClass:String,appendAvatar:String,appendIcon:Fe,closable:Boolean,closeIcon:{type:Fe,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:Fe,ripple:{type:[Boolean,Object],default:!0},text:String,modelValue:{type:Boolean,default:!0},onClick:an(),onClickOnce:an(),...Jn(),...Se(),...Ut(),...On(),...tu(),...At(),...Ui(),...ea(),...Ye({tag:"span"}),...We(),...un({variant:"tonal"})},"VChip"),s_=oe()({name:"VChip",directives:{Ripple:aa},props:n_(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,t){let{attrs:n,emit:s,slots:r}=t;const{t:a}=Ri(),{borderClasses:i}=Xn(e),{colorClasses:o,colorStyles:l,variantClasses:u}=_s(e),{densityClasses:c}=Xt(e),{elevationClasses:d}=In(e),{roundedClasses:f}=Vt(e),{sizeClasses:h}=ta(e),{themeClasses:v}=et(e),g=Ke(e,"modelValue"),b=nu(e,Dv,!1),_=Hi(e,n),E=C(()=>e.link!==!1&&_.isLink.value),w=C(()=>!e.disabled&&e.link!==!1&&(!!b||e.link||_.isClickable.value)),k=C(()=>({"aria-label":a(e.closeLabel),onClick(T){g.value=!1,s("click:close",T)}}));function P(T){var y;s("click",T),w.value&&((y=_.navigate)==null||y.call(_,T),b==null||b.toggle())}function O(T){(T.key==="Enter"||T.key===" ")&&(T.preventDefault(),P(T))}return()=>{const T=_.isLink.value?"a":e.tag,y=!!(e.appendIcon||e.appendAvatar),S=!!(y||r.append),A=!!(r.close||e.closable),z=!!(r.filter||e.filter)&&b,M=!!(e.prependIcon||e.prependAvatar),B=!!(M||r.prepend),N=!b||b.isSelected.value;return g.value&&Xe(m(T,{class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":w.value,"v-chip--filter":z,"v-chip--pill":e.pill},v.value,i.value,N?o.value:void 0,c.value,d.value,f.value,h.value,u.value,b==null?void 0:b.selectedClass.value,e.class],style:[N?l.value:void 0,e.style],disabled:e.disabled||void 0,draggable:e.draggable,href:_.href.value,tabindex:w.value?0:void 0,onClick:P,onKeydown:w.value&&!E.value&&O},{default:()=>{var te;return[xs(w.value,"v-chip"),z&&m(bv,{key:"filter"},{default:()=>[Xe(m("div",{class:"v-chip__filter"},[r.filter?Xe(m(Ze,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},null),[[bn("slot"),r.filter,"default"]]):m(it,{key:"filter-icon",icon:e.filterIcon},null)]),[[Ys,b.isSelected.value]])]}),B&&m("div",{key:"prepend",class:"v-chip__prepend"},[r.prepend?m(Ze,{key:"prepend-defaults",disabled:!M,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},r.prepend):m(_e,null,[e.prependIcon&&m(it,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&m(Zs,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),m("div",{class:"v-chip__content"},[((te=r.default)==null?void 0:te.call(r,{isSelected:b==null?void 0:b.isSelected.value,selectedClass:b==null?void 0:b.selectedClass.value,select:b==null?void 0:b.select,toggle:b==null?void 0:b.toggle,value:b==null?void 0:b.value.value,disabled:e.disabled}))??e.text]),S&&m("div",{key:"append",class:"v-chip__append"},[r.append?m(Ze,{key:"append-defaults",disabled:!y,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},r.append):m(_e,null,[e.appendIcon&&m(it,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&m(Zs,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),A&&m("div",ge({key:"close",class:"v-chip__close"},k.value),[r.close?m(Ze,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},r.close):m(it,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}}),[[bn("ripple"),w.value&&e.ripple,null]])}}});const Sl=Symbol.for("vuetify:list");function Nv(){const e=je(Sl,{hasPrepend:Ce(!1),updateHasPrepend:()=>null}),t={hasPrepend:Ce(!1),updateHasPrepend:n=>{n&&(t.hasPrepend.value=n)}};return _t(Sl,t),e}function Fv(){return je(Sl,null)}const r_={open:e=>{let{id:t,value:n,opened:s,parents:r}=e;if(n){const a=new Set;a.add(t);let i=r.get(t);for(;i!=null;)a.add(i),i=r.get(i);return a}else return s.delete(t),s},select:()=>null},Lv={open:e=>{let{id:t,value:n,opened:s,parents:r}=e;if(n){let a=r.get(t);for(s.add(t);a!=null&&a!==t;)s.add(a),a=r.get(a);return s}else s.delete(t);return s},select:()=>null},a_={open:Lv.open,select:e=>{let{id:t,value:n,opened:s,parents:r}=e;if(!n)return s;const a=[];let i=r.get(t);for(;i!=null;)a.push(i),i=r.get(i);return new Set(a)}},Eu=e=>{const t={select:n=>{let{id:s,value:r,selected:a}=n;if(s=ye(s),e&&!r){const i=Array.from(a.entries()).reduce((o,l)=>{let[u,c]=l;return c==="on"?[...o,u]:o},[]);if(i.length===1&&i[0]===s)return a}return a.set(s,r?"on":"off"),a},in:(n,s,r)=>{let a=new Map;for(const i of n||[])a=t.select({id:i,value:!0,selected:new Map(a),children:s,parents:r});return a},out:n=>{const s=[];for(const[r,a]of n.entries())a==="on"&&s.push(r);return s}};return t},Rv=e=>{const t=Eu(e);return{select:s=>{let{selected:r,id:a,...i}=s;a=ye(a);const o=r.has(a)?new Map([[a,r.get(a)]]):new Map;return t.select({...i,id:a,selected:o})},in:(s,r,a)=>{let i=new Map;return s!=null&&s.length&&(i=t.in(s.slice(0,1),r,a)),i},out:(s,r,a)=>t.out(s,r,a)}},i_=e=>{const t=Eu(e);return{select:s=>{let{id:r,selected:a,children:i,...o}=s;return r=ye(r),i.has(r)?a:t.select({id:r,selected:a,children:i,...o})},in:t.in,out:t.out}},o_=e=>{const t=Rv(e);return{select:s=>{let{id:r,selected:a,children:i,...o}=s;return r=ye(r),i.has(r)?a:t.select({id:r,selected:a,children:i,...o})},in:t.in,out:t.out}},l_=e=>{const t={select:n=>{let{id:s,value:r,selected:a,children:i,parents:o}=n;s=ye(s);const l=new Map(a),u=[s];for(;u.length;){const d=u.shift();a.set(d,r?"on":"off"),i.has(d)&&u.push(...i.get(d))}let c=o.get(s);for(;c;){const d=i.get(c),f=d.every(v=>a.get(v)==="on"),h=d.every(v=>!a.has(v)||a.get(v)==="off");a.set(c,f?"on":h?"off":"indeterminate"),c=o.get(c)}return e&&!r&&Array.from(a.entries()).reduce((f,h)=>{let[v,g]=h;return g==="on"?[...f,v]:f},[]).length===0?l:a},in:(n,s,r)=>{let a=new Map;for(const i of n||[])a=t.select({id:i,value:!0,selected:new Map(a),children:s,parents:r});return a},out:(n,s)=>{const r=[];for(const[a,i]of n.entries())i==="on"&&!s.has(a)&&r.push(a);return r}};return t},qr=Symbol.for("vuetify:nested"),$v={id:Ce(),root:{register:()=>null,unregister:()=>null,parents:U(new Map),children:U(new Map),open:()=>null,openOnSelect:()=>null,select:()=>null,opened:U(new Set),selected:U(new Map),selectedValues:U([])}},u_=K({selectStrategy:[String,Function],openStrategy:[String,Object],opened:Array,selected:Array,mandatory:Boolean},"nested"),c_=e=>{let t=!1;const n=U(new Map),s=U(new Map),r=Ke(e,"opened",e.opened,d=>new Set(d),d=>[...d.values()]),a=C(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single-leaf":return o_(e.mandatory);case"leaf":return i_(e.mandatory);case"independent":return Eu(e.mandatory);case"single-independent":return Rv(e.mandatory);case"classic":default:return l_(e.mandatory)}}),i=C(()=>{if(typeof e.openStrategy=="object")return e.openStrategy;switch(e.openStrategy){case"list":return a_;case"single":return r_;case"multiple":default:return Lv}}),o=Ke(e,"selected",e.selected,d=>a.value.in(d,n.value,s.value),d=>a.value.out(d,n.value,s.value));ln(()=>{t=!0});function l(d){const f=[];let h=d;for(;h!=null;)f.unshift(h),h=s.value.get(h);return f}const u=dt("nested"),c={id:Ce(),root:{opened:r,selected:o,selectedValues:C(()=>{const d=[];for(const[f,h]of o.value.entries())h==="on"&&d.push(f);return d}),register:(d,f,h)=>{f&&d!==f&&s.value.set(d,f),h&&n.value.set(d,[]),f!=null&&n.value.set(f,[...n.value.get(f)||[],d])},unregister:d=>{if(t)return;n.value.delete(d);const f=s.value.get(d);if(f){const h=n.value.get(f)??[];n.value.set(f,h.filter(v=>v!==d))}s.value.delete(d),r.value.delete(d)},open:(d,f,h)=>{u.emit("click:open",{id:d,value:f,path:l(d),event:h});const v=i.value.open({id:d,value:f,opened:new Set(r.value),children:n.value,parents:s.value,event:h});v&&(r.value=v)},openOnSelect:(d,f,h)=>{const v=i.value.select({id:d,value:f,selected:new Map(o.value),opened:new Set(r.value),children:n.value,parents:s.value,event:h});v&&(r.value=v)},select:(d,f,h)=>{u.emit("click:select",{id:d,value:f,path:l(d),event:h});const v=a.value.select({id:d,value:f,selected:new Map(o.value),children:n.value,parents:s.value,event:h});v&&(o.value=v),c.root.openOnSelect(d,f,h)},children:n,parents:s}};return _t(qr,c),c.root},Bv=(e,t)=>{const n=je(qr,$v),s=Symbol(It()),r=C(()=>e.value!==void 0?e.value:s),a={...n,id:r,open:(i,o)=>n.root.open(r.value,i,o),openOnSelect:(i,o)=>n.root.openOnSelect(r.value,i,o),isOpen:C(()=>n.root.opened.value.has(r.value)),parent:C(()=>n.root.parents.value.get(r.value)),select:(i,o)=>n.root.select(r.value,i,o),isSelected:C(()=>n.root.selected.value.get(ye(r.value))==="on"),isIndeterminate:C(()=>n.root.selected.value.get(r.value)==="indeterminate"),isLeaf:C(()=>!n.root.children.value.get(r.value)),isGroupActivator:n.isGroupActivator};return!n.isGroupActivator&&n.root.register(r.value,n.id.value,t),ln(()=>{!n.isGroupActivator&&n.root.unregister(r.value)}),t&&_t(qr,a),a},d_=()=>{const e=je(qr,$v);_t(qr,{...e,isGroupActivator:!0})};function f_(){const e=Ce(!1);return Tn(()=>{window.requestAnimationFrame(()=>{e.value=!0})}),{ssrBootStyles:C(()=>e.value?void 0:{transition:"none !important"}),isBooted:Yr(e)}}const m_=Xs({name:"VListGroupActivator",setup(e,t){let{slots:n}=t;return d_(),()=>{var s;return(s=n.default)==null?void 0:s.call(n)}}}),h_=K({activeColor:String,baseColor:String,color:String,collapseIcon:{type:Fe,default:"$collapse"},expandIcon:{type:Fe,default:"$expand"},prependIcon:Fe,appendIcon:Fe,fluid:Boolean,subgroup:Boolean,title:String,value:null,...Se(),...Ye()},"VListGroup"),Ud=oe()({name:"VListGroup",props:h_(),setup(e,t){let{slots:n}=t;const{isOpen:s,open:r,id:a}=Bv(he(e,"value"),!0),i=C(()=>`v-list-group--id-${String(a.value)}`),o=Fv(),{isBooted:l}=f_();function u(h){r(!s.value,h)}const c=C(()=>({onClick:u,class:"v-list-group__header",id:i.value})),d=C(()=>s.value?e.collapseIcon:e.expandIcon),f=C(()=>({VListItem:{active:s.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&d.value,appendIcon:e.appendIcon||!e.subgroup&&d.value,title:e.title,value:e.value}}));return pe(()=>m(e.tag,{class:["v-list-group",{"v-list-group--prepend":o==null?void 0:o.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":s.value},e.class],style:e.style},{default:()=>[n.activator&&m(Ze,{defaults:f.value},{default:()=>[m(m_,null,{default:()=>[n.activator({props:c.value,isOpen:s.value})]})]}),m(Hn,{transition:{component:pv},disabled:!l.value},{default:()=>{var h;return[Xe(m("div",{class:"v-list-group__items",role:"group","aria-labelledby":i.value},[(h=n.default)==null?void 0:h.call(n)]),[[Ys,s.value]])]}})]})),{}}});const v_=Cs("v-list-item-subtitle"),g_=Cs("v-list-item-title"),y_=K({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:Fe,baseColor:String,disabled:Boolean,lines:String,link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:Fe,ripple:{type:[Boolean,Object],default:!0},subtitle:[String,Number,Boolean],title:[String,Number,Boolean],value:null,onClick:an(),onClickOnce:an(),...Jn(),...Se(),...Ut(),...Qn(),...On(),...At(),...Ui(),...Ye(),...We(),...un({variant:"text"})},"VListItem"),wi=oe()({name:"VListItem",directives:{Ripple:aa},props:y_(),emits:{click:e=>!0},setup(e,t){let{attrs:n,slots:s,emit:r}=t;const a=Hi(e,n),i=C(()=>e.value===void 0?a.href.value:e.value),{select:o,isSelected:l,isIndeterminate:u,isGroupActivator:c,root:d,parent:f,openOnSelect:h}=Bv(i,!1),v=Fv(),g=C(()=>{var Y;return e.active!==!1&&(e.active||((Y=a.isActive)==null?void 0:Y.value)||l.value)}),b=C(()=>e.link!==!1&&a.isLink.value),_=C(()=>!e.disabled&&e.link!==!1&&(e.link||a.isClickable.value||e.value!=null&&!!v)),E=C(()=>e.rounded||e.nav),w=C(()=>e.color??e.activeColor),k=C(()=>({color:g.value?w.value??e.baseColor:e.baseColor,variant:e.variant}));we(()=>{var Y;return(Y=a.isActive)==null?void 0:Y.value},Y=>{Y&&f.value!=null&&d.open(f.value,!0),Y&&h(Y)},{immediate:!0});const{themeClasses:P}=et(e),{borderClasses:O}=Xn(e),{colorClasses:T,colorStyles:y,variantClasses:S}=_s(k),{densityClasses:A}=Xt(e),{dimensionStyles:z}=es(e),{elevationClasses:M}=In(e),{roundedClasses:B}=Vt(E),N=C(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),te=C(()=>({isActive:g.value,select:o,isSelected:l.value,isIndeterminate:u.value}));function ee(Y){var J;r("click",Y),!(c||!_.value)&&((J=a.navigate)==null||J.call(a,Y),e.value!=null&&o(!l.value,Y))}function se(Y){(Y.key==="Enter"||Y.key===" ")&&(Y.preventDefault(),ee(Y))}return pe(()=>{const Y=b.value?"a":e.tag,J=s.title||e.title,me=s.subtitle||e.subtitle,fe=!!(e.appendAvatar||e.appendIcon),Ee=!!(fe||s.append),Re=!!(e.prependAvatar||e.prependIcon),ze=!!(Re||s.prepend);return v==null||v.updateHasPrepend(ze),e.activeColor&&Zp("active-color",["color","base-color"]),Xe(m(Y,{class:["v-list-item",{"v-list-item--active":g.value,"v-list-item--disabled":e.disabled,"v-list-item--link":_.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!ze&&(v==null?void 0:v.hasPrepend.value),[`${e.activeClass}`]:e.activeClass&&g.value},P.value,O.value,T.value,A.value,M.value,N.value,B.value,S.value,e.class],style:[y.value,z.value,e.style],href:a.href.value,tabindex:_.value?v?-2:0:void 0,onClick:ee,onKeydown:_.value&&!b.value&&se},{default:()=>{var Mt;return[xs(_.value||g.value,"v-list-item"),ze&&m("div",{key:"prepend",class:"v-list-item__prepend"},[s.prepend?m(Ze,{key:"prepend-defaults",disabled:!Re,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>{var W;return[(W=s.prepend)==null?void 0:W.call(s,te.value)]}}):m(_e,null,[e.prependAvatar&&m(Zs,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&m(it,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),m("div",{class:"v-list-item__content","data-no-activator":""},[J&&m(g_,{key:"title"},{default:()=>{var W;return[((W=s.title)==null?void 0:W.call(s,{title:e.title}))??e.title]}}),me&&m(v_,{key:"subtitle"},{default:()=>{var W;return[((W=s.subtitle)==null?void 0:W.call(s,{subtitle:e.subtitle}))??e.subtitle]}}),(Mt=s.default)==null?void 0:Mt.call(s,te.value)]),Ee&&m("div",{key:"append",class:"v-list-item__append"},[s.append?m(Ze,{key:"append-defaults",disabled:!fe,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>{var W;return[(W=s.append)==null?void 0:W.call(s,te.value)]}}):m(_e,null,[e.appendIcon&&m(it,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&m(Zs,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])]}}),[[bn("ripple"),_.value&&e.ripple]])}),{}}}),p_=K({color:String,inset:Boolean,sticky:Boolean,title:String,...Se(),...Ye()},"VListSubheader"),b_=oe()({name:"VListSubheader",props:p_(),setup(e,t){let{slots:n}=t;const{textColorClasses:s,textColorStyles:r}=on(he(e,"color"));return pe(()=>{const a=!!(n.default||e.title);return m(e.tag,{class:["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},s.value,e.class],style:[{textColorStyles:r},e.style]},{default:()=>{var i;return[a&&m("div",{class:"v-list-subheader__text"},[((i=n.default)==null?void 0:i.call(n))??e.title])]}})}),{}}}),w_=K({items:Array},"VListChildren"),Hv=oe()({name:"VListChildren",props:w_(),setup(e,t){let{slots:n}=t;return Nv(),()=>{var s,r;return((s=n.default)==null?void 0:s.call(n))??((r=e.items)==null?void 0:r.map(a=>{var h,v;let{children:i,props:o,type:l,raw:u}=a;if(l==="divider")return((h=n.divider)==null?void 0:h.call(n,{props:o}))??m(pr,o,null);if(l==="subheader")return((v=n.subheader)==null?void 0:v.call(n,{props:o}))??m(b_,o,null);const c={subtitle:n.subtitle?g=>{var b;return(b=n.subtitle)==null?void 0:b.call(n,{...g,item:u})}:void 0,prepend:n.prepend?g=>{var b;return(b=n.prepend)==null?void 0:b.call(n,{...g,item:u})}:void 0,append:n.append?g=>{var b;return(b=n.append)==null?void 0:b.call(n,{...g,item:u})}:void 0,title:n.title?g=>{var b;return(b=n.title)==null?void 0:b.call(n,{...g,item:u})}:void 0},[d,f]=Ud.filterProps(o);return i?m(Ud,ge({value:o==null?void 0:o.value},d),{activator:g=>{let{props:b}=g;return n.header?n.header({props:{...o,...b}}):m(wi,ge(o,b),c)},default:()=>m(Hv,{items:i},n)}):n.item?n.item({props:o}):m(wi,o,c)}))}}}),Uv=K({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean},"list-items");function zv(e,t){const n=hn(t,e.itemTitle,t),s=e.returnObject?t:hn(t,e.itemValue,n),r=hn(t,e.itemChildren),a=e.itemProps===!0?typeof t=="object"&&t!=null&&!Array.isArray(t)?"children"in t?Xr(t,["children"])[1]:t:void 0:hn(t,e.itemProps),i={title:n,value:s,...a};return{title:String(i.title??""),value:i.value,props:i,children:Array.isArray(r)?jv(e,r):void 0,raw:t}}function jv(e,t){const n=[];for(const s of t)n.push(zv(e,s));return n}function S_(e){const t=C(()=>jv(e,e.items));return C_(t,n=>zv(e,n))}function C_(e,t){function n(r){return r.filter(a=>a!==null||e.value.some(i=>i.value===null)).map(a=>e.value.find(o=>Js(a,o.value))??t(a))}function s(r){return r.map(a=>{let{value:i}=a;return i})}return{items:e,transformIn:n,transformOut:s}}function x_(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"}function __(e,t){const n=hn(t,e.itemType,"item"),s=x_(t)?t:hn(t,e.itemTitle),r=hn(t,e.itemValue,void 0),a=hn(t,e.itemChildren),i=e.itemProps===!0?Xr(t,["children"])[1]:hn(t,e.itemProps),o={title:s,value:r,...i};return{type:n,title:o.title,value:o.value,props:o,children:n==="item"&&a?Wv(e,a):void 0,raw:t}}function Wv(e,t){const n=[];for(const s of t)n.push(__(e,s));return n}function E_(e){return{items:C(()=>Wv(e,e.items))}}const T_=K({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,lines:{type:[Boolean,String],default:"one"},nav:Boolean,...u_({selectStrategy:"single-leaf",openStrategy:"list"}),...Jn(),...Se(),...Ut(),...Qn(),...On(),itemType:{type:String,default:"type"},...Uv(),...At(),...Ye(),...We(),...un({variant:"text"})},"VList"),k_=oe()({name:"VList",props:T_(),emits:{"update:selected":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:select":e=>!0},setup(e,t){let{slots:n}=t;const{items:s}=E_(e),{themeClasses:r}=et(e),{backgroundColorClasses:a,backgroundColorStyles:i}=ws(he(e,"bgColor")),{borderClasses:o}=Xn(e),{densityClasses:l}=Xt(e),{dimensionStyles:u}=es(e),{elevationClasses:c}=In(e),{roundedClasses:d}=Vt(e),{open:f,select:h}=c_(e),v=C(()=>e.lines?`v-list--${e.lines}-line`:void 0),g=he(e,"activeColor"),b=he(e,"baseColor"),_=he(e,"color");Nv(),Yn({VListGroup:{activeColor:g,baseColor:b,color:_},VListItem:{activeClass:he(e,"activeClass"),activeColor:g,baseColor:b,color:_,density:he(e,"density"),disabled:he(e,"disabled"),lines:he(e,"lines"),nav:he(e,"nav"),variant:he(e,"variant")}});const E=Ce(!1),w=U();function k(S){E.value=!0}function P(S){E.value=!1}function O(S){var A;!E.value&&!(S.relatedTarget&&((A=w.value)!=null&&A.contains(S.relatedTarget)))&&y()}function T(S){if(w.value){if(S.key==="ArrowDown")y("next");else if(S.key==="ArrowUp")y("prev");else if(S.key==="Home")y("first");else if(S.key==="End")y("last");else return;S.preventDefault()}}function y(S){if(w.value)return ni(w.value,S)}return pe(()=>m(e.tag,{ref:w,class:["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav},r.value,a.value,o.value,l.value,c.value,v.value,d.value,e.class],style:[i.value,u.value,e.style],tabindex:e.disabled||E.value?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:k,onFocusout:P,onFocus:O,onKeydown:T},{default:()=>[m(Hv,{items:s.value},n)]})),{open:f,select:h,focus:y}}});const O_=K({id:String,...Qr(la({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:wu}}),["absolute"])},"VMenu"),I_=oe()({name:"VMenu",props:O_(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"modelValue"),{scopeId:r}=ia(),a=It(),i=C(()=>e.id||`v-menu-${a}`),o=U(),l=je(sl,null),u=Ce(0);_t(sl,{register(){++u.value},unregister(){--u.value},closeParents(){setTimeout(()=>{u.value||(s.value=!1,l==null||l.closeParents())},40)}}),we(s,v=>{v?l==null||l.register():l==null||l.unregister()});function c(){l==null||l.closeParents()}function d(v){var g,b;e.disabled||v.key==="Tab"&&(s.value=!1,(b=(g=o.value)==null?void 0:g.activatorEl)==null||b.focus())}function f(v){var b;if(e.disabled)return;const g=(b=o.value)==null?void 0:b.contentEl;g&&s.value?v.key==="ArrowDown"?(v.preventDefault(),ni(g,"next")):v.key==="ArrowUp"&&(v.preventDefault(),ni(g,"prev")):["ArrowDown","ArrowUp"].includes(v.key)&&(s.value=!0,v.preventDefault(),setTimeout(()=>setTimeout(()=>f(v))))}const h=C(()=>ge({"aria-haspopup":"menu","aria-expanded":String(s.value),"aria-owns":i.value,onKeydown:f},e.activatorProps));return pe(()=>{const[v]=Gn.filterProps(e);return m(Gn,ge({ref:o,class:["v-menu",e.class],style:e.style},v,{modelValue:s.value,"onUpdate:modelValue":g=>s.value=g,absolute:!0,activatorProps:h.value,"onClick:outside":c,onKeydown:d},r),{activator:n.activator,default:function(){for(var g=arguments.length,b=new Array(g),_=0;_<g;_++)b[_]=arguments[_];return m(Ze,{root:"VMenu"},{default:()=>{var E;return[(E=n.default)==null?void 0:E.call(n,...b)]}})}})}),Qs({id:i,ΨopenChildren:u},o)}});const A_=K({active:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...Se(),...oa({transition:{component:yv}})},"VCounter"),V_=oe()({name:"VCounter",functional:!0,props:A_(),setup(e,t){let{slots:n}=t;const s=C(()=>e.max?`${e.value} / ${e.max}`:String(e.value));return pe(()=>m(Hn,{transition:e.transition},{default:()=>[Xe(m("div",{class:["v-counter",e.class],style:e.style},[n.default?n.default({counter:s.value,max:e.max,value:e.value}):s.value]),[[Ys,e.active]])]})),{}}});const P_=K({floating:Boolean,...Se()},"VFieldLabel"),Pa=oe()({name:"VFieldLabel",props:P_(),setup(e,t){let{slots:n}=t;return pe(()=>m(uv,{class:["v-field-label",{"v-field-label--floating":e.floating},e.class],style:e.style,"aria-hidden":e.floating||void 0},n)),{}}}),M_=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],qv=K({appendInnerIcon:Fe,bgColor:String,clearable:Boolean,clearIcon:{type:Fe,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},error:Boolean,flat:Boolean,label:String,persistentClear:Boolean,prependInnerIcon:Fe,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>M_.includes(e)},"onClick:clear":an(),"onClick:appendInner":an(),"onClick:prependInner":an(),...Se(),...ru(),...At(),...We()},"VField"),Zv=oe()({name:"VField",inheritAttrs:!1,props:{id:String,...wv(),...qv()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:s,slots:r}=t;const{themeClasses:a}=et(e),{loaderClasses:i}=au(e),{focusClasses:o,isFocused:l,focus:u,blur:c}=Su(e),{InputIcon:d}=hv(e),{roundedClasses:f}=Vt(e),{rtlClasses:h}=ts(),v=C(()=>e.dirty||e.active),g=C(()=>!e.singleLine&&!!(e.label||r.label)),b=It(),_=C(()=>e.id||`input-${b}`),E=C(()=>`${_.value}-messages`),w=U(),k=U(),P=U(),O=C(()=>["plain","underlined"].includes(e.variant)),{backgroundColorClasses:T,backgroundColorStyles:y}=ws(he(e,"bgColor")),{textColorClasses:S,textColorStyles:A}=on(C(()=>e.error||e.disabled?void 0:v.value&&l.value?e.color:e.baseColor));we(v,B=>{if(g.value){const N=w.value.$el,te=k.value.$el;requestAnimationFrame(()=>{const ee=Gl(N),se=te.getBoundingClientRect(),Y=se.x-ee.x,J=se.y-ee.y-(ee.height/2-se.height/2),me=se.width/.75,fe=Math.abs(me-ee.width)>1?{maxWidth:de(me)}:void 0,Ee=getComputedStyle(N),Re=getComputedStyle(te),ze=parseFloat(Ee.transitionDuration)*1e3||150,Mt=parseFloat(Re.getPropertyValue("--v-field-label-scale")),W=Re.getPropertyValue("color");N.style.visibility="visible",te.style.visibility="hidden",Fs(N,{transform:`translate(${Y}px, ${J}px) scale(${Mt})`,color:W,...fe},{duration:ze,easing:ri,direction:B?"normal":"reverse"}).finished.then(()=>{N.style.removeProperty("visibility"),te.style.removeProperty("visibility")})})}},{flush:"post"});const z=C(()=>({isActive:v,isFocused:l,controlRef:P,blur:c,focus:u}));function M(B){B.target!==document.activeElement&&B.preventDefault()}return pe(()=>{var Y,J,me;const B=e.variant==="outlined",N=r["prepend-inner"]||e.prependInnerIcon,te=!!(e.clearable||r.clear),ee=!!(r["append-inner"]||e.appendInnerIcon||te),se=r.label?r.label({...z.value,label:e.label,props:{for:_.value}}):e.label;return m("div",ge({class:["v-field",{"v-field--active":v.value,"v-field--appended":ee,"v-field--center-affix":e.centerAffix??!O.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":N,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!se,[`v-field--variant-${e.variant}`]:!0},a.value,T.value,o.value,i.value,f.value,h.value,e.class],style:[y.value,A.value,e.style],onClick:M},n),[m("div",{class:"v-field__overlay"},null),m(_m,{name:"v-field",active:!!e.loading,color:e.error?"error":typeof e.loading=="string"?e.loading:e.color},{default:r.loader}),N&&m("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&m(d,{key:"prepend-icon",name:"prependInner"},null),(Y=r["prepend-inner"])==null?void 0:Y.call(r,z.value)]),m("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&g.value&&m(Pa,{key:"floating-label",ref:k,class:[S.value],floating:!0,for:_.value},{default:()=>[se]}),m(Pa,{ref:w,for:_.value},{default:()=>[se]}),(J=r.default)==null?void 0:J.call(r,{...z.value,props:{id:_.value,class:"v-field__input","aria-describedby":E.value},focus:u,blur:c})]),te&&m(bv,{key:"clear"},{default:()=>[Xe(m("div",{class:"v-field__clearable",onMousedown:fe=>{fe.preventDefault(),fe.stopPropagation()}},[r.clear?r.clear():m(d,{name:"clear"},null)]),[[Ys,e.dirty]])]}),ee&&m("div",{key:"append",class:"v-field__append-inner"},[(me=r["append-inner"])==null?void 0:me.call(r,z.value),e.appendInnerIcon&&m(d,{key:"append-icon",name:"appendInner"},null)]),m("div",{class:["v-field__outline",S.value]},[B&&m(_e,null,[m("div",{class:"v-field__outline__start"},null),g.value&&m("div",{class:"v-field__outline__notch"},[m(Pa,{ref:k,floating:!0,for:_.value},{default:()=>[se]})]),m("div",{class:"v-field__outline__end"},null)]),O.value&&g.value&&m(Pa,{ref:k,floating:!0,for:_.value},{default:()=>[se]})])])}),{controlRef:P}}});function D_(e){const t=Object.keys(Zv.props).filter(n=>!Zl(n)&&n!=="class"&&n!=="style");return Xr(e,t)}const N_=["color","file","time","date","datetime-local","week","month"],Gv=K({autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,suffix:String,type:{type:String,default:"text"},modelModifiers:Object,...Cu(),...qv()},"VTextField"),Ls=oe()({name:"VTextField",directives:{Intersect:lv},inheritAttrs:!1,props:Gv(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:s,slots:r}=t;const a=Ke(e,"modelValue"),{isFocused:i,focus:o,blur:l}=Su(e),u=C(()=>typeof e.counterValue=="function"?e.counterValue(a.value):(a.value??"").toString().length),c=C(()=>{if(n.maxlength)return n.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter}),d=C(()=>["plain","underlined"].includes(e.variant));function f(O,T){var y,S;!e.autofocus||!O||(S=(y=T[0].target)==null?void 0:y.focus)==null||S.call(y)}const h=U(),v=U(),g=U(),b=C(()=>N_.includes(e.type)||e.persistentPlaceholder||i.value||e.active);function _(){var O;g.value!==document.activeElement&&((O=g.value)==null||O.focus()),i.value||o()}function E(O){s("mousedown:control",O),O.target!==g.value&&(_(),O.preventDefault())}function w(O){_(),s("click:control",O)}function k(O){O.stopPropagation(),_(),bt(()=>{a.value=null,zp(e["onClick:clear"],O)})}function P(O){var y;const T=O.target;if(a.value=T.value,(y=e.modelModifiers)!=null&&y.trim&&["text","search","password","tel","url"].includes(e.type)){const S=[T.selectionStart,T.selectionEnd];bt(()=>{T.selectionStart=S[0],T.selectionEnd=S[1]})}}return pe(()=>{const O=!!(r.counter||e.counter||e.counterValue),T=!!(O||r.details),[y,S]=Wl(n),[{modelValue:A,...z}]=pi.filterProps(e),[M]=D_(e);return m(pi,ge({ref:h,modelValue:a.value,"onUpdate:modelValue":B=>a.value=B,class:["v-text-field",{"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-text-field--plain-underlined":["plain","underlined"].includes(e.variant)},e.class],style:e.style},y,z,{centerAffix:!d.value,focused:i.value}),{...r,default:B=>{let{id:N,isDisabled:te,isDirty:ee,isReadonly:se,isValid:Y}=B;return m(Zv,ge({ref:v,onMousedown:E,onClick:w,"onClick:clear":k,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:"textbox"},M,{id:N.value,active:b.value||ee.value,dirty:ee.value||e.dirty,disabled:te.value,focused:i.value,error:Y.value===!1}),{...r,default:J=>{let{props:{class:me,...fe}}=J;const Ee=Xe(m("input",ge({ref:g,value:a.value,onInput:P,autofocus:e.autofocus,readonly:se.value,disabled:te.value,name:e.name,placeholder:e.placeholder,size:1,type:e.type,onFocus:_,onBlur:l},fe,S),null),[[bn("intersect"),{handler:f},null,{once:!0}]]);return m(_e,null,[e.prefix&&m("span",{class:"v-text-field__prefix"},[m("span",{class:"v-text-field__prefix__text"},[e.prefix])]),m("div",{class:me,"data-no-activator":""},[r.default?m(_e,null,[r.default(),Ee]):wn(Ee)]),e.suffix&&m("span",{class:"v-text-field__suffix"},[m("span",{class:"v-text-field__suffix__text"},[e.suffix])])])}})},details:T?B=>{var N;return m(_e,null,[(N=r.details)==null?void 0:N.call(r,B),O&&m(_e,null,[m("span",null,null),m(V_,{active:e.persistentCounter||i.value,value:u.value,max:c.value},r.counter)])])}:void 0})}),Qs({},h,v,g)}});const F_=K({renderless:Boolean,...Se()},"VVirtualScrollItem"),L_=oe()({name:"VVirtualScrollItem",inheritAttrs:!1,props:F_(),emits:{"update:height":e=>!0},setup(e,t){let{attrs:n,emit:s,slots:r}=t;const{resizeRef:a,contentRect:i}=na(void 0,"border");we(()=>{var o;return(o=i.value)==null?void 0:o.height},o=>{o!=null&&s("update:height",o)}),pe(()=>{var o,l;return e.renderless?m(_e,null,[(o=r.default)==null?void 0:o.call(r,{itemRef:a})]):m("div",ge({ref:a,class:["v-virtual-scroll__item",e.class],style:e.style},n),[(l=r.default)==null?void 0:l.call(r)])})}}),zd=-1,jd=1,R_=K({itemHeight:{type:[Number,String],default:48}},"virtual");function $_(e,t,n){const s=Ce(0),r=Ce(e.itemHeight),a=C({get:()=>parseInt(r.value??0,10),set(T){r.value=T}}),i=U(),{resizeRef:o,contentRect:l}=na();_n(()=>{o.value=i.value});const u=Nm(),c=new Map;let d=Array.from({length:t.value.length});const f=C(()=>{const T=(!l.value||i.value===document.documentElement?u.height.value:l.value.height)-((n==null?void 0:n.value)??0);return Math.ceil(T/a.value*1.7+1)});function h(T,y){a.value=Math.max(a.value,y),d[T]=y,c.set(t.value[T],y)}function v(T){return d.slice(0,T).reduce((y,S)=>y+(S||a.value),0)}function g(T){const y=t.value.length;let S=0,A=0;for(;A<T&&S<y;)A+=d[S++]||a.value;return S-1}let b=0;function _(){if(!i.value||!l.value)return;const T=l.value.height-56,y=i.value.scrollTop,S=y<b?zd:jd,A=g(y+T/2),z=Math.round(f.value/3),M=A-z,B=s.value+z*2-1;S===zd&&A<=B?s.value=$r(M,0,t.value.length):S===jd&&A>=B&&(s.value=$r(M,0,t.value.length-f.value)),b=y}function E(T){if(!i.value)return;const y=v(T);i.value.scrollTop=y}const w=C(()=>Math.min(t.value.length,s.value+f.value)),k=C(()=>t.value.slice(s.value,w.value).map((T,y)=>({raw:T,index:y+s.value}))),P=C(()=>v(s.value)),O=C(()=>v(t.value.length)-v(w.value));return we(()=>t.value.length,()=>{d=jl(t.value.length).map(()=>a.value),c.forEach((T,y)=>{const S=t.value.indexOf(y);S===-1?c.delete(y):d[S]=T})}),{containerRef:i,computedItems:k,itemHeight:a,paddingTop:P,paddingBottom:O,scrollToIndex:E,handleScroll:_,handleItemResize:h}}const B_=K({items:{type:Array,default:()=>[]},renderless:Boolean,...R_(),...Se(),...Qn()},"VVirtualScroll"),H_=oe()({name:"VVirtualScroll",props:B_(),setup(e,t){let{slots:n}=t;const s=dt("VVirtualScroll"),{dimensionStyles:r}=es(e),{containerRef:a,handleScroll:i,handleItemResize:o,scrollToIndex:l,paddingTop:u,paddingBottom:c,computedItems:d}=$_(e,he(e,"items"));return Zn(()=>e.renderless,()=>{Tn(()=>{var f;a.value=hm(s.vnode.el,!0),(f=a.value)==null||f.addEventListener("scroll",i)}),pt(()=>{var f;(f=a.value)==null||f.removeEventListener("scroll",i)})}),pe(()=>{const f=d.value.map(h=>m(L_,{key:h.index,renderless:e.renderless,"onUpdate:height":v=>o(h.index,v)},{default:v=>{var g;return(g=n.default)==null?void 0:g.call(n,{item:h.raw,index:h.index,...v})}}));return e.renderless?m(_e,null,[m("div",{class:"v-virtual-scroll__spacer",style:{paddingTop:de(u.value)}},null),f,m("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:de(c.value)}},null)]):m("div",{ref:a,class:["v-virtual-scroll",e.class],onScroll:i,style:[r.value,e.style]},[m("div",{class:"v-virtual-scroll__container",style:{paddingTop:de(u.value),paddingBottom:de(c.value)}},[f])])}),{scrollToIndex:l}}});function U_(e,t){const n=Ce(!1);let s;function r(o){cancelAnimationFrame(s),n.value=!0,s=requestAnimationFrame(()=>{s=requestAnimationFrame(()=>{n.value=!1})})}async function a(){await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>{if(n.value){const l=we(n,()=>{l(),o()})}else o()})}async function i(o){var c,d;if(o.key==="Tab"&&((c=t.value)==null||c.focus()),!["PageDown","PageUp","Home","End"].includes(o.key))return;const l=(d=e.value)==null?void 0:d.$el;if(!l)return;(o.key==="Home"||o.key==="End")&&l.scrollTo({top:o.key==="Home"?0:l.scrollHeight,behavior:"smooth"}),await a();const u=l.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)");if(o.key==="PageDown"||o.key==="Home"){const f=l.getBoundingClientRect().top;for(const h of u)if(h.getBoundingClientRect().top>=f){h.focus();break}}else{const f=l.getBoundingClientRect().bottom;for(const h of[...u].reverse())if(h.getBoundingClientRect().bottom<=f){h.focus();break}}}return{onListScroll:r,onListKeydown:i}}const z_=K({chips:Boolean,closableChips:Boolean,eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,menu:Boolean,menuIcon:{type:Fe,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,valueComparator:{type:Function,default:Js},...Uv({itemChildren:!1})},"Select"),j_=K({...z_(),...Qr(Gv({modelValue:null}),["validationValue","dirty","appendInnerIcon"]),...oa({transition:{component:wu}})},"VSelect"),Wd=oe()({name:"VSelect",props:j_(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,t){let{slots:n}=t;const{t:s}=Ri(),r=U(),a=U(),i=Ke(e,"menu"),o=C({get:()=>i.value,set:N=>{var te;i.value&&!N&&((te=a.value)!=null&&te.ΨopenChildren)||(i.value=N)}}),{items:l,transformIn:u,transformOut:c}=S_(e),d=Ke(e,"modelValue",[],N=>u(N===null?[null]:Wn(N)),N=>{const te=c(N);return e.multiple?te:te[0]??null}),f=Sv(),h=C(()=>d.value.map(N=>l.value.find(te=>e.valueComparator(te.value,N.value))||N)),v=C(()=>h.value.map(N=>N.props.value)),g=Ce(!1);let b="",_;const E=C(()=>e.hideSelected?l.value.filter(N=>!h.value.some(te=>te===N)):l.value),w=C(()=>e.hideNoData&&!l.value.length||e.readonly||(f==null?void 0:f.isReadonly.value)),k=U(),{onListScroll:P,onListKeydown:O}=U_(k,r);function T(N){e.openOnClear&&(o.value=!0)}function y(){w.value||(o.value=!o.value)}function S(N){var J,me;if(e.readonly||f!=null&&f.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(N.key)&&N.preventDefault(),["Enter","ArrowDown"," "].includes(N.key)&&(o.value=!0),["Escape","Tab"].includes(N.key)&&(o.value=!1),N.key==="Home"?(J=k.value)==null||J.focus("first"):N.key==="End"&&((me=k.value)==null||me.focus("last"));const te=1e3;function ee(fe){const Ee=fe.key.length===1,Re=!fe.ctrlKey&&!fe.metaKey&&!fe.altKey;return Ee&&Re}if(e.multiple||!ee(N))return;const se=performance.now();se-_>te&&(b=""),b+=N.key.toLowerCase(),_=se;const Y=l.value.find(fe=>fe.title.toLowerCase().startsWith(b));Y!==void 0&&(d.value=[Y])}function A(N){if(e.multiple){const te=v.value.findIndex(ee=>e.valueComparator(ee,N.value));if(te===-1)d.value=[...d.value,N];else{const ee=[...d.value];ee.splice(te,1),d.value=ee}}else d.value=[N],o.value=!1}function z(N){var te;(te=k.value)!=null&&te.$el.contains(N.relatedTarget)||(o.value=!1)}function M(){var N;g.value&&((N=r.value)==null||N.focus())}function B(N){g.value=!0}return pe(()=>{const N=!!(e.chips||n.chip),te=!!(!e.hideNoData||E.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),ee=d.value.length>0,[se]=Ls.filterProps(e),Y=ee||!g.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return m(Ls,ge({ref:r},se,{modelValue:d.value.map(J=>J.props.value).join(", "),"onUpdate:modelValue":J=>{J==null&&(d.value=[])},focused:g.value,"onUpdate:focused":J=>g.value=J,validationValue:d.externalValue,dirty:ee,class:["v-select",{"v-select--active-menu":o.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":d.value.length,"v-select--selection-slot":!!n.selection},e.class],style:e.style,readonly:!0,placeholder:Y,"onClick:clear":T,"onMousedown:control":y,onBlur:z,onKeydown:S}),{...n,default:()=>m(_e,null,[m(I_,ge({ref:a,modelValue:o.value,"onUpdate:modelValue":J=>o.value=J,activator:"parent",contentClass:"v-select__content",disabled:w.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterLeave:M},e.menuProps),{default:()=>[te&&m(k_,{ref:k,selected:v.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:J=>J.preventDefault(),onKeydown:O,onFocusin:B,onScrollPassive:P,tabindex:"-1"},{default:()=>{var J,me,fe;return[(J=n["prepend-item"])==null?void 0:J.call(n),!E.value.length&&!e.hideNoData&&(((me=n["no-data"])==null?void 0:me.call(n))??m(wi,{title:s(e.noDataText)},null)),m(H_,{renderless:!0,items:E.value},{default:Ee=>{var Z;let{item:Re,index:ze,itemRef:Mt}=Ee;const W=ge(Re.props,{ref:Mt,key:ze,onClick:()=>A(Re)});return((Z=n.item)==null?void 0:Z.call(n,{item:Re,index:ze,props:W}))??m(wi,W,{prepend:$=>{let{isSelected:ae}=$;return m(_e,null,[e.multiple&&!e.hideSelected?m(pl,{key:Re.value,modelValue:ae,ripple:!1,tabindex:"-1"},null):void 0,Re.props.prependIcon&&m(it,{icon:Re.props.prependIcon},null)])}})}}),(fe=n["append-item"])==null?void 0:fe.call(n)]}})]}),h.value.map((J,me)=>{var Re;function fe(ze){ze.stopPropagation(),ze.preventDefault(),A(J)}const Ee={"onClick:close":fe,onMousedown(ze){ze.preventDefault(),ze.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0};return m("div",{key:J.value,class:"v-select__selection"},[N?n.chip?m(Ze,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:J.title}}},{default:()=>{var ze;return[(ze=n.chip)==null?void 0:ze.call(n,{item:J,index:me,props:Ee})]}}):m(s_,ge({key:"chip",closable:e.closableChips,size:"small",text:J.title},Ee),null):((Re=n.selection)==null?void 0:Re.call(n,{item:J,index:me}))??m("span",{class:"v-select__selection-text"},[J.title,e.multiple&&me<h.value.length-1&&m("span",{class:"v-select__selection-comma"},[Pe(",")])])])})]),"append-inner":function(){var Ee;for(var J=arguments.length,me=new Array(J),fe=0;fe<J;fe++)me[fe]=arguments[fe];return m(_e,null,[(Ee=n["append-inner"])==null?void 0:Ee.call(n,...me),e.menuIcon?m(it,{class:"v-select__menu-icon",icon:e.menuIcon},null):void 0])}})}),Qs({isFocused:g,menu:o,select:A},r)}});const W_=K({id:String,text:String,...Qr(la({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:!1}),["absolute","persistent"])},"VTooltip"),kr=oe()({name:"VTooltip",props:W_(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const s=Ke(e,"modelValue"),{scopeId:r}=ia(),a=It(),i=C(()=>e.id||`v-tooltip-${a}`),o=U(),l=C(()=>e.location.split(" ").length>1?e.location:e.location+" center"),u=C(()=>e.origin==="auto"||e.origin==="overlap"||e.origin.split(" ").length>1||e.location.split(" ").length>1?e.origin:e.origin+" center"),c=C(()=>e.transition?e.transition:s.value?"scale-transition":"fade-transition"),d=C(()=>ge({"aria-describedby":i.value},e.activatorProps));return pe(()=>{const[f]=Gn.filterProps(e);return m(Gn,ge({ref:o,class:["v-tooltip",e.class],style:e.style,id:i.value},f,{modelValue:s.value,"onUpdate:modelValue":h=>s.value=h,transition:c.value,absolute:!0,location:l.value,origin:u.value,persistent:!0,role:"tooltip",activatorProps:d.value,_disableGlobalStack:!0},r),{activator:n.activator,default:function(){var b;for(var h=arguments.length,v=new Array(h),g=0;g<h;g++)v[g]=arguments[g];return((b=n.default)==null?void 0:b.call(n,...v))??e.text}})}),Qs({},o)}}),q_={class:"p-3"},Z_=I("thead",null,[I("tr",{class:"custom-thead custom-font-size"},[I("th",{class:"text-left"},"-"),I("th",{class:"text-left"},"N° BHE"),I("th",{class:"text-left"},"N° NC"),I("th",{class:"text-left"},"N° OT"),I("th",{class:"text-left"},"FECHA"),I("th",{class:"text-left"},"GLOSA"),I("th",{class:"text-left"},"HONORARIOS"),I("th",{class:"text-left"},"RET 2° CAT."),I("th",{class:"text-left"},"TOTAL"),I("th",{class:"text-left"},"USUARIO"),I("th",{class:"text-left"},"CLIENTE"),I("th",{class:"text-left"},"RUT"),I("th",{class:"text-left"},"F. PAGO"),I("th",{class:"text-left"},"#")])],-1),G_={key:0,style:{"min-width":"250px"}},K_=I("br",null,null,-1),Y_=I("br",null,null,-1),J_={key:1},X_="#f46a6a7a",Q_={__name:"Reporte",setup(e){const{success:t,error:n,warning:s,hide:r}=Fi(),a=U(null),i=U([]),o=U([]),l=U([]),u=U(null),c=U(!1),d=O=>O.forma_pago==="Por Pagar"?{backgroundColor:X_}:{},f=ct({fecha_i:re.now().toFormat("yyyy-MM-dd"),fecha_f:re.now().toFormat("yyyy-MM-dd"),num_bhe:null,rut_receptor:null,usuario_search:null,cliente:null,anuladas:!1,pendiente_pago:!1,ot:null,nc:null,pdf:!0}),h=async()=>{if(!vs("bhe_buscar")){n({msg:hs});return}c.value=!0;const T={accion:"reporteBHE",form:{...f,fecha_i:P(f.fecha_i,"back"),fecha_f:P(f.fecha_f,"back")}};var y=await gt.post("/caja_v2/api/",T).catch(A=>!1);if(y){var S=y.data;console.log(S),S.estado?(u.value=S.pdf_reporte,i.value=S.data):(i.value=[],s({msg:S.mensaje}))}else s({msg:"Ocurrio un error, Intentelo nuevamente"});c.value=!1},v=async()=>{const O={accion:"ListClientes",form:f.value};var T=await gt.post("/caja_v2/api/",O).catch(S=>!1);if(T){var y=T.data;y.estado?o.value=y.data:s({msg:y.mensaje})}else s({msg:"Ocurrio un error, Intentelo nuevamente"})},g=async()=>{const O={accion:"ListUsuarios"};var T=await gt.post("/caja_v2/api/",O).catch(S=>!1);if(T){var y=T.data;y.estado?l.value=y.data:s({msg:y.mensaje})}else s({msg:"Ocurrio un error, Intentelo nuevamente"})},b=async O=>{if(!vs("bhe_eliminar")){n({msg:hs});return}try{if(await a.value.show({title:"Eliminar BHE",message:"Estás seguro de eliminar definitivamente esta Boleta de Honorarios (BHE) ?",okButton:"Eliminar"})){const A={accion:"anularBHE",boleta:O};var T=await gt.post("/caja_v2/api/",A).catch(z=>!1);if(T){var y=T.data;y.estado?t({msg:y.mensaje}):s({msg:y.mensaje}),h()}else s({msg:"Ocurrio un error, Intentelo nuevamente"})}}catch(S){console.error("Error:",S)}},_=async O=>{if(!vs("bhe_visualizar")){n({msg:hs});return}const T={accion:"traeBHE",bhe:O};var y=await gt.post("/ot/api/",T).catch(A=>!1);if(y){var S=y.data;S.estado?w(S.data.pdf64):s({msg:S.mensaje})}else s({msg:"Ocurrio un error, Intentelo nuevamente"})},E=()=>{if(!vs("bhe_generar_informe")){n({msg:hs});return}const O=u.value;w(O)},w=O=>{var T=window.open();T.document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64,"+O+"'></iframe>"),T.document.close()},k=()=>{f.fecha_i=re.now().toFormat("yyyy-MM-dd"),f.fecha_f=re.now().toFormat("yyyy-MM-dd"),f.num_bhe=null,f.rut_receptor=null,f.usuario_search=null,f.cliente=null,f.anuladas=!1,f.pendiente_pago=!1,f.ot=null,f.nc=null};console.log("hoy =>",re.now().toFormat("yyyy-MM-dd"));const P=(O,T)=>{if(T=="back")var y=re.fromFormat(O,"yyyy-MM-dd").toFormat("dd-MM-yyyy");if(T=="front")var y=re.fromFormat(O,"yyyy-MM-dd").toFormat("yyyy-MM-dd");return y??null};return v(),g(),(O,T)=>(Ae(),ut(Wr,{class:"fill-height"},{default:F(()=>[m(Un,{class:"mx-auto pa-3",flat:""},{default:F(()=>[m(lt,null,{default:F(()=>[m(Ne,{cols:"12"},{default:F(()=>[m(lt,null,{default:F(()=>[m(Ne,{cols:"12",md:"2"},{default:F(()=>[m(Ls,{modelValue:f.fecha_i,"onUpdate:modelValue":T[0]||(T[0]=y=>f.fecha_i=y),"hide-details":"",variant:"outlined",label:"DESDE",required:"",type:"date",class:"custom-field-input"},null,8,["modelValue"])]),_:1}),m(Ne,{cols:"12",md:"2"},{default:F(()=>[m(Ls,{modelValue:f.fecha_f,"onUpdate:modelValue":T[1]||(T[1]=y=>f.fecha_f=y),label:"HASTA","hide-details":"",variant:"outlined",required:"",type:"date"},null,8,["modelValue"])]),_:1}),m(Ne,{cols:"12",md:"2"},{default:F(()=>[m(Ls,{modelValue:f.num_bhe,"onUpdate:modelValue":T[2]||(T[2]=y=>f.num_bhe=y),label:"N° BHE","hide-details":"",variant:"outlined",required:""},null,8,["modelValue"])]),_:1}),m(Ne,{cols:"12",md:"2"},{default:F(()=>[m(Ls,{modelValue:f.rut_receptor,"onUpdate:modelValue":T[3]||(T[3]=y=>f.rut_receptor=y),onInput:T[4]||(T[4]=y=>f.rut_receptor=rt(Ds)(y.target.value)),label:"RUT","hide-details":"",variant:"outlined",required:""},null,8,["modelValue"])]),_:1}),m(Ne,{cols:"12",md:"2"},{default:F(()=>[m(Wd,{modelValue:f.usuario_search,"onUpdate:modelValue":T[5]||(T[5]=y=>f.usuario_search=y),label:"USUARIO","hide-details":"",variant:"outlined","item-value":"nombre","item-title":"nombre",items:l.value},null,8,["modelValue","items"])]),_:1}),m(Ne,{cols:"12",md:"2"},{default:F(()=>[m(Wd,{modelValue:f.cliente,"onUpdate:modelValue":T[6]||(T[6]=y=>f.cliente=y),label:"CLIENTE","hide-details":"",variant:"outlined","item-value":"nombre","item-title":"nombre",items:o.value},null,8,["modelValue","items"])]),_:1})]),_:1}),m(lt,{class:"d-flex justify-end mr-2"},{default:F(()=>[m(Ne,{cols:"auto py-0"},{default:F(()=>[m(bl,{label:"ANULADAS",class:"mt-n1 pt-n1",modelValue:f.anuladas,"onUpdate:modelValue":T[7]||(T[7]=y=>f.anuladas=y),density:"compact","hide-details":""},null,8,["modelValue"])]),_:1}),m(Ne,{cols:"auto py-0"},{default:F(()=>[m(bl,{label:"PENDIENTE PAGO",class:"mt-n1 pt-n1",modelValue:f.pendiente_pago,"onUpdate:modelValue":T[8]||(T[8]=y=>f.pendiente_pago=y),density:"compact","hide-details":""},null,8,["modelValue"])]),_:1}),m(kr,{text:"Limpiar filtros",location:"top"},{activator:F(({props:y})=>[m(Be,ge({size:"small","prepend-icon":"mdi-delete",variant:"text",color:"primary",onClick:T[9]||(T[9]=S=>k())},y,{class:"mx-2"}),{default:F(()=>[Pe("limpiar filtros")]),_:2},1040)]),_:1}),m(Be,{size:"small","prepend-icon":"mdi-magnify",color:"primary",onClick:T[10]||(T[10]=y=>h()),class:"mx-2",loading:c.value},{default:F(()=>[Pe("Buscar")]),_:1},8,["loading"])]),_:1}),m(lt,null,{default:F(()=>[m(e_,{type:"success",class:"mx-3",closable:"",title:"Importante",density:"compact",text:"Por disposición del Servicio de Impuestos Internos (SII), la anulación de boletas de honorarios electrónicas (BHE) debe ser realizada desde su portal en https://www.sii.cl "})]),_:1})]),_:1}),m(Ne,{cols:"12"},{default:F(()=>[m(Be,{"prepend-icon":"mdi-download",disabled:!u.value,size:"small",onClick:E,class:"my-2"},{default:F(()=>[Pe("descargar pdf")]),_:1},8,["disabled"]),I("h5",q_,ie(i.value.length??0)+" registros encontrados",1),m(bi,{density:"compact",height:"300px"},{default:F(()=>[Z_,I("tbody",null,[(Ae(!0),St(_e,null,Ns(i.value,(y,S)=>(Ae(),St("tr",{key:y.id,class:"custom-font-size",style:Gr(d(y))},[I("td",null,[m(kr,{text:"Ver BHE",location:"top"},{activator:F(({props:A})=>[m(Be,ge({size:"",variant:"text",color:"error",onClick:z=>_(y.boleta)},A),{default:F(()=>[m(it,null,{default:F(()=>[Pe("mdi-file-pdf-box")]),_:1})]),_:2},1040,["onClick"])]),_:2},1024)]),y.anulada=="1"?(Ae(),St("td",G_,[I("div",null,[Pe(" Nro Boleta:"+ie(y.boleta),1),K_,Pe(" Fecha anulación: "+ie(O.$filters.fecha(y.fecha_anulacion))+" ",1),Y_,Pe(" Usuario anulación: "+ie(y.usuario_anulacion),1)])])):(Ae(),St("td",J_,ie(y.boleta),1)),I("td",null,ie(y.nc),1),I("td",null,ie(y.ot),1),I("td",null,ie(y.fecha),1),I("td",null,ie(y.glosa),1),I("td",null,ie(O.$filters.moneda(y.total)),1),I("td",null,ie(y.id),1),I("td",null,ie(O.$filters.moneda(y.total)),1),I("td",null,ie(y.usuario),1),I("td",null,ie(y.nombre_cliente),1),I("td",null,ie(y.rut_receptor),1),I("td",null,ie(y.forma_pago),1),I("td",null,[y.anulada!="1"?(Ae(),ut(kr,{key:0,text:"Eliminar BHE",location:"top"},{activator:F(({props:A})=>[m(Be,ge({size:"x-small",color:"error",icon:"mdi-delete-outline",onClick:z=>b(y.boleta)},A),null,16,["onClick"])]),_:2},1024)):tn("",!0)])],4))),128))])]),_:1})]),_:1})]),_:1}),m(Mv,{ref_key:"confirmDialogueRef",ref:a},null,512)]),_:1})]),_:1}))}};const eE=I("thead",null,[I("tr",{class:"custom-thead custom-font-size"},[I("th",{class:"text-left"},"-"),I("th",{class:"text-left"},"N° BHE"),I("th",{class:"text-left"},"N° NC"),I("th",{class:"text-left"},"N° OT"),I("th",{class:"text-left"},"FECHA"),I("th",{class:"text-left"},"GLOSA"),I("th",{class:"text-left"},"HONORARIOS"),I("th",{class:"text-left"},"RET 2° CAT."),I("th",{class:"text-left"},"TOTAL"),I("th",{class:"text-left"},"USUARIO"),I("th",{class:"text-left"},"CLIENTE"),I("th",{class:"text-left"},"RUT"),I("th",{class:"text-left"},"F. PAGO"),I("th",{class:"text-left"},"#")])],-1),tE="#f46a6a7a",nE={__name:"ReporteDiario",setup(e){const{success:t,error:n,warning:s,hide:r}=Fi(),a=U(null),i=U([]);U([]),U([]),U(null);const o=U(!1),l=U(0),u=U(0),c=_=>_.forma_pago==="Por Pagar"?{backgroundColor:tE}:{},d=ct({fecha_i:re.now().toFormat("yyyy-MM-dd"),fecha_f:re.now().toFormat("yyyy-MM-dd"),num_bhe:null,rut_receptor:null,usuario_search:null,cliente:null,anuladas:!1,ot:null,nc:null,pdf:!1}),f=async()=>{if(!vs("bhe_informe_diario")){n({msg:hs});return}o.value=!0;const E={accion:"reporteBHErt",form:{...d,fecha_i:b(d.fecha_i,"back"),fecha_f:b(d.fecha_f,"back")}};var w=await gt.post("/caja_v2/api/",E).catch(P=>!1);if(w){var k=w.data;if(console.log(k),k.estado){const P=k.data.reduce((O,T)=>O+parseInt(T.total),0);l.value=P,u.value=k.data.length,i.value=k.data}else i.value=[],s({msg:k.mensaje})}else s({msg:"Ocurrio un error, Intentelo nuevamente"});o.value=!1},h=async _=>{try{if(await a.value.show({title:"Eliminar BHE",message:"Estás seguro de eliminar definitivamente esta Boleta de Honorarios (BHE) ?",okButton:"Eliminar"})){const P={accion:"anularBHE",boleta:_};var E=await gt.post("/caja_v2/api/",P).catch(O=>!1);if(E){var w=E.data;w.estado?t({msg:w.mensaje}):s({msg:w.mensaje}),f()}else s({msg:"Ocurrio un error, Intentelo nuevamente"})}}catch(k){console.error("Error:",k)}},v=async _=>{if(!vs("bhe_visualizar")){n({msg:hs});return}const E={accion:"traeBHE",bhe:_};var w=await gt.post("/ot/api/",E).catch(P=>!1);if(w){var k=w.data;k.estado?g(k.data.pdf64):s({msg:k.mensaje})}else s({msg:"Ocurrio un error, Intentelo nuevamente"})},g=_=>{var E=window.open();E.document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64,"+_+"'></iframe>"),E.document.close()},b=(_,E)=>{if(E=="back")var w=re.fromFormat(_,"yyyy-MM-dd").toFormat("dd-MM-yyyy");if(E=="front")var w=re.fromFormat(_,"yyyy-MM-dd").toFormat("yyyy-MM-dd");return w??null};return setInterval(()=>{f()},3e4),f(),(_,E)=>(Ae(),ut(Wr,{class:"fill-height"},{default:F(()=>[m(Un,{class:"mx-auto pa-3",flat:""},{default:F(()=>[m(lt,null,{default:F(()=>[m(Ne,{cols:"12"},{default:F(()=>[Pe(" Total Registros :"+ie(u.value)+" - Total Valor: $"+ie(_.$filters.moneda(l.value))+" ",1),m(bi,{density:"compact",height:"300px"},{default:F(()=>[eE,I("tbody",null,[(Ae(!0),St(_e,null,Ns(i.value,(w,k)=>(Ae(),St("tr",{key:w.id,class:"custom-font-size",style:Gr(c(w))},[I("td",null,[m(kr,{text:"Ver BHE",location:"top"},{activator:F(({props:P})=>[m(Be,ge({size:"",variant:"text",color:"error",onClick:O=>v(w.boleta)},P),{default:F(()=>[m(it,null,{default:F(()=>[Pe("mdi-file-pdf-box")]),_:1})]),_:2},1040,["onClick"])]),_:2},1024)]),I("td",null,ie(w.boleta),1),I("td",null,ie(w.nc),1),I("td",null,ie(w.ot),1),I("td",null,ie(w.fecha),1),I("td",null,ie(w.glosa),1),I("td",null,ie(_.$filters.moneda(w.total)),1),I("td",null,ie(w.id),1),I("td",null,ie(_.$filters.moneda(w.total)),1),I("td",null,ie(w.usuario),1),I("td",null,ie(w.nombre_cliente),1),I("td",null,ie(w.rut_receptor),1),I("td",null,ie(w.forma_pago),1),I("td",null,[m(kr,{text:"Eliminar BHE",location:"top"},{activator:F(({props:P})=>[m(Be,ge({size:"x-small",color:"error",icon:"mdi-delete-outline",onClick:O=>h(w.boleta)},P),null,16,["onClick"])]),_:2},1024)])],4))),128))])]),_:1})]),_:1})]),_:1}),m(Mv,{ref_key:"confirmDialogueRef",ref:a},null,512)]),_:1})]),_:1}))}};const sE=K({text:String,...Se(),...Ye()},"VToolbarTitle"),Kv=oe()({name:"VToolbarTitle",props:sE(),setup(e,t){let{slots:n}=t;return pe(()=>{const s=!!(n.default||n.text||e.text);return m(e.tag,{class:["v-toolbar-title",e.class],style:e.style},{default:()=>{var r;return[s&&m("div",{class:"v-toolbar-title__placeholder"},[n.text?n.text():e.text,(r=n.default)==null?void 0:r.call(n)])]}})}),{}}}),rE=[null,"prominent","default","comfortable","compact"],aE=K({absolute:Boolean,collapse:Boolean,color:String,density:{type:String,default:"default",validator:e=>rE.includes(e)},extended:Boolean,extensionHeight:{type:[Number,String],default:48},flat:Boolean,floating:Boolean,height:{type:[Number,String],default:64},image:String,title:String,...Jn(),...Se(),...On(),...At(),...Ye({tag:"header"}),...We()},"VToolbar"),iE=oe()({name:"VToolbar",props:aE(),setup(e,t){var h;let{slots:n}=t;const{backgroundColorClasses:s,backgroundColorStyles:r}=ws(he(e,"color")),{borderClasses:a}=Xn(e),{elevationClasses:i}=In(e),{roundedClasses:o}=Vt(e),{themeClasses:l}=et(e),{rtlClasses:u}=ts(),c=Ce(!!(e.extended||(h=n.extension)!=null&&h.call(n))),d=C(()=>parseInt(Number(e.height)+(e.density==="prominent"?Number(e.height):0)-(e.density==="comfortable"?8:0)-(e.density==="compact"?16:0),10)),f=C(()=>c.value?parseInt(Number(e.extensionHeight)+(e.density==="prominent"?Number(e.extensionHeight):0)-(e.density==="comfortable"?4:0)-(e.density==="compact"?8:0),10):0);return Yn({VBtn:{variant:"text"}}),pe(()=>{var _;const v=!!(e.title||n.title),g=!!(n.image||e.image),b=(_=n.extension)==null?void 0:_.call(n);return c.value=!!(e.extended||b),m(e.tag,{class:["v-toolbar",{"v-toolbar--absolute":e.absolute,"v-toolbar--collapse":e.collapse,"v-toolbar--flat":e.flat,"v-toolbar--floating":e.floating,[`v-toolbar--density-${e.density}`]:!0},s.value,a.value,i.value,o.value,l.value,u.value,e.class],style:[r.value,e.style]},{default:()=>[g&&m("div",{key:"image",class:"v-toolbar__image"},[n.image?m(Ze,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},n.image):m(no,{key:"image-img",cover:!0,src:e.image},null)]),m(Ze,{defaults:{VTabs:{height:de(d.value)}}},{default:()=>{var E,w,k;return[m("div",{class:"v-toolbar__content",style:{height:de(d.value)}},[n.prepend&&m("div",{class:"v-toolbar__prepend"},[(E=n.prepend)==null?void 0:E.call(n)]),v&&m(Kv,{key:"title",text:e.title},{text:n.title}),(w=n.default)==null?void 0:w.call(n),n.append&&m("div",{class:"v-toolbar__append"},[(k=n.append)==null?void 0:k.call(n)])])]}}),m(Ze,{defaults:{VTabs:{height:de(f.value)}}},{default:()=>[m(pv,null,{default:()=>[c.value&&m("div",{class:"v-toolbar__extension",style:{height:de(f.value)}},[b])]})]})]})}),{contentHeight:d,extensionHeight:f}}}),oE={__name:"Layout",setup(e){const t=U("caja"),n=s=>{t.value=s};return(s,r)=>(Ae(),ut(Un,{color:"grey-lighten-4",flat:"",rounded:"0"},{default:F(()=>[m(iE,{density:"compact"},{default:F(()=>[m(Kv,null,{default:F(()=>[Pe("Módulo Boleta de Honorarios Electrónica")]),_:1}),m(Tr),t.value=="caja"?(Ae(),ut(Be,{key:0,"prepend-icon":"mdi-file-chart-outline",color:"primary",variant:"text",onClick:r[0]||(r[0]=a=>n("reporte_diario"))},{default:F(()=>[Pe("Informe diario BHE ")]),_:1})):tn("",!0),t.value=="caja"?(Ae(),ut(Be,{key:1,"prepend-icon":"mdi-file-chart-outline",color:"primary",variant:"text",onClick:r[1]||(r[1]=a=>n("reporte"))},{default:F(()=>[Pe("Consultar BHE emitidas ")]),_:1})):tn("",!0),t.value=="reporte"||t.value=="reporte_diario"?(Ae(),ut(Be,{key:2,"prepend-icon":"mdi-keyboard-return",color:"primary",variant:"text",onClick:r[2]||(r[2]=a=>n("caja"))},{default:F(()=>[Pe("ir a Caja ")]),_:1})):tn("",!0)]),_:1}),t.value=="caja"?(Ae(),ut(Yx,{key:0})):tn("",!0),t.value=="reporte"?(Ae(),ut(Q_,{key:1})):tn("",!0),t.value=="reporte_diario"?(Ae(),ut(nE,{key:2})):tn("",!0)]),_:1}))}};const lE=K({...Se(),...TC({fullHeight:!0}),...We()},"VApp"),uE=oe()({name:"VApp",props:lE(),setup(e,t){let{slots:n}=t;const s=et(e),{layoutClasses:r,layoutStyles:a,getLayoutItem:i,items:o,layoutRef:l}=AC(e),{rtlClasses:u}=ts();return pe(()=>{var c;return m("div",{ref:l,class:["v-application",s.themeClasses.value,r.value,u.value,e.class],style:[a.value,e.style]},[m("div",{class:"v-application__wrap"},[(c=n.default)==null?void 0:c.call(n)])])}),{getLayoutItem:i,items:o,theme:s}}}),cE={__name:"App",setup(e){return(t,n)=>(Ae(),ut(uE,{id:"inspire"},{default:F(()=>[m(oE),m(D0)]),_:1}))}},dE="modulepreload",fE=function(e,t){return new URL(e,t).href},qd={},mE=function(t,n,s){if(!n||n.length===0)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map(a=>{if(a=fE(a,s),a in qd)return;qd[a]=!0;const i=a.endsWith(".css"),o=i?'[rel="stylesheet"]':"";if(!!s)for(let c=r.length-1;c>=0;c--){const d=r[c];if(d.href===a&&(!i||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${o}`))return;const u=document.createElement("link");if(u.rel=i?"stylesheet":dE,i||(u.as="script",u.crossOrigin=""),u.href=a,document.head.appendChild(u),i)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${a}`)))})})).then(()=>t()).catch(a=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=a,window.dispatchEvent(i),!i.defaultPrevented)throw a})};async function hE(){(await mE(()=>import("./webfontloader-523643f5.js").then(t=>t.w),[],import.meta.url)).load({google:{families:["Roboto:100,300,400,500,700,900&display=swap"]}})}const vE=av({theme:{themes:{light:{colors:{primary:"#1867C0",secondary:"#5CBBF6"}}}},display:{mobileBreakpoint:"sm",thresholds:{xs:0,sm:340,md:540,lg:800,xl:1280}}}),gE=Mp();function yE(e){hE(),e.use(vE).use(gE)}const Tu=Ap(cE);Je.defaultLocale="es-ES";Tu.config.globalProperties.$filters={mayuscula(e){return e&&e.length>0&&typeof e=="string"?e.toUpperCase():e},moneda(e){return e==null?0:Intl.NumberFormat("de-DE").format(e)??0},fechaLarga(){if(!value)return"";var e=re.fromFormat(value,"dd-MM-yyyy HH:mm:ss").toFormat("d LLL yyyy HH:mm");return e=="Invalid DateTime"?value:e},fecha(e){if(!e)return"";var t=re.fromFormat(e,"dd-MM-yyyy HH:mm:ss").toFormat("d LLL yyyy HH:mm");if(t=="Invalid DateTime")var t=re.fromFormat(e,"yyyy-MM-dd HH:mm:ss").toFormat("d LLL yyyy HH:mm");if(t=="Invalid DateTime")var t=re.fromFormat(e,"yyyy-MM-dd ").toFormat("d LLL yyyy");if(t=="Invalid DateTime")var t=re.fromISO(e).toFormat("d LLL yyyy");return t=="Invalid DateTime"?e:t}};yE(Tu);Tu.mount("#app");

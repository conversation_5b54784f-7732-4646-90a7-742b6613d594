<?php
Use PhpRbac\Rbac;
class PERMISOS {

    private ?array $resp;

    public function __construct() { 
        $this->resp = array(
            'estado' => true,
            'mensaje' => 'Se encontraron resultados',
            'data' => array()
        );
    }

    public static function traeTodosPermisos($id)
    {
        $resp         = new static();
        $permisos        = array();
        if(!empty($id) && is_numeric($id))
        {
          $sql_add = " AND ID = '".$id."' ";
        }
        $sql_roles          = "SELECT * FROM rbac_permissions WHERE Title != 'root' ".$sql_add." ORDER BY ID ASC";
        $row_roles        = DB::query($sql_roles,'S');
        foreach($row_roles AS &$rol)
        {
            if(!empty($rol['ID']))
            {
                array_push($permisos, array(   'id'            => $rol['ID'],
                                            'title'         => $rol['Title'],
                                            'description'   => $rol['Description']
                ));
            }
        }
        if(count($permisos) == 0)
        {
            $resp->estado      = false;
            $resp->mensaje     = 'No se encontraron permisos';
            $resp->data        = array();
        }else{
            $resp->estado      = true;
            $resp->mensaje     = 'Se encontraron resultados';
            $resp->data        = $permisos;
        } 
    echo json_encode($resp);
    return $resp;
    }

    public static function validaPermiso($roles,$permisox)
    {
        $rbac           = new Rbac();
        $permissions    = array();
        $respuesta      = false;
        if(!empty($permisox))
        {
            $sql 		    = "SELECT * FROM rbac_permissions WHERE  UPPER(Title) = '".strtoupper($permisox)."'";
            $row      		= DB::query($sql,'S');
            if(count($row) > 0)
            {
                if(count($roles) > 0)
                {
                    foreach($roles AS $rol)
                    {
                        $permisos = $rbac->Roles->permissions($rol['id'], false);
                        foreach($permisos AS $permiso)
                        {
                            $permissions[] = strtoupper($permiso['Title']);
                        }
                    }
                }
                $respuesta = in_array(strtoupper($permisox), $permissions);
            }
        }
        return $respuesta;
    }

    public static function validarRolPagina($roles)
    {
        $permitido      = false;
        if(count($roles) > 0)
        {
            $roles_usuario_logueado = array();
            foreach($roles as $r)
            {
                $roles_usuario_logueado[] = $r['Title'];
            }
            //Saco roles permitidos para esta pagina
            $parts              = explode('/', $_SERVER["SCRIPT_NAME"]);
            $file               = $parts[count($parts) - 1];
            $sql 				= "SELECT * FROM paginas_roles WHERE  modulo = 'usuario' AND archivo = '".$file."'";
            $row      			= DB::query($sql,'S');
            if(count($row) > 0)
            {
                $roles_permitidos 	= explode(",",strtoupper($row[0]['roles']));
                $roles_permitidos   = array_map('trim', $roles_permitidos);
                $permitido          = false;
                foreach($roles_usuario_logueado as $rol)
                {
                    if (in_array(strtoupper($rol), $roles_permitidos)) {
                        $permitido = true;
                        break;
                    }
                }
            }
        }
        return $permitido;
    }
}

?>
{"name": "sign-v3-prototype", "version": "3.0.0-prototype", "description": "SIGN+ v3 Prototype - Sistema de Gestión Notarial Moderno", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/lib/db/seed.ts", "db:reset": "tsx src/lib/db/reset.ts", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:rebuild": "docker-compose up -d --build"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.0", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-table": "^1.0.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.19", "@tanstack/react-table": "^8.11.8", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "date-fns": "^3.2.0", "drizzle-orm": "^0.29.3", "drizzle-zod": "^0.5.1", "ioredis": "^5.3.2", "jose": "^5.2.0", "lucide-react": "^0.312.0", "next": "^15.0.3", "next-auth": "5.0.0-beta.4", "next-themes": "^0.2.1", "postgres": "^3.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "recharts": "^2.10.3", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/eslint-config-next": "^15.0.3", "@playwright/test": "^1.41.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.11", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "autoprefixer": "^10.4.17", "drizzle-kit": "^0.20.13", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8.4.33", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "keywords": ["nextjs", "typescript", "postgresql", "notarial", "sign", "document-management"], "author": "SIGN+ Development Team", "license": "PROPRIETARY"}
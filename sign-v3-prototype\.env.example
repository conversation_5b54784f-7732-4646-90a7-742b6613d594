# ==============================================
# CONFIGURACIÓN DE ENTORNO - SIGN V3 PROTOTYPE
# ==============================================

# Entorno de ejecución
NODE_ENV=development

# ==============================================
# BASE DE DATOS POSTGRESQL
# ==============================================
DATABASE_URL=postgresql://sign_user:sign_secure_password_2024@localhost:5432/sign_v3_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=sign_v3_db
DB_USER=sign_user
DB_PASSWORD=sign_secure_password_2024

# Pool de conexiones
DB_POOL_MIN=2
DB_POOL_MAX=10

# ==============================================
# REDIS (CACHE Y SESIONES)
# ==============================================
REDIS_URL=redis://:redis_secure_password_2024@localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_secure_password_2024

# ==============================================
# NEXTAUTH.JS CONFIGURACIÓN
# ==============================================
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-nextauth-secret-key-minimum-32-characters-long

# JWT configuración
JWT_SECRET=your-jwt-secret-key-for-additional-tokens
JWT_EXPIRATION=8h

# ==============================================
# CONFIGURACIÓN DE APLICACIÓN
# ==============================================
APP_NAME="SIGN+ v3 Prototype"
APP_VERSION=3.0.0-prototype
APP_URL=http://localhost:3000

# ==============================================
# CONFIGURACIÓN DE SEGURIDAD
# ==============================================
# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Sesiones
SESSION_MAX_AGE=28800
SESSION_UPDATE_AGE=3600

# Contraseñas
PASSWORD_MIN_LENGTH=12
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# ==============================================
# CONFIGURACIÓN DE EMAIL
# ==============================================
# Para desarrollo (MailHog)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# Para producción (configurar según proveedor)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM=<EMAIL>

# ==============================================
# CONFIGURACIÓN DE LOGGING
# ==============================================
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log
LOG_MAX_FILES=7
LOG_MAX_SIZE=10m

# ==============================================
# CONFIGURACIÓN DE ARCHIVOS
# ==============================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,jpg,jpeg,png
UPLOAD_PATH=./uploads

# ==============================================
# CONFIGURACIÓN DE DESARROLLO
# ==============================================
# Next.js
NEXT_TELEMETRY_DISABLED=1

# Debug
DEBUG=sign:*
VERBOSE_LOGGING=true

# Hot reload
WATCHPACK_POLLING=true

# ==============================================
# CONFIGURACIÓN DE INTEGRACIÓN LEGACY
# ==============================================
# Para conectar con sistema PHP actual
LEGACY_DB_HOST=localhost
LEGACY_DB_PORT=3306
LEGACY_DB_NAME=cons_not
LEGACY_DB_USER=root
LEGACY_DB_PASSWORD=653a9ff1ec

# API Legacy
LEGACY_API_URL=http://localhost/SIGNv2-Main
LEGACY_API_TOKEN=legacy-integration-token

# ==============================================
# CONFIGURACIÓN DE MONITOREO
# ==============================================
# Sentry (opcional)
# SENTRY_DSN=your-sentry-dsn
# SENTRY_ENVIRONMENT=development

# Health checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# ==============================================
# CONFIGURACIÓN ESPECÍFICA NOTARIAL
# ==============================================
# Configuración de notaría
NOTARIA_NOMBRE="Notaría de Prueba"
NOTARIA_DIRECCION="Dirección de Prueba"
NOTARIA_COMUNA="Comuna de Prueba"
NOTARIA_TELEFONO="+56 2 1234 5678"

# Configuración de repertorio
REPERTORIO_ANHO_ACTUAL=2025
REPERTORIO_NUMERO_INICIAL=1

# Configuración de firma digital
FIRMA_DIGITAL_ENABLED=true
FIRMA_DIGITAL_PROVIDER=local

# ==============================================
# CONFIGURACIÓN DE TESTING
# ==============================================
# Base de datos de testing
TEST_DATABASE_URL=postgresql://sign_user:sign_secure_password_2024@localhost:5432/sign_v3_test_db

# Configuración de testing
TEST_TIMEOUT=30000
TEST_COVERAGE_THRESHOLD=80

<?php
class BASECAJA {
    public static function crearTablas(){

        $raf      = "SHOW COLUMNS FROM cardex_boletas LIKE 'debito' ";
        $arf      = DB::query($raf,'S');
        if( count($arf) == 0 )
        { 
            $r_up     = "ALTER TABLE `cardex_boletas` ADD `debito` INT(16) NULL AFTER `deposito`, ADD `tipo_pago` VARCHAR(50) NULL";
            $fin_up   = DB::query($r_up,'U');
        }

        $raf      = "SHOW COLUMNS FROM ot_nota_cobro_bhe LIKE 'tipo_pago' ";
        $arf      = DB::query($raf,'S');
        if( count($arf) == 0 )
        { 
            $r_up     = "ALTER TABLE `ot_nota_cobro_bhe` ADD `tipo_pago` VARCHAR(50) NULL";
            $fin_up   = DB::query($r_up,'U');
        }
        
        $create="CREATE TABLE IF NOT EXISTS `tipos_de_pago` (
            `id` bigint(21) NOT NULL auto_increment,
            `tipo` varchar(50) NULL,
            `icon` varchar(50) NULL,
            `creado` datetime default NULL,
            `creado_por` varchar(30) default NULL,
            `actualizado` datetime default NULL,
            `actualizado_por` varchar(30) default NULL,
            `eliminado` datetime default NULL,
            `eliminado_por` varchar(30) default NULL,
            PRIMARY KEY  (`id`),
            KEY `tipo` (`tipo`)
          ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
          
        $row4           = DB::query($create,'C');

        $result002	    = "SELECT count(*) as total FROM tipos_de_pago";
        $row121         = DB::query($result002,'S');
        if($row121[0]['total']==0)
        {
        
        $r_up=" INSERT INTO `tipos_de_pago` (`tipo`, `icon`,`creado`, `creado_por`) VALUES
        ('Débito/crédito','tarjeta','".date("Y-m-d H:i:s")."','1'),
        ('Efectivo','efectivo','".date("Y-m-d H:i:s")."','1'),
        ('Documento','documento','".date("Y-m-d H:i:s")."','1'),
        ('Depósito','deposito','".date("Y-m-d H:i:s")."','1');";
        $fin_up  = DB::query($r_up,'I');
        }

        $result002	    = "SELECT count(*) as total FROM tipos_de_pago where tipo = 'Por Pagar'";
        $row121         = DB::query($result002,'S');
        if($row121[0]['total']==0)
        {
        
        $r_up=" INSERT INTO `tipos_de_pago` (`tipo`, `icon`,`creado`, `creado_por`) VALUES
        ('Por Pagar','deposito','".date("Y-m-d H:i:s")."','1');";
        $fin_up  = DB::query($r_up,'I');
        }
      }
      
}
?>
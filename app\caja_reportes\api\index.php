<?php
require_once "../../../layouts/config.php";
require_once PHYSICAL_PATH. '/library/PhpRbac/autoload.php';
require_once PHYSICAL_PATH.'/library/jwt/jwt_utils.php';
require_once('soap/nusoap.php');
require_once('../library/html2pdf/vendor/autoload.php');
require_once "../../../appa/autoload.php";
use MyApp\Chat;
Use PhpRbac\Rbac;
$rbac = new Rbac();
include 'db.php';
include 'token.php';
include 'caja.php';
include 'permisos.php';
include 'base.php';
include 'historial.php';
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Credentials: true");
//header("Access-Control-Allow-Headers: Content-Type");
header("Access-Control-Allow-Methods: POST, OPTIONS");
//header("Access-Control-Allow-Headers:authorization, content-type, accept, origin");

$token 	= USUARIOTOKEN::get_bearer_token();
if(empty($token)){
	http_response_code(401);
	echo json_encode(array('error' => 'Acceso Denegado'));
	die();
} else {
	$ciphering = "AES-128-CTR";
	$encryption_iv = '1236667891004233';
	$iv_length = openssl_cipher_iv_length($ciphering);
	$options = 0;
	$secret = ';bokZdnT2Hfv^:7)9gC*eW/>=chF6c';
	$decryption=openssl_decrypt (base64_decode($token), $ciphering,$secret, $options, $encryption_iv);
	if(!is_jwt_valid($decryption)){
		http_response_code(401);
		echo json_encode(array('error' => 'Token Inválido'));
		die();	
	}else{
		$user 		= new entidades\usuario();
		$dataToken 	= json_decode($user->getUncipheredToken($token));
		$sql 		= "SELECT * FROM usuarios_not WHERE id = '".$dataToken->id."' AND nombreSocial = '".$dataToken->username."' AND estado= 1";
		$row      	= DB::query($sql,'S');
		if(empty($row[0]['id']))
		{
			http_response_code(401);
			echo json_encode(array('error' => 'Acceso Denegado'));
			die();
		}else{
			if(count($dataToken->roles) == 0)
			{
				http_response_code(403);
				echo json_encode(array('error' => 'Acceso Denegado'));
				die();
			}
		}
	}
}

foreach($dataToken->roles AS $datarol)
{
	$array_roles 	= $rbac->Roles->descendants($datarol->id);
	$roles  		= array();
	if(count($array_roles) > 0)
	{
		$roles = array_map(function($item) {
			return array('id' => $item['ID'],'Title' =>  $item['Title']);
		}, $array_roles);
		foreach($dataToken->roles AS $rol)
		{
			$roles[strtoupper($rol->Title)] = array('id' => $rol->id,'Title' =>  strtoupper($rol->Title));
		}
	}else{
		$roles = array_map(function($item) {
			return array('id' => $item->id,'Title' =>  strtoupper($item->Title));
		}, $dataToken->roles);
	}
}

$permitido = PERMISOS::validarRolPagina($roles);
if(!$permitido)
{
	http_response_code(403);
	echo json_encode(array('error' => 'Rol del usuario no es permitido para este modulo'));
	die();
}

$data = json_decode(file_get_contents("php://input", true));


switch ($data->accion) {
	case 'creaTablas':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar") && !PERMISOS::validaPermiso($roles,"usuario_editar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			BASECAJA::crearTablas();
		}
		break;
	case 'emitirBHE':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			$form		= $data->form;
			CAJA::emitirBHE($form,$dataToken->id,$roles,$token);
		}
		break;
	case 'tiposPagos':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar") && !PERMISOS::validaPermiso($roles,"usuario_editar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			//$obj = new WebSocketServer();
			//$obj->sendMessageToAllClients('wena lee desde el back');
			CAJA::tiposPagos();			
		}
		break;
	case 'reporteBHE':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar") && !PERMISOS::validaPermiso($roles,"usuario_editar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			$form		= $data->form;
			CAJA::reporteBHE($form,$dataToken->id,$roles,$token);
		}
		break;
	case 'ListUsuarios':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar") && !PERMISOS::validaPermiso($roles,"usuario_editar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			CAJA::ListUsuarios();
		}
		break;
	case 'ListClientes':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar") && !PERMISOS::validaPermiso($roles,"usuario_editar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			CAJA::ListClientes();
		}
		break;
	case 'anularBHE':
		if (!PERMISOS::validaPermiso($roles,"usuario_agregar") && !PERMISOS::validaPermiso($roles,"usuario_editar")) {
			http_response_code(403);
			echo json_encode(array('error' => 'Usuario no posee el permiso para esta acción'));
		}else{
			$boleta		= $data->boleta;
			CAJA::anularBHE($boleta,$dataToken->id,$roles);
		}
		break;
	default:
		http_response_code(404);
		echo json_encode(array('error' => 'Acción Invalida'));
		die();
		break;
}

?>

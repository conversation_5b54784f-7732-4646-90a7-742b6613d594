{"name": "evenement/evenement", "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "autoload": {"psr-0": {"Evenement": "src"}}, "autoload-dev": {"psr-0": {"Evenement": "tests"}, "files": ["tests/Evenement/Tests/functions.php"]}}
import { bigint } from "./bigint.js";
import { bigintT } from "./bigintT.js";
import { boolean } from "./boolean.js";
import { bytes } from "./bytes.js";
import { customType } from "./custom.js";
import { dateDuration } from "./date-duration.js";
import { decimal } from "./decimal.js";
import { doublePrecision } from "./double-precision.js";
import { duration } from "./duration.js";
import { integer } from "./integer.js";
import { json } from "./json.js";
import { localDate } from "./localdate.js";
import { localTime } from "./localtime.js";
import { real } from "./real.js";
import { relDuration } from "./relative-duration.js";
import { smallint } from "./smallint.js";
import { text } from "./text.js";
import { timestamp } from "./timestamp.js";
import { timestamptz } from "./timestamptz.js";
import { uuid } from "./uuid.js";
export declare function getGelColumnBuilders(): {
    localDate: typeof localDate;
    localTime: typeof localTime;
    decimal: typeof decimal;
    dateDuration: typeof dateDuration;
    bigintT: typeof bigintT;
    duration: typeof duration;
    relDuration: typeof relDuration;
    bytes: typeof bytes;
    customType: typeof customType;
    bigint: typeof bigint;
    boolean: typeof boolean;
    doublePrecision: typeof doublePrecision;
    integer: typeof integer;
    json: typeof json;
    real: typeof real;
    smallint: typeof smallint;
    text: typeof text;
    timestamptz: typeof timestamptz;
    uuid: typeof uuid;
    timestamp: typeof timestamp;
};
export type GelColumnsBuilders = ReturnType<typeof getGelColumnBuilders>;

version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:16-alpine
    container_name: e-notaria-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: e_notaria_db
      POSTGRES_USER: notaria_user
      POSTGRES_PASSWORD: notaria_secure_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    networks:
      - notaria-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U notaria_user -d e_notaria_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para cache y sesiones
  redis:
    image: redis:7-alpine
    container_name: e-notaria-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_notaria_2024 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - notaria-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # pgAdmin para administración de BD
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: e-notaria-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_notaria_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - notaria-network

  # Mailhog para testing de emails (opcional)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: e-notaria-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - notaria-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  notaria-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

{"name": "e-notaria", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "next": "15.3.5", "next-auth": "^5.0.0-beta.29", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}
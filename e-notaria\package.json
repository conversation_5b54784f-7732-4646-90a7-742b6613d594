{"name": "e-notaria", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@tailwindcss/postcss": "^4.1.11", "@types/bcryptjs": "^3.0.0", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "drizzle-kit": "^0.18.1", "drizzle-orm": "^0.44.2", "next": "15.3.5", "next-auth": "^5.0.0-beta.29", "postcss": "^8.5.6", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4.1.11", "typescript": "^5"}}
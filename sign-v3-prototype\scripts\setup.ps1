# Script de configuración inicial para Windows
# Ejecutar como: .\scripts\setup.ps1

Write-Host "🚀 Configurando SIGN+ v3 Prototype..." -ForegroundColor Green

# Verificar que Docker esté instalado
Write-Host "📋 Verificando prerequisitos..." -ForegroundColor Yellow

if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker no está instalado. Por favor instala Docker Desktop." -ForegroundColor Red
    exit 1
}

if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker Compose no está disponible." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Docker está disponible" -ForegroundColor Green

# Verificar que Node.js esté instalado (para desarrollo local)
if (!(Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "⚠️  Node.js no está instalado. Se recomienda para desarrollo local." -ForegroundColor Yellow
} else {
    $nodeVersion = node --version
    Write-Host "✅ Node.js $nodeVersion está disponible" -ForegroundColor Green
}

# Crear archivo .env desde .env.example
Write-Host "📝 Configurando variables de entorno..." -ForegroundColor Yellow

if (!(Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host "✅ Archivo .env creado desde .env.example" -ForegroundColor Green
    Write-Host "⚠️  Revisa y ajusta las variables en .env según tu entorno" -ForegroundColor Yellow
} else {
    Write-Host "✅ Archivo .env ya existe" -ForegroundColor Green
}

# Crear directorios necesarios
Write-Host "📁 Creando directorios..." -ForegroundColor Yellow

$directories = @(
    "database/init",
    "database/backups",
    "logs",
    "uploads",
    "src/app",
    "src/components",
    "src/lib",
    "src/types"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Directorio $dir creado" -ForegroundColor Green
    }
}

# Crear archivo .gitignore si no existe
Write-Host "📝 Configurando .gitignore..." -ForegroundColor Yellow

if (!(Test-Path ".gitignore")) {
    @"
# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output

# Next.js
.next/
out/

# Production
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Docker
.dockerignore

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Database
database/backups/*.sql
database/backups/*.dump

# Uploads
uploads/*
!uploads/.gitkeep

# Temporary files
tmp/
temp/
"@ | Out-File -FilePath ".gitignore" -Encoding UTF8
    Write-Host "✅ Archivo .gitignore creado" -ForegroundColor Green
}

# Inicializar Docker Compose
Write-Host "🐳 Iniciando servicios Docker..." -ForegroundColor Yellow

try {
    docker-compose up -d postgres redis
    Write-Host "✅ Servicios de base de datos iniciados" -ForegroundColor Green
    
    # Esperar a que PostgreSQL esté listo
    Write-Host "⏳ Esperando a que PostgreSQL esté listo..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # Verificar conexión a PostgreSQL
    $maxAttempts = 30
    $attempt = 0
    
    do {
        $attempt++
        try {
            docker exec sign-v3-postgres pg_isready -U sign_user -d sign_v3_db | Out-Null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ PostgreSQL está listo" -ForegroundColor Green
                break
            }
        } catch {
            # Continuar intentando
        }
        
        if ($attempt -eq $maxAttempts) {
            Write-Host "❌ PostgreSQL no respondió después de $maxAttempts intentos" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "⏳ Intento $attempt/$maxAttempts - Esperando PostgreSQL..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    } while ($true)
    
} catch {
    Write-Host "❌ Error al iniciar servicios Docker: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Mostrar información de servicios
Write-Host "`n🎉 Configuración completada!" -ForegroundColor Green
Write-Host "`n📋 Servicios disponibles:" -ForegroundColor Cyan
Write-Host "  🗄️  PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "  🔴 Redis: localhost:6379" -ForegroundColor White
Write-Host "  🔧 pgAdmin: http://localhost:5050" -ForegroundColor White
Write-Host "  📧 MailHog: http://localhost:8025" -ForegroundColor White

Write-Host "`n📝 Próximos pasos:" -ForegroundColor Cyan
Write-Host "  1. Revisa y ajusta las variables en .env" -ForegroundColor White
Write-Host "  2. Ejecuta: npm install" -ForegroundColor White
Write-Host "  3. Ejecuta: npm run db:generate" -ForegroundColor White
Write-Host "  4. Ejecuta: npm run db:migrate" -ForegroundColor White
Write-Host "  5. Ejecuta: npm run dev" -ForegroundColor White

Write-Host "`n🔗 URLs importantes:" -ForegroundColor Cyan
Write-Host "  📱 Aplicación: http://localhost:3000" -ForegroundColor White
Write-Host "  🗄️  pgAdmin: http://localhost:5050 (<EMAIL> / admin_password_2024)" -ForegroundColor White
Write-Host "  📧 MailHog: http://localhost:8025" -ForegroundColor White

Write-Host "`n✨ ¡Listo para desarrollar!" -ForegroundColor Green

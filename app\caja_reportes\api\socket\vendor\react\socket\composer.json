{"name": "react/socket", "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["async", "socket", "stream", "connection", "ReactPHP"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0", "evenement/evenement": "^3.0 || ^2.0 || ^1.0", "react/dns": "^1.11", "react/event-loop": "^1.2", "react/promise": "^3 || ^2.6 || ^1.2.1", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/async": "^4 || ^3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.9"}, "autoload": {"psr-4": {"React\\Socket\\": "src"}}, "autoload-dev": {"psr-4": {"React\\Tests\\Socket\\": "tests"}}}
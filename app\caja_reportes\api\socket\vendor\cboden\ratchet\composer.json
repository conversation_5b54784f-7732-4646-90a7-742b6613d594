{"name": "cboden/ratchet", "type": "library", "description": "PHP WebSocket library", "keywords": ["WebSockets", "Server", "Ratchet", "Sockets", "WebSocket"], "homepage": "http://socketo.me", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "role": "Developer"}], "support": {"issues": "https://github.com/ratchetphp/Ratchet/issues", "chat": "https://gitter.im/reactphp/reactphp"}, "autoload": {"psr-4": {"Ratchet\\": "src/Ratchet"}}, "require": {"php": ">=5.4.2", "ratchet/rfc6455": "^0.3.1", "react/socket": "^1.0 || ^0.8 || ^0.7 || ^0.6 || ^0.5", "react/event-loop": ">=0.4", "guzzlehttp/psr7": "^1.7|^2.0", "symfony/http-foundation": "^2.6|^3.0|^4.0|^5.0|^6.0", "symfony/routing": "^2.6|^3.0|^4.0|^5.0|^6.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}}
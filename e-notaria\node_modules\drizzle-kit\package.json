{"name": "drizzle-kit", "version": "0.18.1", "repository": "https://github.com/drizzle-team/drizzle-kit-mirror", "author": "Drizzle Team", "license": "MIT", "bin": {"drizzle-kit": "./index.js"}, "scripts": {"migrate:old": "drizzle-kit generate:pg --out ./dev/migrations-pg --schema ./dev/migrations-pg/schema.ts", "push": "node -r esbuild-register ./src/cli/index.ts push:mysql", "migrate:old:mysql": "drizzle-kit generate:mysql --out ./dev/migrations-mysql --schema ./dev/migrations-mysql/schema.ts", "start:pg": "node -r esbuild-register ./src/cli/index.ts generate:pg", "start:sqlite": "node -r esbuild-register ./src/cli/index.ts generate:sqlite --out ./dev/migrations-sqlite --schema ./dev/migrations-sqlite/schema.ts", "start:mysql": "node -r esbuild-register ./src/cli/index.ts generate:mysql", "check:pg": "node -r esbuild-register ./src/cli/index.ts check --out ./dev/migrations --dialect pg", "introspect:mysql": "node -r esbuild-register ./src/cli/index.ts introspect:mysql --config drizzle.config2.ts", "introspect:pg": "node -r esbuild-register ./src/cli/index.ts introspect:pg --out ./dev/introspect-pg --connectionString=postgresql://postgres@localhost:5432/introspect", "drop": "node -r esbuild-register ./src/cli/index.ts drop --out ./dev/migrations-pg", "up:pg": "node -r esbuild-register ./src/cli/index.ts up:pg --out ./dev/migrations-pg", "up:mysql": "node -r esbuild-register ./src/cli/index.ts up:mysql --out ./dev/migrations-mysql", "check:equedi": "node -r esbuild-register ./src/cli/index.ts check --out ./dev/equedi --dialect pg", "run": "node -r esbuild-register index.ts", "watch": "esbuild ./src/clie/index.ts --bundle --platform=node --target=node10.4 --outfile=./dist/index.js --external:esbuild --external:pg-native --sourcemap --watch", "diff": "esbuild ./src/clie/index.ts ./dev/diff.ts", "prepare:mysql": "node -r esbuild-register dev/mysql/index.ts", "prepare:pg": "node -r esbuild-register dev/index.ts", "prepare-snapshot": "node -r esbuild-register ./dev/prepare-snapshot prepare ./dev/data", "sim": "node -r esbuild-register ./dev/simulate.ts", "sim:sqlite": "node -r esbuild-register ./dev/sqlite/index.ts", "test": "ava test --timeout=60s", "build": "pnpm build:cli && pnpm build:utils && pnpm build:cli-types", "build:cli": "esbuild ./src/cli/index.ts --bundle --platform=node --target=node10.4 --outfile=./dist/index.js --external:esbuild --external:drizzle-orm-pg --external:drizzle-orm-sqlite --external:drizzle-orm-mysql --external:drizzle-orm --external:pg-native", "build:utils": "esbuild ./src/utils.ts --bundle --platform=node --target=node10.4 --outfile=./dist/utils.js", "build:cli-types": "tsc -p tsconfig.cli-types.json", "pack": "build && package", "tsc": "tsc -p tsconfig.build.json", "pub": "cp package.json readme.md dist/ && cd dist && npm publish"}, "ava": {"files": ["test/**/*.ts"], "extensions": {"ts": "module"}, "nodeArguments": ["--loader=tsx"]}, "dependencies": {"camelcase": "^7.0.1", "chalk": "^5.2.0", "commander": "^9.4.1", "esbuild": "^0.15.18", "esbuild-register": "^3.4.2", "glob": "^8.1.0", "hanji": "^0.0.5", "json-diff": "0.9.0", "minimatch": "^7.4.3", "zod": "^3.20.2"}, "devDependencies": {"@types/dockerode": "^3.3.14", "@types/glob": "^8.1.0", "@types/minimatch": "^5.1.2", "@types/node": "^18.11.15", "@types/pg": "^8.6.5", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "ava": "^5.1.0", "dockerode": "^3.3.4", "dotenv": "^16.0.3", "drizzle-kit": "^0.16.8", "drizzle-orm": "0.25.1", "esbuild": "^0.15.7", "esbuild-register": "^3.3.3", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "get-port": "^6.1.2", "mysql2": "2.3.3", "pg": "^8.8.0", "prettier": "^2.8.1", "tsx": "^3.12.1", "typescript": "^4.9.4", "uvu": "^0.5.6"}}
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuración experimental para Next.js 15
  experimental: {
    // Habilitar Server Actions
    serverActions: {
      allowedOrigins: ['localhost:3000'],
      bodySizeLimit: '10mb'
    },
    // Turbopack para desarrollo más rápido
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js'
        }
      }
    },
    // Optimizaciones de compilación
    optimizePackageImports: [
      '@radix-ui/react-icons',
      'lucide-react',
      'date-fns'
    ]
  },

  // Configuración de TypeScript
  typescript: {
    // Ignorar errores de TypeScript durante el build (solo para desarrollo)
    ignoreBuildErrors: false
  },

  // Configuración de ESLint
  eslint: {
    // Ignorar errores de ESLint durante el build (solo para desarrollo)
    ignoreDuringBuilds: false
  },

  // Configuración de imágenes
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**'
      }
    ],
    formats: ['image/webp', 'image/avif']
  },

  // Headers de seguridad
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          }
        ]
      }
    ]
  },

  // Configuración de redirects
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/dashboard/admin',
        permanent: true
      }
    ]
  },

  // Configuración de rewrites para API legacy
  async rewrites() {
    return [
      {
        source: '/api/legacy/:path*',
        destination: 'http://localhost/SIGNv2-Main/app/:path*'
      }
    ]
  },

  // Configuración de webpack
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Configuración adicional de webpack si es necesaria
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false
    }

    return config
  },

  // Configuración de output para standalone
  output: 'standalone',

  // Configuración de compresión
  compress: true,

  // Configuración de trailing slash
  trailingSlash: false,

  // Configuración de powered by header
  poweredByHeader: false,

  // Configuración de generación de source maps
  productionBrowserSourceMaps: false,

  // Configuración de optimización de CSS
  optimizeFonts: true,

  // Configuración de SWC minifier
  swcMinify: true,

  // Variables de entorno públicas
  env: {
    APP_NAME: process.env.APP_NAME || 'SIGN+ v3',
    APP_VERSION: process.env.APP_VERSION || '3.0.0-prototype'
  }
}

module.exports = nextConfig

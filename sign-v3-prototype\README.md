# 🚀 SIGN+ v3 Prototype

Sistema de Gestión Notarial Moderno construido con **Next.js 15**, **PostgreSQL** y **Docker**.

## 📋 Características Principales

- ✅ **Next.js 15** con App Router y Server Actions
- ✅ **PostgreSQL 16** como base de datos principal
- ✅ **Redis** para cache y sesiones
- ✅ **Auth.js** para autenticación segura
- ✅ **Drizzle ORM** para manejo de base de datos
- ✅ **TypeScript** para type safety
- ✅ **Tailwind CSS** para estilos
- ✅ **Docker** para desarrollo y despliegue
- ✅ **Shadcn/ui** para componentes UI

## 🛠️ Prerequisitos

- **Docker Desktop** (recomendado)
- **Node.js 20+** (para desarrollo local)
- **Git**

## 🚀 Inicio Rápido

### 1. Configuración Inicial

```powershell
# Clonar y navegar al directorio
cd sign-v3-prototype

# Ejecutar script de configuración (Windows)
.\scripts\setup.ps1

# O manualmente:
cp .env.example .env
docker-compose up -d postgres redis
```

### 2. Instalación de Dependencias

```bash
# Instalar dependencias
npm install

# Generar esquemas de base de datos
npm run db:generate

# Ejecutar migraciones
npm run db:migrate

# Poblar base de datos (opcional)
npm run db:seed
```

### 3. Desarrollo

```bash
# Iniciar en modo desarrollo
npm run dev

# O con Docker
docker-compose up -d
```

## 🐳 Servicios Docker

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| **nextjs** | 3000 | Aplicación Next.js |
| **postgres** | 5432 | Base de datos PostgreSQL |
| **redis** | 6379 | Cache y sesiones |
| **pgadmin** | 5050 | Administrador de BD |
| **mailhog** | 8025 | Testing de emails |

## 📁 Estructura del Proyecto

```
sign-v3-prototype/
├── src/
│   ├── app/                 # App Router de Next.js
│   │   ├── (auth)/         # Rutas de autenticación
│   │   ├── dashboard/      # Panel principal
│   │   ├── api/           # API Routes
│   │   └── globals.css    # Estilos globales
│   ├── components/        # Componentes reutilizables
│   │   ├── ui/           # Componentes base (shadcn)
│   │   └── forms/        # Formularios específicos
│   ├── lib/              # Utilidades y configuración
│   │   ├── db/           # Configuración de base de datos
│   │   ├── auth/         # Configuración de autenticación
│   │   └── utils.ts      # Utilidades generales
│   └── types/            # Definiciones de tipos TypeScript
├── database/
│   ├── init/             # Scripts de inicialización
│   └── backups/          # Respaldos de BD
├── scripts/              # Scripts de automatización
├── docker-compose.yml    # Configuración Docker
├── Dockerfile           # Imagen Docker
└── drizzle.config.ts    # Configuración Drizzle ORM
```

## 🔧 Scripts Disponibles

```bash
# Desarrollo
npm run dev              # Iniciar en modo desarrollo
npm run build           # Construir para producción
npm run start           # Iniciar en modo producción

# Base de datos
npm run db:generate     # Generar migraciones
npm run db:migrate      # Ejecutar migraciones
npm run db:studio       # Abrir Drizzle Studio
npm run db:seed         # Poblar base de datos
npm run db:reset        # Resetear base de datos

# Testing
npm run test            # Ejecutar tests
npm run test:watch      # Tests en modo watch
npm run test:coverage   # Tests con coverage
npm run test:e2e        # Tests end-to-end

# Calidad de código
npm run lint            # Ejecutar ESLint
npm run lint:fix        # Corregir errores de ESLint
npm run format          # Formatear código con Prettier
npm run type-check      # Verificar tipos TypeScript

# Docker
npm run docker:up       # Iniciar servicios Docker
npm run docker:down     # Detener servicios Docker
npm run docker:logs     # Ver logs de Docker
npm run docker:rebuild  # Reconstruir contenedores
```

## 🔐 Configuración de Seguridad

### Variables de Entorno Críticas

```bash
# Cambiar estos valores en producción
NEXTAUTH_SECRET=your-super-secret-nextauth-secret-key
JWT_SECRET=your-jwt-secret-key
DATABASE_URL=postgresql://user:password@localhost:5432/db
REDIS_URL=redis://:password@localhost:6379
```

### Credenciales por Defecto (Solo Desarrollo)

- **PostgreSQL**: `sign_user` / `sign_secure_password_2024`
- **Redis**: `redis_secure_password_2024`
- **pgAdmin**: `<EMAIL>` / `admin_password_2024`

## 📊 Base de Datos

### Conexión a PostgreSQL

```bash
# Desde línea de comandos
psql -h localhost -p 5432 -U sign_user -d sign_v3_db

# Desde pgAdmin
# URL: http://localhost:5050
# Email: <EMAIL>
# Password: admin_password_2024
```

### Esquema Principal

- **users** - Usuarios del sistema
- **roles** - Roles y permisos
- **ordenes_trabajo** - Órdenes de trabajo (entidad principal)
- **clientes** - Información de clientes
- **documentos** - Gestión documental
- **audit_logs** - Logs de auditoría

## 🧪 Testing

```bash
# Tests unitarios
npm run test

# Tests con coverage
npm run test:coverage

# Tests end-to-end
npm run test:e2e

# Tests en modo watch
npm run test:watch
```

## 📈 Monitoreo y Logs

### Logs de Aplicación

```bash
# Ver logs en tiempo real
docker-compose logs -f nextjs

# Ver logs de base de datos
docker-compose logs -f postgres

# Ver todos los logs
npm run docker:logs
```

### Health Checks

- **Aplicación**: `http://localhost:3000/api/health`
- **Base de datos**: Incluido en Docker Compose
- **Redis**: Incluido en Docker Compose

## 🔄 Integración con Sistema Legacy

### Configuración

```bash
# Variables para conectar con PHP actual
LEGACY_DB_HOST=localhost
LEGACY_DB_PORT=3306
LEGACY_DB_NAME=cons_not
LEGACY_API_URL=http://localhost/SIGNv2-Main
```

### API Proxy

```typescript
// Ejemplo de integración
const legacyData = await fetch('/api/legacy/ordenes-trabajo/123')
```

## 🚀 Despliegue

### Desarrollo

```bash
docker-compose up -d
```

### Producción

```bash
# Construir imagen
docker build -t sign-v3-prototype .

# Ejecutar en producción
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 Documentación Adicional

- [Arquitectura del Sistema](./docs/architecture.md)
- [Guía de Desarrollo](./docs/development.md)
- [API Reference](./docs/api.md)
- [Guía de Despliegue](./docs/deployment.md)

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto es propietario y confidencial.

## 🆘 Soporte

Para soporte técnico, contactar al equipo de desarrollo.

---

**SIGN+ v3 Prototype** - Sistema de Gestión Notarial Moderno

{"name": "react/event-loop", "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["event-loop", "asynchronous"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "autoload-dev": {"psr-4": {"React\\Tests\\EventLoop\\": "tests/"}}}
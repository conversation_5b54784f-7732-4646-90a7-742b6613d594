version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:16-alpine
    container_name: sign-v3-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: sign_v3_db
      POSTGRES_USER: sign_user
      POSTGRES_PASSWORD: sign_secure_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    networks:
      - sign-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sign_user -d sign_v3_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para cache y sesiones
  redis:
    image: redis:7-alpine
    container_name: sign-v3-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_secure_password_2024 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - sign-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Aplicación Next.js 15
  nextjs:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: sign-v3-app
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************************************/sign_v3_db
      - REDIS_URL=redis://:redis_secure_password_2024@redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-super-secret-nextauth-secret-key-here
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sign-network
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: rebuild
          path: package.json

  # pgAdmin para administración de BD
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: sign-v3-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - sign-network

  # Mailhog para testing de emails
  mailhog:
    image: mailhog/mailhog:latest
    container_name: sign-v3-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - sign-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  sign-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

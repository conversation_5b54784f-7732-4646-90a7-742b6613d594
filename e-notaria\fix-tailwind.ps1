# Script para solucionar problemas de Tailwind CSS en Windows
Write-Host "🔧 Solucionando problemas de Tailwind CSS..." -ForegroundColor Yellow

# Detener el servidor de desarrollo si está ejecutándose
Write-Host "🛑 Deteniendo servidor de desarrollo..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*next*" } | Stop-Process -Force

# Limpiar cache de Next.js
Write-Host "🧹 Limpiando cache..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force ".next"
    Write-Host "✅ Cache de .next eliminado" -ForegroundColor Green
}

if (Test-Path "node_modules/.cache") {
    Remove-Item -Recurse -Force "node_modules/.cache"
    Write-Host "✅ Cache de node_modules eliminado" -ForegroundColor Green
}

# Reinstalar dependencias problemáticas
Write-Host "📦 Reinstalando dependencias..." -ForegroundColor Yellow

# Desinstalar paquetes problemáticos
npm uninstall lightningcss @tailwindcss/postcss

# Instalar versiones compatibles
npm install tailwindcss@latest postcss@latest autoprefixer@latest

# Regenerar configuración de Tailwind
Write-Host "⚙️ Regenerando configuración de Tailwind..." -ForegroundColor Yellow
npx tailwindcss init -p

Write-Host "✅ Problemas de Tailwind solucionados" -ForegroundColor Green
Write-Host "🚀 Ahora puedes ejecutar: npm run dev" -ForegroundColor Cyan

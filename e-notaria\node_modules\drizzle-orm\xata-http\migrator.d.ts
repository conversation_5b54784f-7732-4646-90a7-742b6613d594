import type { XataHttpDatabase } from "./driver.js";
export interface MigrationConfig {
    migrationsFolder: string;
    migrationsTable?: string;
}
/**
 * This function reads migrationFolder and execute each unapplied migration and mark it as executed in database
 *
 * NOTE: The Xata HTTP driver does not support transactions. This means that if any part of a migration fails,
 * no rollback will be executed. Currently, you will need to handle unsuccessful migration yourself.
 * @param db - drizzle db instance
 * @param config - path to migration folder generated by drizzle-kit
 */ export declare function migrate<TSchema extends Record<string, unknown>>(db: XataHttpDatabase<TSchema>, config: MigrationConfig): Promise<void>;

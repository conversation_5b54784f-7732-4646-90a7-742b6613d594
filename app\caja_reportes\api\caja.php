<?php

use Spipu\Html2Pdf\Html2Pdf;
use Spipu\Html2Pdf\Exception\Html2PdfException;
use Spipu\Html2Pdf\Exception\ExceptionFormatter;
define("tipoImpresora","NORMAL");
define("showPDF","I");
class CAJA {
    public static function emitirBHE($form,$usuario,$roles,$token)
    {
        $resp               = array('estado' => True, 'mensaje' => '', 'data' => '');
        $respuesta          = array();
        $totales_array      = array();
        $usuario_nombre     = DB::traeUsuario($usuario)['nombre'];
        $ot                 = $form->ot;
        $nc                 = $form->nc;
        $rut                = $form->rut;
        $glosa              = $form->glosa <> null && $form->glosa <> '' ? utf8_encode($form->glosa) : "Servicios notariales";
        $retiene_emisor     = $form->retiene_emisor;
        $porcentaje         = $form->porcentaje;
        $montoHonorario     = $form->montoHonorario;
        $montoRetencion     = ($montoHonorario*$porcentaje)/100;
        $montoRetencion     = round($montoRetencion);
        $montoNeto          = $montoHonorario - $montoRetencion;
        $tipo_pago          = $form->tipo_pago;

        $raf      = "SHOW COLUMNS FROM cardex_boletas LIKE 'debito' ";
        $arf      = DB::query($raf,'S');
        if( count($arf) == 0 )
        { 
            $r_up     = "ALTER TABLE `cardex_boletas` ADD `debito` INT(16) NULL AFTER `deposito`, ADD `tipo_pago` VARCHAR(50) NULL";
            $fin_up   = DB::query($r_up,'U');
        }

        $raf      = "SHOW COLUMNS FROM ot_nota_cobro_bhe LIKE 'tipo_pago' ";
        $arf      = DB::query($raf,'S');
        if( count($arf) == 0 )
        { 
            $r_up     = "ALTER TABLE `ot_nota_cobro_bhe` ADD `tipo_pago` VARCHAR(50) NULL";
            $fin_up   = DB::query($r_up,'U');
        }
        if(empty($ot) || !is_numeric($ot) || intval($ot) <= 0)
        {
            $resp['estado']     = false;
            $resp['mensaje']    = 'OT no válida';
            $resp['data']       = '';
            echo json_encode($resp);
            die();
        }else{
            $sql4         = "SELECT id FROM cardex WHERE numero = '".$ot."' ORDER by id DESC LIMIT 1";
            $roww         = DB::query($sql4,'S');
            if(empty($roww[0]['id']))
            {
                $resp['estado']     = false;
                $resp['mensaje']    = 'OT no válida';
                $resp['data']       = '';
                echo json_encode($resp);
                die();
            }
        }

        if(empty($nc) || !is_numeric($nc) || intval($nc) <= 0)
        {
            $resp['estado']     = false;
            $resp['mensaje']    = 'Nota de cobro no válida';
            $resp['data']       = '';
            echo json_encode($resp);
            die();
        }else{
            $sql4         = "SELECT id FROM ot_nota_cobro WHERE id = '".$nc."' AND eliminado IS NULL AND eliminado_por IS NULL ORDER by id DESC LIMIT 1";
            $roww         = DB::query($sql4,'S');
            if(empty($roww[0]['id']))
            {
                $resp['estado']     = false;
                $resp['mensaje']    = 'Nota de cobro no válida';
                $resp['data']       = '';
                echo json_encode($resp);
                die();
            }

            $sql4         = "SELECT id,total,bhe FROM ot_nota_cobro WHERE OT = '".$ot."' AND id = '".$nc."' AND eliminado IS NULL AND eliminado_por IS NULL ORDER by id DESC LIMIT 1";
            $roww         = DB::query($sql4,'S');
            if(empty($roww[0]['id']))
            {
                $resp['estado']     = false;
                $resp['mensaje']    = 'Nota de cobro no pertenece a la OT ingresada';
                $resp['data']       = '';
                echo json_encode($resp);
                die();
            }else{
                if(!is_null($roww[0]['bhe']))
                {
                    $resp['estado']     = false;
                    $resp['mensaje']    = 'Nota de cobro ya tiene una boleta asociada N°: '.$roww[0]['bhe'];
                    $resp['data']       = '';
                    echo json_encode($resp);
                    die();
                }

                if($montoHonorario <> $roww[0]['total'])
                {
                    $resp['estado']     = false;
                    $resp['mensaje']    = 'Montos no coinciden, el total de la NC es : $'.number_format($roww[0]['total'], 0, '', '.').' y se esta pagando un monto por : $'.number_format($montoHonorario, 0, '', '.');
                    $resp['data']       = '';
                    echo json_encode($resp);
                    die();
                }
            }
        }
        if($rut <> '0' && $rut <> '0-0')
        {
            $rut                = CAJA::rut_formato($rut); 
            if(!CAJA::valida_rut($rut) && $rut <> '0' && $rut <> '0-0')
            {
                $resp['estado']     = false;
                $resp['mensaje']    = 'Rut no válido';
                $resp['data']       = '';
                echo json_encode($resp);
                die();
            }else{
                
                    $rut_aux            = explode("-",$rut);
                    $rut_sin_dv         = $rut_aux[0];
                    $rut_sin_dv         = str_replace(".", "", $rut_sin_dv);
                    $dv                 = $rut_aux[1];
            }
        }else{
            $rut_sin_dv         = '';
            $dv                 = '';
        }
        
        $client             = new nusoap_client('http://177.221.141.191/publicWs/bhe_notarios/servidor.php?wsdl',true);
        $result             = $client->call('boleta_electronica', array(
                                'cod_notario'       => 'not_jkrimn',
                                'rutBHE'            =>  $rut_sin_dv, 
                                'dvRutBHE'          =>  $dv,
                                'glosaBHE'          =>  $glosa, 
                                'montoHonorario'    =>  $montoHonorario, 
                                'montoRetencion'    =>  $montoRetencion, 
                                'montoNeto'         =>  $montoNeto, 
                                'retieneEmisor'     =>  $retiene_emisor
                                ));
                                
        $codigoEstado       = CAJA::extraer_entre_cadenas($client->response,'<codigoEstado xsi:type="xsd:string">','</codigoEstado>');
        $numero_boleta      = CAJA::extraer_entre_cadenas($client->response,'<numeroBoleta xsi:type="xsd:string">','</numeroBoleta>');
        if(intval($codigoEstado) <> 0 || intval($numero_boleta) <= 0)
        {
            $resp['estado']     = false;
            $resp['mensaje']    = 'No fue posible generar la boleta error en el SII, codigo: '.$codigoEstado.' informar a soporte tecnico por favor <NAME_EMAIL>';
            $resp['data']       = '';
            echo json_encode($resp);
            die();
        }else{
            
            $update       = "UPDATE `cardex` SET 
                                    `retiene_emisor` = '".$retiene_emisor."'  
                                WHERE numero = '".$ot."';";
            $update_fin   = DB::query($update,'U');

            
            if(CAJA::extraer_entre_cadenas($client->response,'<retieneEmisor xsi:type="xsd:string">','</retieneEmisor>')=='S'){
                $impuestoCargo  = 0;
                $aPagar         = CAJA::extraer_entre_cadenas($client->response,'<totalHonorarios xsi:type="xsd:string">','</totalHonorarios>');
                $textoRetencion = 'El contribuyente emisor de esta boleta es el encargado de retener el porcentaje definido';                  
            }
            else{
                $impuestoCargo  = CAJA::extraer_entre_cadenas($client->response,'<impuestoHonorarios xsi:type="xsd:string">','</impuestoHonorarios>');
                $aPagar         = CAJA::extraer_entre_cadenas($client->response,'<liquidoHonorarios xsi:type="xsd:string">','</liquidoHonorarios>');
                $textoRetencion = 'El contribuyente para el cual está destinada está boleta es el encargado de retener el porcentaje definido';
            }

            $textoFechaBoleta       = CAJA::convertirFecha(CAJA::extraer_entre_cadenas($client->response,'<fechaBoleta xsi:type="xsd:string">','</fechaBoleta>'));  
            $telefono               = CAJA::extraer_entre_cadenas($client->response,'<telefonoEmisor xsi:type="xsd:string">','</telefonoEmisor>');
            $actividad_economica    = CAJA::extraer_entre_cadenas($client->response,'<actividadEconomica xsi:type="xsd:string">','</actividadEconomica>');
            if ($actividad_economica == "")
            {
                $actividad_economica = "SERVICIOS NOTARIALES";
            }
            $ddtt               = new datos;
            $nombre_conservador = $ddtt->conservador();
            $rut_cbr            = $ddtt->rut(); 
            $direccion          = $ddtt->direccion();
            $comuna             = $ddtt->comuna();
            $ciudad             = $ddtt->ciudad();
            $region             = $ddtt->region();
            if ($telefono == 0)
            {
                $telefono = $ddtt->telefono();
            }
            $array=array(
                '[codigoAlfa]'          => CAJA::extraer_entre_cadenas($client->response,'<codigoAlfa xsi:type="xsd:string">','</codigoAlfa>'),
                '[nombreReceptor]'      => CAJA::extraer_entre_cadenas($client->response,'<nombreReceptor xsi:type="xsd:string">','</nombreReceptor>'),
                '[numeroBoleta]'        => CAJA::extraer_entre_cadenas($client->response,'<numeroBoleta xsi:type="xsd:string">','</numeroBoleta>'),
                '[domicilioReceptor]'   => CAJA::extraer_entre_cadenas($client->response,'<domicilioReceptor xsi:type="xsd:string">','</domicilioReceptor>'),
                '[porcentajeImpuesto]'  => CAJA::extraer_entre_cadenas($client->response,'<porcentajeImpuesto xsi:type="xsd:string">','</porcentajeImpuesto>'),
                '[retieneEmisor]'       => CAJA::extraer_entre_cadenas($client->response,'<retieneEmisor xsi:type="xsd:string">','</retieneEmisor>'),
                '[fechaBoleta]'         => CAJA::extraer_entre_cadenas($client->response,'<fechaBoleta xsi:type="xsd:string">','</fechaBoleta>'),
                '[fechorEnv]'           => CAJA::extraer_entre_cadenas($client->response,'<fechorEnv xsi:type="xsd:string">','</fechorEnv>'),
                '[fechorGen]'           => CAJA::extraer_entre_cadenas($client->response,'<fechorGen xsi:type="xsd:string">','</fechorGen>'),
                '[rutReceptor]'         => CAJA::extraer_entre_cadenas($client->response,'<rutReceptor xsi:type="xsd:string">','</rutReceptor>'),
                '[dvReceptor]'          => CAJA::extraer_entre_cadenas($client->response,'<dvReceptor xsi:type="xsd:string">','</dvReceptor>'),
                '[totalHonorarios]'     => CAJA::extraer_entre_cadenas($client->response,'<totalHonorarios xsi:type="xsd:string">','</totalHonorarios>'),
                '[impuestoHonorarios]'  => $impuestoCargo,
                '[liquidoHonorarios]'   => CAJA::extraer_entre_cadenas($client->response,'<liquidoHonorarios xsi:type="xsd:string">','</liquidoHonorarios>'),
                '[descripcionLinea]'    => CAJA::extraer_entre_cadenas($client->response,'<descripcionLinea xsi:type="xsd:string">','</descripcionLinea>'),
                '[valorServicio]'       => CAJA::extraer_entre_cadenas($client->response,'<valorServicio xsi:type="xsd:string">','</valorServicio>'),
                '[aPagar]'              => $aPagar,
                '[textoRetencion]'      => $textoRetencion,
                '[textoFechaBoleta]'    => $textoFechaBoleta,
                '[nombreNotario]'       => $nombre_conservador,
                '[rutNotario]'          => $rut_cbr,
                '[direccionNotario]'    => CAJA::extraer_entre_cadenas($client->response,'<domicilioEmisor xsi:type="xsd:string">','</domicilioEmisor>'),
                '[telefonoNotario]'     => $telefono,
                '[giroNotario]'         => $actividad_economica,
                
                );

                $total_honorarios       = CAJA::extraer_entre_cadenas($client->response,'<totalHonorarios xsi:type="xsd:string">','</totalHonorarios>');
                $observaciones          = CAJA::extraer_entre_cadenas($client->response,'<descripcionLinea xsi:type="xsd:string">','</descripcionLinea>');
                $nombre_receptor        = CAJA::extraer_entre_cadenas($client->response,'<nombreReceptor xsi:type="xsd:string">','</nombreReceptor>');
                $rut_receptor           = CAJA::extraer_entre_cadenas($client->response,'<rutReceptor xsi:type="xsd:string">','</rutReceptor>');
                $dv_receptor            = CAJA::extraer_entre_cadenas($client->response,'<dvReceptor xsi:type="xsd:string">','</dvReceptor>');
                $array1                 = array_keys($array);
                $array1                 = array_map($$array,$array1);
                $array2                 = array_values($array);
                
                //Genero el pdf de la boleta
                ob_start();
                if(tipoImpresora=="NORMAL") {
                    $html2pdf   = new Html2Pdf('P', 'LETTER', 'es', true, 'UTF-8', array(10, 10, 10, 10));
                    $template   = file_get_contents(dirname('__FILE__').'/preview.htm');

                }
                if(tipoImpresora=="TERMICA") {
                    $width_in_mm    = 3.14961 * 25.4;
                    $height_in_mm   = 6 * 25.4;
                    $html2pdf       = new Html2Pdf('P', array($width_in_mm,$height_in_mm), 'es', true, 'UTF-8', array(0, 0, 0, 0));
                    $template       = file_get_contents(dirname('__FILE__').'/preview_termica.htm');
                }
                
                $template = str_replace($array1 , $array2 , $template);
                $content = ob_get_clean();
                try
                {
                    $html2pdf->pdf->SetDisplayMode('fullpage');
                    $html2pdf->writeHTML($template, isset($_GET['vuehtml']));
                }
                catch(Html2PdfException $e) {
                    $html2pdf->clean();
                    $formatter = new ExceptionFormatter($e);
                    file_put_contents("pdf_error.txt",$formatter->getHtmlMessage());
                    exit;
                }
                $nro_boleta     = CAJA::extraer_entre_cadenas($client->response,'<numeroBoleta xsi:type="xsd:string">','</numeroBoleta>');
                $nombre_archivo = "bhe_".$nro_boleta.".pdf";
                /* ob_clean();
                $html2pdf->Output($nombre_archivo, 'I'); */
                $pdf_out        = base64_encode($html2pdf->Output($nombre_archivo, "S"));
                $pdf_out_bin    = addslashes(base64_decode($pdf_out, "S"));
                $respuesta = array(
                    'showPDF'       => showPDF,
                    'Numero_boleta' => $nro_boleta,
                    'boleta_pdf'    => $pdf_out
                    );
                $mensaje = 'Se genero la boleta correctamente';
                $database_link = DB::connect();
                mysqli_begin_transaction($database_link);
                try {
                    $fechatablaPDF  = date("mY");
                    $r_irepert3     = "CREATE TABLE IF NOT EXISTS `cardex_boletas_BHE_SII_pdf_".$fechatablaPDF."` (
                        `id` bigint(11) NOT NULL AUTO_INCREMENT,
                        `id_boleta` int(20) NOT NULL,  
                        `id_OT` varchar(100) NOT NULL, 
                        `pdf` LONGBLOB NOT NULL,
                        PRIMARY KEY (`id`),
                        KEY `id_cardex_boleta` (`id_boleta`),
                        KEY `id_cardex_OT` (`id_OT`)
                        ) ENGINE=MyISAM  DEFAULT CHARSET=latin1 AUTO_INCREMENT=0 ;";
                    $result = mysqli_query($database_link, $r_irepert3);

                    $result2 = "INSERT INTO cardex_boletas_BHE_SII_pdf_".$fechatablaPDF." (`id_boleta`, `id_OT`, `pdf`) 
                    VALUES ('".$nro_boleta."','".$ot."','".$pdf_out_bin."')";
                    $result = mysqli_query($database_link, $result2);

                    //se guarda la boleta en sistema antiguo
                    $efectivo           = 0;
                    $documento          = 0;
                    $deposito           = 0;
                    $debito_credito     = 0;
                    switch (strtolower($tipo_pago)) {
                        case 'débito/crédito':
                            $debito_credito = $aPagar;
                            break;
                        case 'documento':
                            $documento      = $aPagar;
                            break;
                        case 'efectivo':
                            $efectivo       = $aPagar;
                            break;
                        case 'depósito':
                            $deposito       = $aPagar;
                            break;
                        case 'por pagar':
                            $tipo_pago      = $tipo_pago;
                            break;
                        default:
                            $efectivo       = $aPagar;
                            $tipo_pago      = 'efectivo';
                    }

                    /* $parametro      = $database_link->real_escape_string($observaciones ?? '');
                    $observaciones  = utf8_encode(addslashes($parametro)); */

                    $sql ="INSERT INTO cardex_boletas( boleta, caratula, total, fecha,retencion, 
                    efectivo, documento, deposito, debito, 
                    usuario , descripcion, 
                    fecha_documento, observaciones, es_electronica,tipo_pago,fecha_hora) 
                    VALUES
				    ('".$nro_boleta."','".$ot."','".$aPagar."','".DB::fechaHora('fecha')."','".$impuestoCargo."',  
                    '".$efectivo."','".$documento."','".$deposito."', '".$debito_credito."', 
                    '".$usuario_nombre."' , '".$observaciones."', 
                    '".DB::fechaHora('fecha')."', '".$observaciones."', 1, '".$tipo_pago."', '".DB::fechaHora('fechahora')."')";
                    $result = mysqli_query($database_link, $sql);

                    
                    $ch = curl_init(BASE_PATH.'app/ot/api/');

                    $arrayMateriaId = [
                        "accion"            => "ingresarBHE",
                        "ot"                => $ot,
                        "nc"                => $nc,
                        "rut"               => $rut,
                        "glosa"             => $glosa,
                        "tipo"              => "BHE",
                        "num_bhe"           => $nro_boleta,
                        "monto"             => $aPagar,
                        "tipo_pago"         => $tipo_pago,
                        "nombre_archivo"    => $nombre_archivo,
                        "pdf"               => $pdf_out
                    ];
                
                    curl_setopt_array($ch, array(
                        CURLOPT_POST => TRUE,
                        CURLOPT_RETURNTRANSFER => TRUE,
                        CURLOPT_HTTPHEADER => array(
                            'Authorization: Bearer '.$token,
                            'Content-Type: application/json'
                        ),
                        CURLOPT_POSTFIELDS => json_encode($arrayMateriaId)
                    ));

                    $response_bhe = json_decode(curl_exec($ch), true);

                    if($response_bhe['estado'])
                    {
                        mysqli_commit($database_link);
                    }else{
                        mysqli_rollback($database_link);
                        $mensaje .= ', pero hubo problemas para ingresarla a la base local 1'.$response_bhe['mensaje'];
                    }
                } catch (mysqli_sql_exception $exception) {
                    mysqli_rollback($database_link);
                    $mensaje .= ', pero hubo problemas para ingresarla a la base local 2';
                }
                
                 //guardo historial
                $descripcion        = 'Se genero la boleta : '.$nro_boleta.' para la nota de cobro : '.$nc.' para el rut receptor : '.$rut. ' por un total de : $'.number_format($aPagar, 0, '', '.');
                $opcion_historial   = 'CAJA';
                $seccion_historial  = 'CAJA';
                $historial          = HISTORIAL::ingresarHistorial($ot,$opcion_historial,$seccion_historial,$descripcion,$usuario);               
                
                
        }

        
        
        $resp['estado']     = true;
        $resp['mensaje']    = $mensaje;
        $resp['data']       = $respuesta;
        echo json_encode($resp);
    }

    public static function extraer_entre_cadenas($variable, $cadena1, $cadena2)
    {
        $cadena1        = !is_null($cadena1) ? $cadena1 : '';
        $cadena2        = !is_null($cadena2) ? $cadena2 : '';
        $variable       = !is_null($variable) ? $variable : '';
        $variable2      = explode($cadena1,$variable);//partimos en dos el código
        $script         = explode($cadena2,$variable2[1] ?? '');//partimos la segunda parte de lo que acabamos de hacer en dos nuevamente
        $script         = $script[0];//cogemos la primera parte, que es el script  
        return trim($script);
    }

    public static function rut_formato( $rut) {
        if(!empty($rut))
        {
          $rut = str_replace(".", "", $rut);
          $rut = str_replace("-", "", $rut);
          return number_format( substr ( $rut, 0 , -1 ) , 0, "", ".") . '-' . substr ( $rut, strlen($rut) -1 , 1 );
        }
    }

    public static function valida_rut($rut)
    {
    if(strlen($rut) >= 9)
    {
    
        $rut = preg_replace('/[^k0-9]/i', '', $rut);
        $dv  = substr($rut, -1);
        $numero = substr($rut, 0, strlen($rut)-1);
        $i = 2;
        $suma = 0;
        foreach(array_reverse(str_split($numero)) as $v)
        {
            if($i==8)
                $i = 2;

            $suma += $v * $i;
            ++$i;
        }

        $dvr = 11 - ($suma % 11);
        
        if($dvr == 11)
            $dvr = 0;
        if($dvr == 10)
            $dvr = 'K';

        if($dvr == strtoupper($dv))
            return true;
        else
            return false;
            
        }else{
        return false;
        }
    }

    public static function convertirFecha($fechaOriginal){
   
        $meses = array("Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre");
        $date = str_replace('/', '-', $fechaOriginal);
        $fecha= date('Y-m-d', strtotime($date));
       
        if (($timestamp = strtotime($fecha)) === false) {
         $fecha = DB::fechaHora('fecha');
        } 
    
        $dia = date('d', $timestamp);
        $mes = $meses[date('m', $timestamp)-1];
        $año = date('Y', $timestamp);
        return $dia.' de '.$mes.' del '.$año;    
    }

    public static function tiposPagos()
    {
        $resp            = array('estado' => True,'mensaje' =>'','data'=>'');
        $tipos           = array();
        $sql_cliente     = "SELECT * FROM tipos_de_pago ORDER BY id ASC";
        $row_cliente2    = DB::query($sql_cliente,'S');
        foreach($row_cliente2 as $row_cliente)
        {
            array_push($tipos, array(
                                        'tipo'              => $row_cliente['tipo'],
                                        'icon'              => $row_cliente['icon']
                        ));
        } 
        if(count($tipos) == 0)
        {
            $resp['estado']      = false;
            $resp['mensaje']     = 'No se encontraron tipos de pagos';
            $resp['data']        = '';
        }else{
            $resp['estado']   = true;
            $resp['mensaje']  = 'Se encontro tipos de pagos';
            $resp['data']     = $tipos;
        }
        echo json_encode($resp);
    }

    public static function reporteBHE($form,$usuario,$roles,$token)
    {
        $resp            = array('estado' => True,'mensaje' =>'','data'=>'');
        $boletas         = array();
        $fecha_i         = $form->fecha_i;
        $fecha_f         = $form->fecha_f;
        $num_bhe         = $form->num_bhe;
        $rut_receptor    = $form->rut_receptor;
        $usuario_search  = $form->usuario_search;
        $cliente         = $form->cliente;
        $anuladas        = $form->anuladas;
        $pendiente_pago  = $form->pendiente_pago;
        $ot              = $form->ot;
        $nc              = $form->nc;
        $si_va_pdf       = $form->pdf;
        $fecha_f_valida  = true;
        $fecha_i_valida  = true;
        $fecha_i_array   = explode("-",$fecha_i);
        $dia_i           = $fecha_i_array[0];
        $mes_i           = $fecha_i_array[1];
        $anho_i          = $fecha_i_array[2];
        $fecha_f_array   = explode("-",$fecha_f);
        $dia_f           = $fecha_f_array[0];
        $mes_f           = $fecha_f_array[1];
        $anho_f          = $fecha_f_array[2];
        if(!checkdate($mes_i,$dia_i,$anho_i) || $fecha_i == '')
        {
            $fecha_i_valida = false;
        }
        
        if(!checkdate($mes_f, $dia_f, $anho_f) || $fecha_f == '')
        {
            $fecha_f_valida = false;
        }

        if(!$fecha_i_valida || !$fecha_f_valida)
        {
            $resp['estado']      = false;
            $resp['mensaje']     = 'Fechas no válidas';
            $resp['data']        = '';
        }else{
            $fecha = date("Y-m-d", strtotime($fecha_i));
            $fecha2 = date("Y-m-d", strtotime($fecha_f));
            
            $sql_bhe = "SELECT
                cardex_boletas.`boleta` AS boleta,
                cardex_boletas.`caratula` AS caratula,
                cardex_boletas.`fecha` AS fecha,
                cardex_boletas.`fecha_hora` AS fecha_hora,
                cardex_boletas.`total` AS total,
                cardex_boletas.`retencion` AS retencion,
                cardex_boletas.`observaciones` AS observaciones,
                cardex_boletas.`usuario` AS usuario,
                cardex_boletas.`anulada` AS anulada,
                ot_nota_cobro.`cliente` AS rut_receptor,
                ot_nota_cobro.`forma_pago` AS forma_pago,
                ot_nota_cobro.`id` AS nc
                FROM
                    cardex_boletas
                INNER JOIN ot_nota_cobro ON cardex_boletas.caratula = ot_nota_cobro.OT
                AND cardex_boletas.boleta = ot_nota_cobro.bhe
                WHERE cardex_boletas.es_electronica = '1'
                AND cardex_boletas.fecha >= '" . $fecha . "'
                AND cardex_boletas.fecha <= '" . $fecha2 . "'
                AND cardex_boletas.boleta <> 0
                ORDER BY
                    cardex_boletas.boleta DESC ";
            $row_bhe    = DB::query($sql_bhe,'S');
            foreach($row_bhe as $row_b)
            {
                $fecha_anulacion    = null;
                $usuario_anulacion  = null;
                $sql_anulada        = "SELECT eliminado,eliminado_por FROM ot_nota_cobro_bhe WHERE id_nc = '".$row_b['nc']."' AND bhe = '".$row_b['boleta']."' ORDER BY id DESC LIMIT 1";
                $row_anulada        = DB::query($sql_anulada,'S');
                if(!is_null($row_anulada[0]['eliminado']) && !is_null($row_anulada[0]['eliminado_por']))
                {
                    $fecha_anulacion    = date("d-m-Y H:i:s",strtotime($row_anulada[0]['eliminado']));
                    if($row_anulada[0]['eliminado_por'] <> "")
                    {
                        $usuario_anulacion  = strtoupper(CAJA::traeUsuario($row_anulada[0]['eliminado_por']));
                    }
                    

                }
                //traigo nombre de cliente
                $nom_cliente  = null;
                if(!is_null($row_b['rut_receptor']) && $row_b['rut_receptor'] <> "")
                {
                    $cliente_name = CAJA::traeClienteInterna($row_b['rut_receptor'],'1');
                    if($cliente_name['estado'])
                    {
                        $nom_cliente = utf8_decode($cliente_name['data']['nombre']." ".$cliente_name['data']['apellido_paterno']." ".$cliente_name['data']['apellido_materno']);
                    }
                }
                array_push($boletas, array(
                            'nc'                => $row_b['nc'],
                            'boleta'            => $row_b['boleta'],
                            'ot'                => $row_b['caratula'],
                            'fecha'             => $row_b['fecha'],
                            'fecha_hora'        => $row_b['fecha_hora'],
                            'total'             => $row_b['total'],
                            'retencion'         => $row_b['retencion'],
                            'glosa'             => $row_b['observaciones'],
                            'usuario'           => $row_b['usuario'],
                            'anulada'           => $row_b['anulada'],
                            'rut_receptor'      => $row_b['rut_receptor'],
                            'nombre_cliente'    => $nom_cliente,
                            'forma_pago'        => $row_b['forma_pago'],
                            'fecha_anulacion'   => $fecha_anulacion,
                            'usuario_anulacion' => $usuario_anulacion
                            ));
            } 
        }
        if(count($boletas) == 0)
        {
            $resp['estado']      = false;
            $resp['mensaje']     = 'No se encontraron boletas emitidas';
            $resp['data']        = '';
        }else{
            if(!is_null($num_bhe) && is_numeric($num_bhe) && intval($num_bhe) > 0)
            {
                $boletas = array_filter($boletas, function($v, $k) use ($num_bhe){
                    return $v['boleta'] == $num_bhe;
                }, ARRAY_FILTER_USE_BOTH);
            }
           
            if(!is_null($rut_receptor) && $rut_receptor <> "")
            {
                $rut_formato = CAJA::rut_formato($rut_receptor);
                $rut_limpio  = str_replace(".","",$rut_receptor);
                $boletas = array_filter($boletas, function($v, $k) use ($rut_receptor,$rut_formato,$rut_limpio){
                    return $v['rut_receptor'] == $rut_receptor || $v['rut_receptor'] == $rut_formato || $v['rut_receptor'] == $rut_limpio;
                }, ARRAY_FILTER_USE_BOTH);
            }

            if(!is_null($usuario_search) && $usuario_search <> "")
            {
                $boletas = array_filter($boletas, function($v, $k) use ($usuario_search){
                    return strtoupper($v['usuario']) == strtoupper($usuario_search);
                }, ARRAY_FILTER_USE_BOTH);
            }

            if(!is_null($cliente) && $cliente <> "")
            {
                $boletas = array_filter($boletas, function($v, $k) use ($cliente){
                    return strtoupper($v['nombre_cliente']) == strtoupper($cliente);
                }, ARRAY_FILTER_USE_BOTH);
            }

            if($anuladas)
            {
                $boletas = array_filter($boletas, function($v, $k) {
                    return $v['anulada'] == 1;
                }, ARRAY_FILTER_USE_BOTH);
            }else{
                $boletas = array_filter($boletas, function($v, $k) {
                    return $v['anulada'] == 0;
                }, ARRAY_FILTER_USE_BOTH);
            }

            if($pendiente_pago)
            {
                $boletas = array_filter($boletas, function($v, $k) {
                    return $v['forma_pago'] == 'Por Pagar';
                }, ARRAY_FILTER_USE_BOTH);
            }/* else{
                $boletas = array_filter($boletas, function($v, $k) {
                    return $v['forma_pago'] <> 'Por Pagar';
                }, ARRAY_FILTER_USE_BOTH);
            } */

            if(!is_null($ot) && $ot <> "")
            {
                $boletas = array_filter($boletas, function($v, $k) use ($ot){
                    return $v['ot'] == $ot;
                }, ARRAY_FILTER_USE_BOTH);
            }

            if(!is_null($nc) && $nc <> "")
            {
                $boletas = array_filter($boletas, function($v, $k) use ($nc){
                    return $v['nc'] == $nc;
                }, ARRAY_FILTER_USE_BOTH);
            }

            if(count($boletas) == 0)
            {
                $resp['estado']      = false;
                $resp['mensaje']     = 'No se encontraron boletas emitidas';
                $resp['data']        = '';
            }else{
                $reporte_pdf = '';
                if($si_va_pdf)
                {
                    $reporte_pdf            = CAJA::crearReportePDF($fecha, $fecha2, $boletas, $anuladas);
                }
                
                $resp['estado']         = true;
                $resp['mensaje']        = 'Se encontraron boletas emitidas';
                $resp['data']           = $boletas;
                $resp['pdf_reporte']    = $reporte_pdf;
            }
        }
        echo json_encode($resp);
    }

    public static function traeClienteInterna($rut,$es_chileno)
    {
        $resp           = array('estado' => True,'mensaje' =>'','data'=>'');
        $otes           = array();
        $sql_ep         = '';

        //busqueda por RUT
        if(trim($rut) <> "")
        {
        //agregar si es chileno
        if(intval($es_chileno) == 1)
        {
            $rut_c2       = CAJA::rut_formato($rut);
            $rut_c_valido = CAJA::valida_rut($rut_c2);
            if($rut_c_valido)
            {
                $rut_c_parcial  = str_replace(".", "", $rut);
                $rut_c_parcial  = str_replace("-", "", $rut_c_parcial);
            }
        }else{
            $rut_c          = trim($rut);
            $rut_c_parcial  = trim($rut);
            $rut_c2         = trim($rut);
        }
        
        $sql_ep         = "SELECT * FROM clientes_not WHERE (rut = '".$rut_c."' OR rut = '".$rut_c_parcial."' OR rut = '".$rut_c2."') ORDER BY id DESC LIMIT 1";
        
        }
        // se busca en escrituras publicas
            if($sql_ep <> "")
            {
            $row            = DB::query($sql_ep,'S');
            if($row[0]['id']){
                $otes['rut']               = $row[0]['rut'];
                $otes['nombre']            = DB::selectmysql($row[0]['nombre']);
                $otes['apellido_paterno']  = DB::selectmysql($row[0]['apellido_paterno']);
                $otes['apellido_materno']  = DB::selectmysql($row[0]['apellido_materno']);
                $otes['id']                = $row[0]['id'];                 
            }
            }
            
        if(count($otes) == 0)
                {
                    $resp['estado']      = false;
                    $resp['mensaje']     = 'No se encontraron OT';
                    $resp['data']        = '';
                }else{
                    $resp['mensaje']     = 'Datos devueltos';
                    $resp['data']        = $otes;
                }
        return $resp;
    }

    public static function ListUsuarios()
    {
        $resp            = array('estado' => True,'mensaje' =>'','data'=>'');
        $tipos           = array();
        $sql_cliente     = "SELECT * FROM usuarios_not ORDER BY nombreSocial ASC";
        $row_cliente2    = DB::query($sql_cliente,'S');
        foreach($row_cliente2 as $row_cliente)
        {
            if($row_cliente['nombreSocial'] <> "")
            {
                array_push($tipos, array(
                    'nombre' => $row_cliente['nombreSocial']
                ));
            }
            
        } 
        if(count($tipos) == 0)
        {
            $resp['estado']      = false;
            $resp['mensaje']     = 'No se encontraron usuarios';
            $resp['data']        = '';
        }else{
            $resp['estado']   = true;
            $resp['mensaje']  = 'Se encontraron usuarios';
            $resp['data']     = $tipos;
        }
        echo json_encode($resp);
    }

    public static function ListClientes()
    {
        $resp            = array('estado' => True,'mensaje' =>'','data'=>'');
        $tipos           = array();
        $sql_cliente     = "SELECT * FROM clientes_not ORDER BY nombre ASC";
        $row_cliente2    = DB::query($sql_cliente,'S');
        foreach($row_cliente2 as $row_cliente)
        {
            if($row_cliente['nombre'] <> "")
            {
                array_push($tipos, array(
                    'nombre' => $row_cliente['nombre']." ".$row_cliente['apellido_paterno']." ".$row_cliente['apellido_materno']
                ));
            }
            
        } 
        if(count($tipos) == 0)
        {
            $resp['estado']      = false;
            $resp['mensaje']     = 'No se encontraron usuarios';
            $resp['data']        = '';
        }else{
            $resp['estado']   = true;
            $resp['mensaje']  = 'Se encontraron usuarios';
            $resp['data']     = $tipos;
        }
        echo json_encode($resp);
    }

    public static function anularBHE($boleta,$usuario,$roles)
    {
        $resp            = array('estado' => True,'mensaje' =>'','data'=>'');
        $tipos           = array();
        if(!is_numeric($boleta) || intval($boleta) <= 0)
        {
            
            $resp['estado']      = false;
            $resp['mensaje']     = 'Numero de boleta no válido';
            $resp['data']        = '';

        }else{
           
            $sql_bhe      = "SELECT id,caratula,anulada FROM cardex_boletas WHERE boleta = '".$boleta."' ORDER BY id DESC LIMIT 1";
            $row_bhe      = DB::query($sql_bhe,'S');
            if(empty($row_bhe[0]['id']))
            {
                $resp['estado']   = false;
                $resp['mensaje']  = 'Boleta no existe';
                $resp['data']     = $tipos;
                echo json_encode($resp);
                die();
            }
            if($row_bhe[0]['anulada'] == 1)
            {
                $resp['estado']   = false;
                $resp['mensaje']  = 'La boleta ya esta anulada';
                $resp['data']     = $tipos;
                echo json_encode($resp);
                die();
            }
            if(empty($row_bhe[0]['caratula']))
            {
                $resp['estado']   = false;
                $resp['mensaje']  = 'OT no válida';
                $resp['data']     = $tipos;
                echo json_encode($resp);
                die();
            }
            $ot           = $row_bhe[0]['caratula'];
            $update       = "UPDATE `cardex_boletas` SET 
                                    `anulada` = 1  
                                WHERE boleta = '".$boleta."';";
            $update_fin   = DB::query($update,'U');

            $update2      = "UPDATE `cardex` SET 
                                    `boleta` = 0  
                                WHERE numero = '".$ot."';";
            $update_fin2  = DB::query($update2,'U');

            $sql_bhe      = "SELECT id_nc FROM ot_nota_cobro_bhe WHERE bhe = '".$boleta."' AND eliminado IS NULL AND eliminado_por IS NULL ORDER BY id DESC LIMIT 1";
            $row_bhe      = DB::query($sql_bhe,'S');

            if(empty($row_bhe[0]['id_nc']))
            {
                $resp['estado']   = false;
                $resp['mensaje']  = 'No existe nota de cobro';
                $resp['data']     = $tipos;
                echo json_encode($resp);
                die();
            }

            $nc        = $row_bhe[0]['id_nc'];

            $update_dp =" UPDATE ot_nota_cobro SET 
                                total_pago  = null,
                                cliente     = null,
                                bhe         = null,
                                glosa       = null,
                                forma_pago  = null
                            WHERE id = '".$nc."'";
            $fin_re    = DB::query($update_dp,'U');

            $update_dp =" UPDATE ot_nota_cobro_bhe SET 
                                eliminado       = '".DB::fechaHora('fechahora')."',
                                eliminado_por   = '".$usuario."'
                            WHERE id_nc = '".$nc."' AND bhe = '".$boleta."'";
            $fin_re    = DB::query($update_dp,'U');

            $update_dp =" UPDATE ot_nota_cobro_detalle SET 
                                total_pago      = null
                            WHERE id_nc = '".$nc."'";
            $fin_re    = DB::query($update_dp,'U');

            $resp['estado']   = true;
            $resp['mensaje']  = 'Boleta anulada correctamente';
            $resp['data']     = $tipos;

            //guardo historial
            $descripcion        = 'Se anulo la boleta : '.$boleta.' para la nota de cobro : '.$nc;
            $opcion_historial   = 'CAJA';
            $seccion_historial  = 'CAJA';
            $historial          = HISTORIAL::ingresarHistorial($ot,$opcion_historial,$seccion_historial,$descripcion,$usuario);

        }  
        echo json_encode($resp);
    }

    public static function crearReportePDF($fecha, $fecha2, $data, $anuladas){
        $total_bhe_emitidas = count($data);
        $reporte = '<table>
						<tbody>
							<tr>
								<td>
								<img  src="imagenes/publicidad.jpg" alt="" width="75" height="55">
								</td>
								<td>';
								
								
								if ($anuladas)
								{
									$reporte.='<p><h2>Informe BHE Anuladas</h2>desde '.$fecha.' hasta '.$fecha2.'</p>';
								}
								else
								{
									$reporte.='
									<p><h2>Informe BHE Emitidas</h2>desde '.$fecha.' hasta '.$fecha2.'</p>';
								}
								
								
								$reporte.='
                                    <p>Total de BHE emitididas : '.$total_bhe_emitidas.'</p>
								</td>
							</tr>
							<tr>
								<td></td>
								<td></td>
							</tr>
						</tbody>
					</table>';
                    //                                <th style="padding: 5px 5px;border: 0px solid black;width:50px;text-align:center;">Otros Impuestos</th>
	    $reporte .= '<table style="margin: 0px 0px;">
                        <thead>
                        <tr style="background-color: #C9C9C9;color: #000000;text-align: left;font-size:10px;">
                                <th style="padding: 5px 5px;border: 0px solid black;width:40px;text-align:center;">Nro. BHE</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:40px;text-align:center;">Nro. NC</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:40px;text-align:center;">Nro. OT</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:60px;text-align:left;">Fecha/Hora</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:90px;text-align:left;">Glosa BHE</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:70px;text-align:center;">Honorarios</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:60px;text-align:center;">Ret. 2&ordm; Cat.</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:70px;text-align:center;">Total</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:60px;text-align:center;">Usuario</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:60px;text-align:center;">Cliente</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:70px;text-align:center;">RUT receptor</th>
                                <th style="padding: 5px 5px;border: 0px solid black;width:70px;text-align:center;">Forma pago</th>
                         </tr>
                        </thead>
                        <tbody>';
        $total_registros    = 0;
        $suma_impuestos     = 0;
        $suma_total         = 0;
        $suma_retencion     = 0;
        $suma_honorarios    = 0;

        foreach($data as $subArray){
            //ver si esta anulada para no sumarla al total!
                $total_registros    = $total_registros + 1;
                $total_parcial      = $subArray["retencion"] + $subArray["total"];
                $suma_impuestos     = $suma_impuestos + $subArray["impuesto_letra"];
                $suma_total         = $suma_total + $subArray["total"];
                $suma_retencion     = $suma_retencion + $subArray["retencion"];
                $honorarios         = $subArray["retencion"] + $subArray["total"];
                $suma_honorarios    = $suma_honorarios + $honorarios;
                
                //                               <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["impuesto_letra"] . '</td>
                $reporte .= ' <tr border="1">
                                <td style="border-bottom: thin solid #dddddd;border: thin solid #dddddd;text-align:center;">' . $subArray["boleta"] . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;font-size: 8px;text-align:center;">' . $subArray["nc"] . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;font-size: 8px;text-align:center;">' . $subArray["ot"] . '</td>';
                                if ($anuladas)
								{
									$reporte.='<td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;font-size: 8px;text-align:center;">Fecha de emisión: ' . date("d-m-Y H:i:s", strtotime($subArray["fecha_hora"])) . '
                                    <br>Fecha de anulacion: '.$subArray["fecha_anulacion"].'<br>
                                    Usuario que anulo: '.$subArray["usuario_anulacion"].'</td>';
								}else{
                                    $reporte.='<td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;font-size: 8px;text-align:center;">' . date("d-m-Y H:i:s", strtotime($subArray["fecha_hora"])) . '</td>';
                                }
                $reporte .= '   <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["glosa"] . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["total"] . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["retencion"] . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$total_parcial . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["usuario"] . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["nombre_cliente"]. '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;font-size: 8px;text-align:center;">' . str_replace('.','', $subArray["rut_receptor"]) . '</td>
                                <td style="border-bottom: thin solid #dddddd;border: 0px solid #dddddd;text-align:left;font-size: 8px;text-align:center;">'.$subArray["forma_pago"]. '</td>
                            </tr>';
            }
            $reporte.='</tbody></table>';

            $reporte.='<hr style="border: thin solid #dddddd;">
                                    <table name="grillaBHEtotal" class="table table-striped table-bordered" width="50%">
                    
                                        <tr>
                                            <th style="padding: 5px 5px;border: 1px solid black;width:120px;text-align:center;">Total BHE</th>
                                            <th style="padding: 5px 5px;border: 1px solid black;width:190px;text-align:center;">Otros Impuestos Retenidos</th>
                                            <th style="padding: 5px 5px;border: 1px solid black;width:170px;text-align:center;">Total Honorarios</th>
                                            <th style="padding: 5px 5px;border: 1px solid black;width:190px;text-align:center;">Total Retencion Imp. 2º Cat.</th>
                                            <th style="padding: 5px 5px;border: 1px solid black;width:170px;text-align:center;">Honorarios + Retencion</th>
                                        </tr>
                    
                                        <tr>
                                            <th style="border-bottom: thin solid #dddddd;border: 1px solid #dddddd;text-align:center"><h3>'.number_format($total_registros,0,',','.').'</h3></th>
                                            <th style="border-bottom: thin solid #dddddd;border: 1px solid #dddddd;text-align:center"><h3> $ '.number_format($suma_impuestos,0,',','.').'</h3></th>
                                            
                                            <th style="border-bottom: thin solid #dddddd;border: 1px solid #dddddd;text-align:center"><h3> $ '.number_format($suma_total,0,',','.').'</h3></th>
                                            <th style="border-bottom: thin solid #dddddd;border: 1px solid #dddddd;text-align:center"><h3> $ '.number_format($suma_retencion,0,',','.').'</h3></th>
                    
                                            <th style="border-bottom: thin solid #dddddd;border: 1px solid #dddddd;text-align:center"><h3> $ '.number_format($suma_honorarios,0,',','.').'</h3></th>
                                        </tr>
                                    </table>';
            try {
                $html2pdf = new Html2Pdf('L', 'A4', 'es', true, 'UTF-8', array(10, 0, 10, 0));
                //$html2pdf->setDefaultFont("courier");
                $content = ob_get_clean();
                $html2pdf->pdf->SetDisplayMode('fullpage');
                $html2pdf->writeHTML($reporte, isset($_GET['vuehtml']));
            
                $nombre_archivo = "bhe_informe_".$fecha."_".$fecha2.".pdf";
                /* ob_clean();
                $html2pdf->Output($nombre_archivo, 'I'); */
                $pdf_out1 = $html2pdf->Output($nombre_archivo, "S");
                $pdf_out  = base64_encode($html2pdf->Output($nombre_archivo, "S"));
            
                $dir_rub = "../../../tmp/bhe_informe_".$fecha."_".$fecha2.".pdf";
                file_put_contents($dir_rub, $pdf_out1);
                return $pdf_out;
            } catch (HTML2PDF_exception $e) {
                return null;
                exit;
            }
    }

    public static function traeUsuario($id)
    {
      $resp       = null;
      if($id == '' || $id <= 0 || !is_numeric($id))
      {
          $resp       = null;
      }else{
        $sql_nom    = "SELECT id,nombre,nombreSocial FROM usuarios_not WHERE id = '".$id."' LIMIT 1";
        $row_nom    = DB::query($sql_nom,'S');
        if(empty($row_nom[0]['id']))
        {
          $resp       = null;
        }else{
          $resp       = $row_nom[0]['nombre'];
        }
      }
      return $resp;
    }
}

?>